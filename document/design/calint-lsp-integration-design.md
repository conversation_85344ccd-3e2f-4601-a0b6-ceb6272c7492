# CAPL LSP集成

## 概述

实现calint VS Code扩展的LSP集成，使得在VS Code中安装calint插件后，能够在下方的问题面板中显示CAPL文件的静态分析结果。

## 需要实现的功能

### 1. LSP诊断转换器 (DiagnosticConverter)
- **文件**: `src/main/java/com/polelink/calint/lsp/diagnostics/DiagnosticConverter.java`
- **功能**: 将calint的IssueObject转换为LSP标准的Diagnostic格式
- **特性**:
  - 支持所有严重级别映射 (ERROR→Error, WARNING→Warning, INFO→Information, STYLE→Hint)
  - 坐标转换 (1-based → 0-based)
  - 按文件URI分组诊断信息
  - 完整的错误处理和日志记录

### 2. LSP文档服务增强 (CaplTextDocumentService)
- **文件**: `src/main/java/com/polelink/calint/lsp/server/CaplTextDocumentService.java`
- **功能**: 集成真实的calint分析引擎
- **特性**:
  - 替换占位符诊断为真实分析结果
  - 自动创建LinterEngine依赖项
  - 临时文件处理和URI转换
  - 完整的错误处理机制

### 3. VS Code扩展客户端 (languageClient.ts)
- **文件**: `extensions/vscode-calint/src/languageClient.ts`
- **功能**: 完整的LSP客户端实现
- **特性**:
  - 支持远程WebSocket和本地stdio两种模式
  - 自动重连机制
  - 配置集成
  - 详细的错误处理和日志记录

### 4. 扩展主入口 (extension.ts)
- **文件**: `extensions/vscode-calint/src/extension.ts`
- **功能**: 自动启动LSP客户端
- **特性**:
  - 异步激活函数
  - 自动错误处理
  - 生命周期管理

## 技术细节

### LSP诊断映射
```java
// 严重级别映射
ERROR → DiagnosticSeverity.Error
WARNING → DiagnosticSeverity.Warning  
INFO → DiagnosticSeverity.Information
STYLE → DiagnosticSeverity.Hint

// 坐标转换 (calint 1-based → LSP 0-based)
lspLine = caplLine - 1
lspColumn = caplColumn - 1
```

### 依赖注入解决方案
```java
// LinterEngine依赖创建
LexerParser lexerParser = createDefaultLexerParser();
RuleEngine ruleEngine = createDefaultRuleEngine();
InputProcessor inputProcessor = createDefaultInputProcessor();
LinterEngine linterEngine = new LinterEngine(lexerParser, ruleEngine, inputProcessor);
```

### VS Code配置支持
```typescript
// 支持的配置选项
calint.server.mode: "remote" | "local"
calint.server.host: string (默认: "localhost")
calint.server.port: number (默认: 10718)
calint.linter.configPath: string
calint.linter.includePaths: string[]
```

## 测试验证

### 单元测试
- **文件**: `src/test/java/com/polelink/calint/lsp/integration/LspIntegrationTest.java`
- **覆盖范围**:
  - DiagnosticConverter功能测试
  - 严重级别转换测试
  - 文件分组测试
  - 服务实例化测试

### 编译验证
- Java项目编译成功: ✅
- TypeScript扩展编译成功: ✅
- 所有测试通过: ✅

## 文件结构

```
calint/
├── src/main/java/com/polelink/calint/lsp/
│   ├── diagnostics/
│   │   └── DiagnosticConverter.java          # 新增
│   └── server/
│       └── CaplTextDocumentService.java      # 增强
├── extensions/vscode-calint/src/
│   ├── extension.ts                          # 更新
│   ├── languageClient.ts                     # 增强
│   └── configuration.ts                      # 现有
└── test_sample.capl                          # 测试文件
```

## 使用流程

1. **用户操作**: 在VS Code中打开CAPL文件
2. **扩展激活**: VS Code自动激活calint扩展
3. **LSP连接**: 扩展启动LSP客户端连接到服务器
4. **文档分析**: 服务器使用calint引擎分析文件内容
5. **诊断转换**: DiagnosticConverter将分析结果转换为LSP格式
6. **问题显示**: VS Code在问题面板中显示诊断信息

## 下一步建议

1. **性能优化**: 考虑增量分析和缓存机制
2. **功能扩展**: 添加代码补全、跳转定义等LSP功能
3. **配置增强**: 支持更多calint配置选项
4. **错误处理**: 增强网络连接和文件处理的错误恢复
5. **用户体验**: 添加进度指示器和状态栏信息

## 总结

成功实现了完整的LSP集成解决方案，用户现在可以在VS Code中安装calint扩展后，在问题面板中看到CAPL文件的静态分析结果。整个实现遵循LSP标准，具有良好的扩展性和维护性。
