## 2.3 核心数据结构定义

### 2.3.1 AST 节点 (AstNode)

自定义抽象语法树 (AST) 是代码静态分析的核心数据结构。每个 `AstNode` 代表代码中的一个特定语法构造。为了保持一致性和可扩展性，所有具体的 AST 节点（例如代表变量声明、函数调用、if语句等的节点）都将实现一个共同的 `AstNode` 接口或继承自一个通用的基类。

一个通用的 `AstNode` 应该具备以下核心属性和方法：

1.  **节点类型 (`NodeType`)**:
    *   描述：表示 AST 节点的具体语法类型（例如：变量声明 `VARIABLE_DECLARATION`, 函数定义 `FUNCTION_DEFINITION`, 表达式 `EXPRESSION`, If语句 `IF_STATEMENT` 等）。
    *   实现：通常是一个枚举类型 (`enum NodeType`)。
    *   方法：`getNodeType(): NodeType`

2.  **源码位置 (`SourceLocation`)**:
    *   描述：精确记录该节点在原始源代码文件中的位置信息，对于错误报告和代码导航至关重要。
    *   `SourceLocation` 对象应包含：
        *   `filePath: String` (节点所在的源文件绝对路径)
        *   `startLine: int` (起始行号，从1开始)
        *   `startColumn: int` (起始列号，从0或1开始，需统一约定)
        *   `endLine: int` (结束行号)
        *   `endColumn: int` (结束列号)
        *   `getText(): String` (可选，一个便捷方法，返回该节点对应的原始源代码文本片段)
    *   方法：`getSourceLocation(): SourceLocation`

3.  **父节点 (`AstNode parent`)**:
    *   描述：指向当前节点的父节点。树的根节点的父节点为 `null`。
    *   方法：`getParent(): AstNode`
    *   方法：`setParent(parent: AstNode)` (通常在树构建时由构建器调用)

4.  **子节点列表 (`List<AstNode> children`)**:
    *   描述：一个有序列表，包含当前节点的所有直接子节点。叶节点的子节点列表为空。
    *   方法：`getChildren(): List<AstNode>`
    *   方法：`addChild(child: AstNode)` (通常在树构建时由构建器调用)

5.  **语义属性存储 (Attributes)**:
    *   描述：一个灵活的机制，用于存储在语义分析阶段计算出的、与此节点相关的附加信息。例如，表达式节点可以存储其推断出的数据类型，变量引用节点可以存储其指向的符号表条目等。
    *   实现：可以使用一个 `Map<String, Object>` 或专门的属性类。
    *   方法示例：
        *   `getAttribute(key: String): Object`
        *   `setAttribute(key: String, value: Object)`
        *   `hasAttribute(key: String): boolean`
        *   便捷方法如：`getResolvedType(): Type`, `setResolvedType(type: Type)` (内部可能使用通用的 attribute map)

6.  **访问者模式支持 (`accept(visitor: AstVisitor)`)**:
    *   描述：为了方便地遍历和操作 AST，通常会实现访问者设计模式。
    *   方法：`accept(visitor: AstVisitor): void` (或者返回一个泛型结果 `T accept(visitor: AstVisitor<T>)`)

**具体的 AST 节点类**:
除了上述通用接口/基类，每种具体的语法结构都会有其对应的节点类。例如：
*   `VariableDeclarationNode` 可能额外包含 `variableName: String` 和 `declaredType: TypeInfo`。
*   `FunctionCallNode` 可能额外包含 `functionName: String` 和 `arguments: List<AstNode>`。
*   `IfStatementNode` 可能额外包含 `condition: AstNode`, `thenBranch: AstNode`, 和 `elseBranch: AstNode` (可选)。

这样的设计提供了灵活性和一致性。语义分析器和规则检查器可以依赖通用的 `AstNode` 接口遍历树，并在需要时将节点转换为具体的类型以访问特定信息。

### 2.3.2 符号表条目 (SymbolTableEntry)

`SymbolTableEntry` 用于存储在符号表中关于一个特定标识符（符号）的详细信息。

一个 `SymbolTableEntry` 通常应包含以下信息：

1.  **符号名称 (`String name`)**:
    *   描述：标识符的名称，例如变量名 "count" 或函数名 "calculateSum"。
    *   方法：`getName(): String`

2.  **符号种类 (`SymbolKind`)**:
    *   描述：表示该符号代表什么类型的实体。例如：变量 (`VARIABLE`)、函数 (`FUNCTION`)、参数 (`PARAMETER`)、类型定义 (`TYPE_DEFINITION`，如 `struct`、`enum` 成员)、消息 (`MESSAGE`)、环境变量 (`ENVIRONMENT_VARIABLE`) 等。
    *   实现：通常是一个枚举类型 (`enum SymbolKind`)。
    *   方法：`getKind(): SymbolKind`

3.  **类型信息 (`TypeInfo type`)**:
    *   描述：符号的数据类型。对于变量，这是其声明的类型；对于函数，这是其返回类型。对于类型定义本身，这可能指向其详细结构。
    *   `TypeInfo` 本身可能是一个复杂的对象或接口，能够表示基本类型 (如 `int`, `long`, `char`, `string`)、数组类型、结构体类型、函数签名等。我们稍后可以详细定义 `TypeInfo`。
    *   方法：`getType(): TypeInfo`

4.  **声明位置 (`SourceLocation declaredAt`)**:
    *   描述：该符号在源代码中被声明的位置。这对于跳转到定义和某些类型的错误报告很有用。
    *   方法：`getDeclaredAt(): SourceLocation`

5.  **作用域 (`Scope scope`)**:
    *   描述：该符号所属的作用域。`Scope` 对象本身可以链接到父作用域，形成作用域链，用于解析符号引用。
    *   方法：`getScope(): Scope` (稍后我们也会定义 `Scope` 对象)

6.  **附加属性 (`Map<String, Object> attributes`)** (可选但推荐):
    *   描述：用于存储与特定符号种类相关的额外信息。
    *   示例：
        *   对于函数符号：参数列表 (`List<ParameterSymbolEntry>`)、是否为库函数。
        *   对于变量符号：是否为常量 (`isConst`)、是否已初始化 (`isInitialized`)。
        *   对于参数符号：其在参数列表中的位置/索引。
    *   方法：同 `AstNode` 中的 `getAttribute/setAttribute`。

**与 `AstNode` 的关系**:
通常，代表声明的 `AstNode` (例如 `VariableDeclarationNode`, `FunctionDefinitionNode`) 会在语义分析阶段被用来创建相应的 `SymbolTableEntry`，并将其存入当前作用域的符号表中。这些 `AstNode` 可能会有一个指向其对应 `SymbolTableEntry` 的引用，反之亦然，或者通过语义属性存储来关联。

### 2.3.3 问题对象 (IssueObject)

`IssueObject` 用于封装静态分析过程中检测到的单个问题或违规。报告生成模块将使用这些对象来生成用户可见的输出。

一个 `IssueObject` 通常应包含以下核心信息：

1.  **规则 ID (`String ruleId`)**:
    *   描述：触发此问题的规则的唯一标识符。例如 "CALINT-STYLE-001"。这有助于用户查找规则文档或配置特定规则。
    *   方法：`getRuleId(): String`

2.  **问题描述 (`String message`)**:
    *   描述：对检测到的问题的可读描述。应该清晰、简洁，并可能包含问题的具体细节（例如，“变量 'x' 未使用”或“不推荐使用函数 'old_function'，请使用 'new_function'”）。
    *   方法：`getMessage(): String`

3.  **严重级别 (`SeverityLevel severity`)**:
    *   描述：问题的严重程度。例如：错误 (`ERROR`)、警告 (`WARNING`)、信息 (`INFO`)、风格建议 (`STYLE`)。
    *   实现：通常是一个枚举类型 (`enum SeverityLevel`)。
    *   方法：`getSeverity(): SeverityLevel`

4.  **源码位置 (`SourceLocation location`)**:
    *   描述：问题在源代码中的确切位置（或最相关的单个点）。这对于用户定位和修复问题至关重要。
    *   使用之前定义的 `SourceLocation` 对象。
    *   方法：`getLocation(): SourceLocation`

5.  **源文件路径 (`String filePath`)**: (冗余但便捷)
    *   描述：问题所在源文件的绝对路径。虽然 `SourceLocation` 中也包含此信息，但为了方便直接访问和过滤，有时会将其作为顶层属性。
    *   方法：`getFilePath(): String` (可以简单地委托给 `location.getFilePath()`) 

6.  **可选：建议修复 (`String suggestion`)**:
    *   描述：一个可选的字符串，提供关于如何修复此问题的建议。
    *   方法：`getSuggestion(): String` (可能返回 `null` 或空字符串)

**创建和使用**:
`AnalysisEngine` 中的规则执行器在检测到违规时会创建 `IssueObject` 实例。这些对象会被收集起来，并最终传递给 `ReportGenerator` 进行格式化输出。

### 2.3.4 规则对象 (Rule)

`Rule` 接口（或抽象基类）定义了所有静态检查规则必须遵循的契约。每条具体的规则（例如，检查未使用的变量、检查命名约定等）都将实现此接口。

一个 `Rule` 对象/接口应具备以下核心特征：

1.  **规则元数据 (Metadata)**:
    *   `getId(): String`: 返回规则的唯一ID (例如 "CALINT-SEMANTIC-005")。这与 `IssueObject` 中的 `ruleId` 对应。
    *   `getName(): String`: 返回规则的简短、可读名称 (例如 "UnusedVariableCheck")。
    *   `getDescription(): String`: 提供对规则目的和所检查内容的详细描述。
    *   `getCategory(): RuleCategory`: 返回规则的分类（例如 `SEMANTIC`, `STYLE`, `PERFORMANCE`, `BEST_PRACTICE`）。这是一个枚举类型。
    *   `getDefaultSeverity(): SeverityLevel`: 返回此规则在未被用户覆盖配置时的默认严重级别。

2.  **配置 (Configuration)**:
    *   `configure(config: Map<String, Object>): void`: (可选) 如果规则支持特定参数配置（例如，某规则允许配置最大行长度），此方法用于传入配置。配置信息通常来源于用户的配置文件。
    *   规则实现应能处理其自身相关的配置项。

3.  **生命周期/初始化**:
    *   `initialize(context: RuleContext): void`: (可选) 在规则执行前调用，允许规则进行一些初始化设置，例如获取对 `IssueReporter` 或其他共享资源的引用。`RuleContext` 可以提供这些资源。

4.  **执行逻辑 (`check` 或 `apply` 方法)**:
    *   这是规则的核心。通常，规则会被设计为访问特定类型的 AST 节点。
    *   一种常见的设计是利用访问者模式 (Visitor Pattern)。规则可以是一个 `AstVisitor` 的实现，或者在 `AnalysisEngine` 遍历 AST 时，针对特定节点类型调用规则的相应检查方法。
    *   示例方法签名（根据具体实现策略调整）：
        *   `check(node: AstNode, context: AnalysisContext): void`: 如果规则需要对任意节点进行通用检查。`AnalysisContext` 可能包含符号表、当前文件信息、报告器等。
        *   或者，如果与访问者模式集成，可能会有多个 `visitNodeType(node: NodeTypeNode, context: AnalysisContext): void` 方法。

5.  **问题报告 (Issue Reporting)**:
    *   规则在发现问题时，需要一种机制来报告 `IssueObject`。这通常通过 `RuleContext` 或 `AnalysisContext` 中提供的 `IssueReporter` 服务来完成。
    *   `IssueReporter` 会有一个方法，例如 `reportIssue(issue: IssueObject)`。

**规则加载与执行流程**:
`AnalysisEngine` 会负责：
1.  发现和加载所有可用的 `Rule` 实现（可能通过反射、服务加载器或显式注册）。
2.  根据用户配置（`calint-config.yaml`）实例化并配置启用的规则。
3.  在遍历 AST 的过程中，将相关的 AST 节点和分析上下文传递给适用的规则进行检查。

### 2.3.5 类型信息 (TypeInfo)

`TypeInfo` 用于表示 CAPL 程序中变量、表达式、函数参数和返回值的类型。它是类型检查和语义分析的核心。

**1. 基础 `TypeInfo` 接口/抽象类**:

所有具体的类型信息类都将实现此接口或继承此抽象类。

*   **获取类型种类 (`getKind(): TypeKind`)**:
    *   描述：返回一个枚举值，标识该类型的基本种类，例如 `PRIMITIVE`, `ARRAY`, `STRUCT`, `ENUM`, `FUNCTION_SIGNATURE`, `CAPL_OBJECT` (如 `message`, `timer`), `VOID`, `ERROR_TYPE`。
    *   `TypeKind` 是一个枚举，定义了所有支持的类型种类。

*   **获取类型名称 (`getName(): String`)**:
    *   描述：返回类型的可读名称。例如 "int", "MyStruct", "char[20]", "message 0x100"。对于复杂或匿名类型，这可能是生成的描述性字符串。
    *   方法：`getName(): String`

*   **类型兼容性检查 (`isCompatibleWith(otherType: TypeInfo): boolean`)**:
    *   描述：判断当前类型是否与 `otherType` 兼容（例如，在赋值、参数传递、操作数比较等场景）。这是类型检查的核心逻辑。
    *   方法：`isCompatibleWith(otherType: TypeInfo): boolean`

*   **获取类型大小 (可选) (`getSizeInBytes(): int`)**:
    *   描述：返回该类型在内存中占用的字节数。对于某些类型（如不完整类型、某些动态类型），这可能不可用或不适用。
    *   方法：`getSizeInBytes(): int` (可能抛出异常或返回特殊值如 -1)

**2. 枚举 `TypeKind`**:

```
enum TypeKind {
    PRIMITIVE,      // 基本数据类型 (int, double, char, etc.)
    ARRAY,          // 数组类型
    STRUCT,         // 结构体类型
    ENUM,           // 枚举类型
    FUNCTION_SIGNATURE, // 函数签名/类型 (用于函数指针或回调)
    MESSAGE,        // CAPL 特有的 message 类型
    TIMER,          // CAPL 特有的 timer 类型
    // 可以根据需要添加其他 CAPL 特有的对象类型，例如 CAN_FRAME, LIN_FRAME, DIAG_REQUEST 等
    VOID,           // void 类型，通常用于函数返回值
    ERROR_TYPE,     // 表示类型解析或推断错误的特殊类型
    UNKNOWN_TYPE    // 表示类型尚未确定或无法确定
}
```

**3. 具体类型实现**:

*   **原始类型 (`PrimitiveTypeInfo` implements `TypeInfo`)**:
    *   额外属性：`PrimitiveKind primitiveKind` (枚举，如 `INT`, `LONG`, `DOUBLE`, `CHAR`, `BYTE`, `FLOAT`, `WORD`, `DWORD`, `QWORD`, `INT64`)。
    *   `getName()`: 返回如 "int", "double"。
    *   `isCompatibleWith()`: 实现基本类型间的隐式转换规则。

*   **数组类型 (`ArrayTypeInfo` implements `TypeInfo`)**:
    *   额外属性：
        *   `TypeInfo elementType`: 数组元素的类型（也是一个 `TypeInfo` 实例）。
        *   `int size` (可选): 数组的大小。如果大小在声明时未知或不固定（如 `char[]` 用于字符串），可能为特殊值（如 -1 或省略）。
    *   `getName()`: 返回如 "int[10]", "char[]"。
    *   `isCompatibleWith()`: 定义数组间的兼容性规则（例如，元素类型兼容，维度匹配等）。

*   **结构体类型 (`StructTypeInfo` implements `TypeInfo`)**:
    *   额外属性：
        *   `String structName`: 结构体的名称。
        *   `List<StructMember> members`: 结构体成员列表。每个 `StructMember` 包含 `String memberName` 和 `TypeInfo memberType`。
        *   (可选) 指向其在符号表中定义的 `SymbolTableEntry`。
    *   `getName()`: 返回结构体名称，如 "MyDataStruct"。
    *   `isCompatibleWith()`: 通常结构体类型需要名称完全匹配才兼容（名义类型系统）。

*   **枚举类型 (`EnumTypeInfo` implements `TypeInfo`)**:
    *   额外属性：
        *   `String enumName`: 枚举的名称。
        *   `List<EnumConstant> constants`: 枚举常量列表。每个 `EnumConstant` 包含 `String constantName` 和 `long value`。
        *   `TypeInfo underlyingType`: 枚举的底层整数类型（通常是 `int` 或 `long`）。
        *   (可选) 指向其在符号表中定义的 `SymbolTableEntry`。
    *   `getName()`: 返回枚举名称，如 "Colors"。
    *   `isCompatibleWith()`: 通常与底层整数类型兼容，或需要名称完全匹配。

*   **函数签名类型 (`FunctionSignatureTypeInfo` implements `TypeInfo`)**:
    *   描述：用于表示函数签名，这在处理函数指针或需要将函数作为参数传递的场景中非常重要。
    *   额外属性：
        *   `TypeInfo returnType`: 函数的返回类型。
        *   `List<TypeInfo> parameterTypes`: 函数的参数类型列表（有序）。
    *   `getName()`: 返回如 "int(int, double)" 或 "void(char[])"。
    *   `isCompatibleWith()`: 基于返回类型和参数类型序列的兼容性。

*   **CAPL 特殊对象类型 (例如 `MessageTypeInfo`, `TimerTypeInfo`)**:
    *   这些可以作为具有特定 `TypeKind` 的特殊类型处理。
    *   它们可能有特定的属性，例如 `MessageTypeInfo` 可能有 `messageIdRange` 或关联的 `SymbolTableEntry` (如果 `message` 被声明为符号)。
    *   `TimerTypeInfo` 可能只是一个不透明类型，或者关联一个 `SymbolTableEntry`。
    *   `getName()`: 返回如 "message 0x100-0x1FF", "timer MyTimer"。
    *   它们的兼容性规则将是特定的。

*   **Void 类型 (`VoidTypeInfo` implements `TypeInfo`)**:
    *   `TypeKind.VOID`。
    *   通常是一个单例。用于函数返回值，表示没有值。

*   **错误类型 (`ErrorTypeInfo` implements `TypeInfo`)**:
    *   `TypeKind.ERROR_TYPE`。
    *   通常是一个单例。用于在类型推断或解析失败时表示一个无效或未知的类型，以防止级联错误。任何操作涉及 `ErrorType` 通常也会导致 `ErrorType` 或被标记为类型错误。

*   **未知类型 (`UnknownTypeInfo` implements `TypeInfo`)**:
    *   `TypeKind.UNKNOWN_TYPE`。
    *   通常是一个单例。用于表示类型在分析的某个阶段尚未确定。

**使用场景**:
`TypeInfo` 的实例将存储在 `SymbolTableEntry` 中，用于表示符号的类型。在表达式求值、赋值检查、函数调用参数匹配等语义分析阶段，`AstNode` 对应的 `TypeInfo` 将被计算或检索，并用于执行类型检查。

### 2.3.6 作用域 (Scope)

`Scope` 对象用于表示程序中的一个词法作用域，例如全局作用域、文件作用域、函数作用域、块作用域（如 `if`, `for`, `while` 语句块）或结构体/枚举定义内部的作用域。作用域管理符号的可见性和生命周期。

一个 `Scope` 对象通常应具备以下核心特征：

1.  **父作用域引用 (`Scope parentScope`)**:
    *   描述：指向包含当前作用域的父作用域。全局作用域的 `parentScope` 为 `null`。这形成了作用域的层次结构（作用域树）。
    *   方法：`getParentScope(): Scope`

2.  **子作用域列表 (`List<Scope> childrenScopes`)**: (可选，主要用于调试或特定分析)
    *   描述：当前作用域内直接嵌套的子作用域列表。
    *   方法：`getChildrenScopes(): List<Scope>` (或 `addChildScope(scope: Scope)`)

3.  **符号表 (`Map<String, SymbolTableEntry> symbols`)**:
    *   描述：存储在该作用域内直接声明的符号。键是符号名称 (字符串)，值是对应的 `SymbolTableEntry`。
    *   这个表只包含当前作用域的符号，不包含父作用域的。

4.  **作用域类型 (`ScopeKind scopeKind`)**: (可选，但推荐)
    *   描述：一个枚举，标识作用域的类型，例如 `GLOBAL`, `FILE`, `FUNCTION`, `BLOCK`, `STRUCT_DEFINITION`, `ENUM_DEFINITION`。
    *   这有助于调试和某些特定分析逻辑。
    *   `enum ScopeKind { GLOBAL, FILE, FUNCTION, BLOCK, CLASS_LIKE (for struct/message defs), ENUM_DEFINITION }`

5.  **定义符号 (`define(symbolEntry: SymbolTableEntry): boolean` 或 `define(name: String, entry: SymbolTableEntry)` )**:
    *   描述：在当前作用域中定义一个新符号。如果符号已存在于*当前*作用域，则可能报告重定义错误。
    *   方法：`define(entry: SymbolTableEntry): void` (可能抛出 `SymbolRedefinitionException`)

6.  **解析/查找符号 (`resolve(name: String): SymbolTableEntry`)**:
    *   描述：根据名称查找符号。查找首先在当前作用域的符号表中进行。如果未找到，则委托给父作用域进行查找，沿着作用域链向上直到全局作用域。如果最终未找到，则返回 `null` 或抛出 `SymbolNotFoundException`。
    *   方法：`resolve(name: String): SymbolTableEntry`

7.  **本地解析/查找符号 (`resolveLocal(name: String): SymbolTableEntry`)**:
    *   描述：仅在当前作用域的符号表中查找符号，不查找父作用域。
    *   方法：`resolveLocal(name: String): SymbolTableEntry`

8.  **关联的 AST 节点 (`AstNode definingNode`)**: (可选)
    *   描述：指向创建此作用域的 AST 节点（例如，函数定义的 `AstNode` 创建函数作用域，代码块的 `AstNode` 创建块作用域）。
    *   方法：`getDefiningNode(): AstNode`

**作用域的创建与管理**:
`SemanticAnalyzer` (或其子组件如 `SymbolTableManager`) 在遍历 AST 时负责创建和维护作用域栈。
*   当进入一个新的作用域定义结构（如函数体、代码块）时，会创建一个新的 `Scope` 对象，将其 `parentScope` 设置为当前作用域，然后将新作用域压入作用域栈，使其成为新的当前作用域。
*   当离开一个作用域定义结构时，当前作用域会从栈中弹出，恢复其父作用域为当前作用域。
*   符号声明（如变量声明、函数声明）会被添加到当前作用域的符号表中。

### 2.3.7 核心枚举类型 (Core Enumerations)

本节定义了在核心数据结构中广泛使用的关键枚举类型。

**1. `NodeType` (用于 `AstNode`)**

*   描述：标识 AST 节点的具体类型，代表 CAPL 语言中的各种语法构造。
*   初步建议值 (后续会根据 `CAPL.g4` 和分析需求调整)：
    *   `PROGRAM_UNIT`: 代表整个编译单元或文件。
    *   `INCLUDE_DIRECTIVE`: `#include` 指令。
    *   `PRAGMA_LIBRARY_DIRECTIVE`: `#pragma library` 指令。
    *   `VARIABLE_DECLARATION_BLOCK`: `variables { ... }` 代码块。
    *   `VARIABLE_DECLARATION`: 单个变量或数组的声明。
    *   `FUNCTION_DEFINITION`: 函数的完整定义。
    *   `PARAMETER_DECLARATION`: 函数参数的声明。
    *   `TYPEDEF_DEFINITION`: `typedef` 定义。
    *   `STRUCT_DEFINITION`: `struct` 定义。
    *   `ENUM_DEFINITION`: `enum` 定义。
    *   `MESSAGE_DEFINITION`: `message` 定义。
    *   `TIMER_DECLARATION`: `timer` 声明。
    *   `NETWORK_NODE_DEFINITION`: `msleep`, `node` 等网络节点定义。
    *   `EVENT_PROCEDURE`: 事件过程，如 `on timer`, `on key`, `on message`。
    *   `BLOCK_STATEMENT`: 由 `{}` 包围的代码块。
    *   `IF_STATEMENT`: `if-else if-else` 语句。
    *   `SWITCH_STATEMENT`: `switch` 语句。
    *   `CASE_CLAUSE`: `case` 分支。
    *   `DEFAULT_CLAUSE`: `default` 分支。
    *   `FOR_STATEMENT`: `for` 循环。
    *   `WHILE_STATEMENT`: `while` 循环。
    *   `DO_WHILE_STATEMENT`: `do-while` 循环。
    *   `REPEAT_UNTIL_STATEMENT`: (如果 CAPL 支持) `repeat-until` 循环。
    *   `BREAK_STATEMENT`: `break` 语句。
    *   `CONTINUE_STATEMENT`: `continue` 语句。
    *   `RETURN_STATEMENT`: `return` 语句。
    *   `ASSIGNMENT_EXPRESSION`: 赋值表达式 (`=`, `+=`, `-=`, `*=`, `/=`, `%=`, `&=`, `|=`, `^=`, `<<=`, `>>=`)。
    *   `BINARY_EXPRESSION`: 二元运算表达式 (如 `+`, `-`, `*`, `/`, `%`, `<`, `>`, `==`, `!=`, `&&`, `||`, `&`, `|`, `^`, `<<`, `>>`)。
    *   `UNARY_EXPRESSION`: 一元运算表达式 (如 `-` (负号), `!` (逻辑非), `~` (按位非), `++` (前/后置), `--` (前/后置), `&` (取地址), `*` (解引用))。
    *   `POSTFIX_EXPRESSION`: 后缀表达式 (如 `arr[idx]`, `obj.member`, `func_call()`, `ptr->member`)。
    *   `CONDITIONAL_EXPRESSION`: 条件表达式 (三元运算符 `?:`)。
    *   `FUNCTION_CALL_EXPRESSION`: 函数调用。
    *   `ARRAY_ACCESS_EXPRESSION`: 数组元素访问。
    *   `MEMBER_ACCESS_EXPRESSION`: 结构体/对象成员访问 (`.` 或 `->`)。
    *   `IDENTIFIER_REFERENCE`: 标识符引用 (用作值)。
    *   `INTEGER_LITERAL`: 整型字面量。
    *   `FLOAT_LITERAL`: 浮点型字面量。
    *   `CHAR_LITERAL`: 字符字面量。
    *   `STRING_LITERAL`: 字符串字面量。
    *   `ENUM_CONSTANT_REFERENCE`: 枚举常量引用。
    *   `SIZEOF_EXPRESSION`: `sizeof` 表达式。
    *   `TYPE_CAST_EXPRESSION`: 类型转换表达式。
    *   `SELECTOR`: CANoe 特有的选择器 (如 `this.byte(0)`).
    *   `EMPTY_STATEMENT`: 空语句 (`;`)。

**2. `SymbolKind` (用于 `SymbolTableEntry`)**

*   描述：标识符号表中条目的种类。
*   建议值：
    *   `VARIABLE`: 普通变量。
    *   `ARRAY_VARIABLE`: 数组变量。
    *   `FUNCTION`: 函数声明或定义。
    *   `PARAMETER`: 函数参数。
    *   `STRUCT_DEFINITION`: 结构体类型的定义。
    *   `STRUCT_MEMBER`: 结构体成员。
    *   `ENUM_DEFINITION`: 枚举类型的定义。
    *   `ENUM_CONSTANT`: 枚举常量。
    *   `MESSAGE_DEFINITION`: CAPL `message` 定义。
    *   `TIMER_DEFINITION`: CAPL `timer` 定义。
    *   `NETWORK_NODE`: CAPL 网络节点 (如 `node SomeNode;` 或 `msleep`)。
    *   `TYPEDEF_NAME`: 通过 `typedef` 定义的类型别名。
    *   `LABEL`: (如果支持 goto) 标签。
    *   `NAMESPACE_ALIAS`: (如果支持) 命名空间别名。

**3. `SeverityLevel` (用于 `IssueObject` 和 `Rule`)**

*   描述：标识问题或规则的严重程度。
*   建议值：
    *   `ERROR`: 严重的错误，可能导致编译失败或运行时异常。
    *   `WARNING`: 潜在问题，不好的实践，可能影响可读性、可维护性或存在风险。
    *   `INFO`: 代码改进建议，不直接影响功能。
    *   `STYLE`: 代码风格相关的建议。

**4. `RuleCategory` (用于 `Rule`)**

*   描述：对规则进行分类。
*   建议值：
    *   `SYNTAX`: 语法相关的基本检查 (虽然大部分由解析器完成，但可能有 AST 级别的语法健全性检查)。
    *   `SEMANTIC`: 语义错误，如类型不匹配、未声明的符号、作用域问题等。
    *   `STYLE_CONVENTION`: 代码风格和命名约定。
    *   `BEST_PRACTICE`: 编码最佳实践，避免潜在陷阱。
    *   `PERFORMANCE`: 可能导致性能问题的代码模式。
    *   `COMPLEXITY`: 代码复杂度相关的检查。
    *   `REDUNDANCY`: 代码冗余，如未使用的变量/函数。
    *   `PORTABILITY`: 可能影响代码在不同环境或 CAPL 版本间移植性的问题。
    *   `CAPL_SPECIFIC`: 针对 CAPL 特定特性或常见错误的规则。

**5. `TypeKind` (用于 `TypeInfo`)**

*   描述：标识类型信息的基本种类。
*   值 (已在 2.3.5 中定义)：
    *   `PRIMITIVE`
    *   `ARRAY`
    *   `STRUCT`
    *   `ENUM`
    *   `FUNCTION_SIGNATURE`
    *   `MESSAGE`
    *   `TIMER`
    *   `VOID`
    *   `ERROR_TYPE`
    *   `UNKNOWN_TYPE`
    *   (可以补充 `CAPL_OBJECT` 来涵盖其他特定对象如 `CAN_FRAME`, `DIAG_REQUEST` 等，或者为它们创建更具体的 `TypeKind`)

**6. `PrimitiveKind` (用于 `PrimitiveTypeInfo`)**

*   描述：标识具体的原始数据类型。
*   建议值 (需根据 CAPL 实际支持的类型确认)：
    *   `INT`
    *   `LONG`
    *   `DOUBLE`
    *   `CHAR`
    *   `BYTE`
    *   `WORD` (通常 16-bit unsigned)
    *   `DWORD` (通常 32-bit unsigned)
    *   `INT64` (或 `LONGLONG`)
    *   `UINT` (unsigned int)
    *   `UINT64` (unsigned int64)
    *   `FLOAT`
    *   `BOOL` (如果 CAPL 有独立的布尔类型，否则通常用 `int`)

**7. `ScopeKind` (用于 `Scope`)**

*   描述：标识作用域的类型。
*   值 (已在 2.3.6 中定义，可微调)：
    *   `GLOBAL`: 全局作用域。
    *   `FILE`: 文件作用域 (如果 CAPL 的文件边界有明确的独立作用域概念，通常 C/C++ 中是这样)。
    *   `FUNCTION_PROTOTYPE`: 函数原型声明中的参数作用域。
    *   `FUNCTION_BODY`: 函数定义体作用域。
    *   `BLOCK`: 通用块作用域 (如 `if`/`for`/`while` 内部或裸 `{}` 块)。
    *   `STRUCT_DEFINITION`: `struct` 定义内部的作用域 (用于成员)。
    *   `ENUM_DEFINITION`: `enum` 定义内部的作用域 (用于枚举常量)。
    *   `MESSAGE_DEFINITION`: `message` 定义内部的作用域 (如果其成员有自己的局部命名空间)。
    *   `SWITCH_BLOCK`: `switch` 语句的整个块作用域。


### 2.3.8 复合数据结构 (Composite Data Structures)

本节定义了在 linter 核心组件之间传递的关键复合数据结构。

**1. `SourceFile`**

*   **描述**: 代表一个已加载的 CAPL 源文件及其元数据。由 `InputProcessor` 生成。
*   **主要字段/方法 (Java - 概念性)**:
    ```java
    package com.polelink.calint.input; // 或 com.polelink.calint.engine;

    import java.nio.charset.Charset;
    import java.nio.file.Path;

    public class SourceFile {
        private final Path absolutePath;    // 文件的绝对路径
        private final String content;       // 文件的文本内容
        private final Charset detectedCharset; // 检测到的文件字符编码

        public SourceFile(Path absolutePath, String content, Charset detectedCharset) {
            this.absolutePath = absolutePath;
            this.content = content;
            this.detectedCharset = detectedCharset;
        }

        public Path getAbsolutePath() {
            return absolutePath;
        }

        public String getContent() {
            return content;
        }

        public Charset getDetectedCharset() {
            return detectedCharset;
        }

        // (推荐) 实现 equals() 和 hashCode() 以便在集合中使用
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SourceFile that = (SourceFile) o;
            return absolutePath.equals(that.absolutePath);
        }

        @Override
        public int hashCode() {
            return absolutePath.hashCode();
        }

        @Override
        public String toString() {
            return "SourceFile{path=" + absolutePath + ", charset=" + detectedCharset + "}";
        }
    }
    ```
*   **用途**: 作为 `InputProcessor` 的输出，并作为 `LexerParser` 的输入。


**2. `ParsingResult`**

*   **描述**: 封装了词法分析和语法分析阶段的结果。由 `LexerParser` 生成。
* **主要字段/方法 (Java - 概念性)**:
  ```java
  package com.polelink.calint.parser; // 或 com.polelink.calint.engine;

  import com.polelink.calint.input.SourceFile;
  import com.polelink.calint.issue.IssueObject;
  import org.antlr.v4.runtime.tree.ParseTree; // ANTLR ParseTree

  import java.util.Collections;
  import java.util.List;
  import java.util.Map;

  public class ParsingResult {
      // 将每个源文件映射到其对应的 ANTLR ParseTree
      private final Map<SourceFile, ParseTree> parseTrees;
      // 在词法分析或语法分析过程中检测到的所有问题
      private final List<IssueObject> issues;

      public ParsingResult(Map<SourceFile, ParseTree> parseTrees, List<IssueObject> issues) {
          this.parseTrees = Collections.unmodifiableMap(parseTrees != null ? parseTrees : Collections.emptyMap());
          this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
      }

      public Map<SourceFile, ParseTree> getParseTrees() {
          return parseTrees;
      }

      public List<IssueObject> getIssues() {
          return issues;
      }

      public boolean hasIssues() {
          return !issues.isEmpty();
      }

      // (可选) 辅助方法，用于获取特定文件的ParseTree
      public ParseTree getParseTreeFor(SourceFile sourceFile) {
          return parseTrees.get(sourceFile);
      }
  }
  ```
*   **用途**: 作为 `LexerParser` 的输出，并作为 `AstBuilder` 的主要输入。其中包含的 `IssueObject` 列表将与其他阶段产生的 `IssueObject` 合并，最终由 `ReportGenerator` 处理。
*   **依赖**:
    *   `SourceFile`: 见上文。
    *   `IssueObject`: 见 2.3.3 节。
    *   `org.antlr.v4.runtime.tree.ParseTree`: ANTLR 运行时库提供的解析树接口。


**3. `AstConstructionResult`**

*   **描述**: 封装了将 ANTLR ParseTree 转换为自定义 AST (Abstract Syntax Tree) 的结果。由 `AstBuilder` 生成。
* **主要字段/方法 (Java - 概念性)**:
  ```java
  package com.polelink.calint.ast; // 或 com.polelink.calint.engine;

  import com.polelink.calint.input.SourceFile;
  import com.polelink.calint.issue.IssueObject;
  // AstNode 是我们自定义的 AST 节点基类/接口，定义在 2.3.1 节
  // import com.polelink.calint.ast.AstNode; 

  import java.util.Collections;
  import java.util.List;
  import java.util.Map;

  public class AstConstructionResult {
      // 将每个源文件映射到其对应的自定义 AST 的根节点
      private final Map<SourceFile, AstNode> abstractSyntaxTrees;
      // 在 AST 构建过程中检测到的所有问题
      private final List<IssueObject> issues;

      public AstConstructionResult(Map<SourceFile, AstNode> abstractSyntaxTrees, List<IssueObject> issues) {
          this.abstractSyntaxTrees = Collections.unmodifiableMap(abstractSyntaxTrees != null ? abstractSyntaxTrees : Collections.emptyMap());
          this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
      }

      public Map<SourceFile, AstNode> getAbstractSyntaxTrees() {
          return abstractSyntaxTrees;
      }

      public List<IssueObject> getIssues() {
          return issues;
      }

      public boolean hasIssues() {
          return !issues.isEmpty();
      }

      // (可选) 辅助方法，用于获取特定文件的 AST 根节点
      public AstNode getAstRootFor(SourceFile sourceFile) {
          return abstractSyntaxTrees.get(sourceFile);
      }
  }
  ```
*   **用途**: 作为 `AstBuilder` 的输出，并作为 `SemanticAnalysisEngine` 的主要输入。其中包含的 `IssueObject` 列表将与其他阶段产生的 `IssueObject` 合并。
*   **依赖**:
    *   `SourceFile`: 见上文。
    *   `AstNode`: 定义在 2.3.1 节，代表自定义 AST 中的节点。
    *   `IssueObject`: 见 2.3.3 节。


**4. `SemanticAnalysisResult`**

*   **描述**: 封装了语义分析阶段的结果，包括符号解析、类型检查以及已配置规则的执行。由 `SemanticAnalysisEngine` 生成。
* **主要字段/方法 (Java - 概念性)**:
  ```java
  package com.polelink.calint.semantic; // 或 com.polelink.calint.engine;

  import com.polelink.calint.ast.AstNode; // 通常 AST 在此阶段会被遍历和注解，但可能不直接作为此结果的主要新产出
  import com.polelink.calint.input.SourceFile;
  import com.polelink.calint.issue.IssueObject;
  // SymbolTable 和 ProjectIndex 等可能作为分析的上下文或副产品，
  // 但此结果对象主要聚焦于新发现的问题。

  import java.util.Collections;
  import java.util.List;
  import java.util.Map;

  public class SemanticAnalysisResult {
      // 在语义分析、类型检查和规则执行过程中检测到的所有问题
      private final List<IssueObject> issues;
      // (可选) 可能包含对分析过的 AST 的引用，特别是如果它们在此阶段被修改或注解
      // private final Map<SourceFile, AstNode> analyzedSyntaxTrees;

      // 简单版本，只包含问题
      public SemanticAnalysisResult(List<IssueObject> issues) {
          this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
          // this.analyzedSyntaxTrees = Collections.unmodifiableMap(analyzedSyntaxTrees != null ? analyzedSyntaxTrees : Collections.emptyMap());
      }

      // public SemanticAnalysisResult(Map<SourceFile, AstNode> analyzedSyntaxTrees, List<IssueObject> issues) {
      //     this.analyzedSyntaxTrees = Collections.unmodifiableMap(analyzedSyntaxTrees != null ? analyzedSyntaxTrees : Collections.emptyMap());
      //     this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
      // }

      public List<IssueObject> getIssues() {
          return issues;
      }

      // public Map<SourceFile, AstNode> getAnalyzedSyntaxTrees() {
      //     return analyzedSyntaxTrees;
      // }

      public boolean hasIssues() {
          return !issues.isEmpty();
      }
  }
  ```
*   **用途**: 作为 `SemanticAnalysisEngine` 的输出。其包含的 `IssueObject` 列表将与之前阶段产生的 `IssueObject` 合并，最终由 `ReportGenerator` 处理。
*   **依赖**:
    *   `IssueObject`: 见 2.3.3 节。
    *   `AstNode` (间接): 语义分析引擎操作的对象。
    *   `SourceFile` (间接): 问题报告可能需要关联到源文件。


**5. `Configuration`**

*   **描述**: 代表整个 `calint` 工具的配置信息。它是一个数据持有对象 (DTO)，由 `ConfigurationManager` 加载和提供。
* **主要字段/方法 (Java - 概念性)**:
  ```java
  package com.polelink.calint.config; // 或 com.polelink.calint.engine;

  import com.polelink.calint.report.ReportFormat; // 已在 core-component-interfaces.md 的 ReportGenerator 中提及
  import com.polelink.calint.issue.SeverityLevel; // 已在 core-data-structures.md 的 2.3.7 中提及

  import java.nio.charset.Charset;
  import java.nio.file.Path;
  import java.util.List;
  import java.util.Map;
  import java.util.Optional;
  import java.util.Set;

  // 使用 record (Java 16+) 或普通类来定义
  // @Record (示意)
  public class Configuration {
      // 输入处理相关配置
      private final List<String> inputPaths; // 命令行传入的原始输入路径
      private final Set<String> excludedPaths; // 要排除的文件或目录模式
      private final Charset defaultInputCharset; // 默认输入文件编码

      // 分析与规则相关配置
      private final Set<String> enabledRuleIds; // 启用的规则ID集合
      private final Set<String> disabledRuleIds; // 禁用的规则ID集合 (优先级高于全局启用)
      private final Map<String, Map<String, Object>> ruleParameters; // RuleId -> (ParamName -> ParamValue)
      private final SeverityLevel defaultSeverityThreshold; // 报告的最低严重级别
      private final Map<String, SeverityLevel> ruleSeverityOverrides; // RuleId -> SeverityLevel (覆盖规则的默认严重性)
      private final List<String> projectIncludePaths; // 用于 #include 解析的额外包含路径

      // 报告相关配置
      private final ReportFormat reportFormat; // 输出报告的格式
      private final Optional<Path> reportOutputPath; // 报告输出文件的路径 (如果适用)
      private final boolean failOnSeverity; // 如果达到或超过此严重级别，则工具以非零状态码退出

      // 示例构造函数 (如果不是 record)
      public Configuration(List<String> inputPaths, Set<String> excludedPaths, Charset defaultInputCharset,
                           Set<String> enabledRuleIds, Set<String> disabledRuleIds,
                           Map<String, Map<String, Object>> ruleParameters,
                           SeverityLevel defaultSeverityThreshold,
                           Map<String, SeverityLevel> ruleSeverityOverrides,
                           List<String> projectIncludePaths,
                           ReportFormat reportFormat, Optional<Path> reportOutputPath,
                           boolean failOnSeverity) {
          this.inputPaths = inputPaths;
          this.excludedPaths = excludedPaths;
          this.defaultInputCharset = defaultInputCharset;
          this.enabledRuleIds = enabledRuleIds;
          this.disabledRuleIds = disabledRuleIds;
          this.ruleParameters = ruleParameters;
          this.defaultSeverityThreshold = defaultSeverityThreshold;
          this.ruleSeverityOverrides = ruleSeverityOverrides;
          this.projectIncludePaths = projectIncludePaths;
          this.reportFormat = reportFormat;
          this.reportOutputPath = reportOutputPath;
          this.failOnSeverity = failOnSeverity;
      }

      // Getter 方法...
      public List<String> getInputPaths() { return inputPaths; }
      public Set<String> getExcludedPaths() { return excludedPaths; }
      public Charset getDefaultInputCharset() { return defaultInputCharset; }
      public Set<String> getEnabledRuleIds() { return enabledRuleIds; }
      public Set<String> getDisabledRuleIds() { return disabledRuleIds; }
      public Map<String, Map<String, Object>> getRuleParameters() { return ruleParameters; }
      public SeverityLevel getDefaultSeverityThreshold() { return defaultSeverityThreshold; }
      public Map<String, SeverityLevel> getRuleSeverityOverrides() { return ruleSeverityOverrides; }
      public List<String> getProjectIncludePaths() { return projectIncludePaths; }
      public ReportFormat getReportFormat() { return reportFormat; }
      public Optional<Path> getReportOutputPath() { return reportOutputPath; }
      public boolean isFailOnSeverity() { return failOnSeverity; }

      // (可选) 辅助方法，例如检查特定规则是否启用并考虑覆盖
      public boolean isRuleEnabled(String ruleId) {
          if (disabledRuleIds.contains(ruleId)) {
              return false;
          }
          // 简化逻辑：假设 enabledRuleIds 为空表示启用所有 (除非显式禁用)，或需要更复杂的启用策略
          return enabledRuleIds.isEmpty() || enabledRuleIds.contains(ruleId);
      }

      public Optional<Object> getRuleParameter(String ruleId, String paramName) {
          return Optional.ofNullable(ruleParameters.get(ruleId))
                         .map(params -> params.get(paramName));
      }
      
      public SeverityLevel getEffectiveSeverityForRule(String ruleId, SeverityLevel defaultRuleSeverity) {
          return ruleSeverityOverrides.getOrDefault(ruleId, defaultRuleSeverity);
      }
  }
  ```
*   **用途**: `ConfigurationManager` 的输出，并被工具的几乎所有其他组件（如 `InputProcessor`, `LexerParser`, `SemanticAnalysisEngine`, `ReportGenerator`）使用，以控制其行为。
*   **依赖**:
    *   `ReportFormat`: 定义在 `ReportGenerator` 接口中 (或作为独立枚举)。
    *   `SeverityLevel`: 核心枚举，见 2.3.7 节。


**6. `ReportFormat` (枚举)**

*   **描述**: 指定分析报告的输出格式。在 `ReportGenerator` 和 `Configuration` 中使用。
*   **主要字段/方法 (Java - 概念性)**:
    ```java
    package com.polelink.calint.report; // 或 com.polelink.calint.engine;

    public enum ReportFormat {
        CONSOLE, // 直接输出到控制台，通常是人类可读的文本格式
        JSON,    // JSON 格式，便于机器解析和集成
        HTML,    // HTML 格式，提供丰富的 Web 页面报告
        XML,     // XML 格式 (可选，类似于 Checkstyle 或 PMD 的输出)
        SARIF    // Static Analysis Results Interchange Format (一种标准的静态分析结果格式)
        // 可以根据需要添加其他格式
    }
    ```
*   **用途**: 由 `Configuration` 对象持有，并由 `ReportGenerator` 用于确定如何格式化和输出分析结果。

**7. `ConfigurationException` (自定义异常)**

*   **描述**: 表示在配置加载、解析或验证过程中发生的错误。由 `ConfigurationManager` 抛出。
* **主要字段/方法 (Java - 概念性)**:
  ```java
  package com.polelink.calint.config; // 或 com.polelink.calint.engine;

  import com.polelink.calint.issue.IssueObject; // 用于携带详细的配置问题列表

  import java.util.Collections;
  import java.util.List;

  public class ConfigurationException extends Exception {
      private final List<IssueObject> issues; // (可选) 与配置错误相关的具体问题列表

      public ConfigurationException(String message) {
          super(message);
          this.issues = Collections.emptyList();
      }

      public ConfigurationException(String message, Throwable cause) {
          super(message, cause);
          this.issues = Collections.emptyList();
      }

      public ConfigurationException(String message, List<IssueObject> issues) {
          super(message);
          this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
      }

      public ConfigurationException(String message, Throwable cause, List<IssueObject> issues) {
          super(message, cause);
          this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());
      }

      public List<IssueObject> getIssues() {
          return issues;
      }
  }
  ```
*   **用途**: `ConfigurationManager` 在无法成功加载或处理配置时抛出此异常，通常会包含导致程序终止的信息。
*   **依赖**:
    *   `IssueObject`: 见 2.3.3 节。
