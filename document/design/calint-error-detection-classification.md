# CAPL Linter 错误检测分类与应用对应关系设计文档

**文档版本**: 1.0  
**创建日期**: 2025-01-XX  
**最后更新**: 2025-01-XX  
**作者**: CAPL Linter 开发团队

## 目录

1. [概述](#1-概述)
2. [错误检测架构](#2-错误检测架构)
3. [错误分类体系](#3-错误分类体系)
4. [应用层次对应关系](#4-应用层次对应关系)
5. [具体错误类型详解](#5-具体错误类型详解)
6. [错误处理流程](#6-错误处理流程)
7. [扩展性设计](#7-扩展性设计)

## 1. 概述

### 1.1 文档目的

本文档详细描述了CAPL Linter在构建Custom AST过程中能够检测的各类程序代码问题，以及这些问题与应用架构层次之间的对应关系。通过系统化的分类，为开发者提供清晰的错误检测能力概览和实现指导。

### 1.2 设计原则

- **分层检测**: 按照编译器前端的经典分层架构进行错误检测
- **渐进式分析**: 从语法错误到语义错误再到代码质量问题的渐进式分析
- **上下文感知**: 根据CAPL语言特性提供上下文相关的错误信息
- **可扩展性**: 支持新的错误类型和检测规则的动态添加

### 1.3 适用范围

- CAPL静态代码分析
- 语法和语义错误检测
- 代码质量和风格检查
- 开发工具集成（IDE、CI/CD）

## 2. 分层错误检测架构

### 2.1 分层架构概述

CAPL Linter采用经典的编译器前端分层架构，每一层都有特定的职责和错误检测能力。这种分层设计确保了错误检测的全面性、准确性和可扩展性。

```mermaid
graph TD
    A[源代码输入] --> B[第1层: 预处理阶段]
    B --> C[第2层: 词法分析阶段]
    C --> D[第3层: 语法分析阶段]
    D --> E[第4层: AST构建阶段]
    E --> F[第5层: 语义分析阶段]
    F --> G[第6层: 规则检查阶段]
    G --> H[统一错误报告]

    B --> B1[宏展开错误<br/>包含文件错误]
    C --> C1[词法错误<br/>Token识别错误]
    D --> D1[语法结构错误<br/>语法规则违规]
    E --> E1[AST构建错误<br/>节点创建失败]
    F --> F1[语义错误<br/>类型检查错误<br/>作用域错误]
    G --> G1[代码质量问题<br/>风格问题<br/>最佳实践违规]
```

### 2.2 应用组件对应关系

| 检测阶段 | 应用组件 | 主要职责 | 错误类型前缀 | 错误恢复策略 |
|---------|---------|---------|-------------|-------------|
| 第1层: 预处理 | `PreprocessingInputProcessor`<br/>`MacroExpander` | 宏展开、文件包含 | `CAPL-MACRO-*`<br/>`CAPL-INCLUDE-*` | 跳过错误宏，继续处理 |
| 第2层: 词法分析 | `CAPLLexer`<br/>`CaplAntlrErrorListener` | Token识别、字符验证 | `LEXER-*` | 跳过错误字符，继续tokenize |
| 第3层: 语法分析 | `CAPLParser`<br/>`CaplAntlrErrorListener` | 语法规则匹配、结构验证 | `PARSER-*` | 错误恢复规则，同步到下一语句 |
| 第4层: AST构建 | `AntlrToCustomAstVisitor`<br/>Helper类 | 自定义AST构建、节点验证 | `AST-*` | 创建错误占位符节点 |
| 第5层: 语义分析 | `SemanticAnalyzer`<br/>`SymbolTable`<br/>Semantic Helpers | 符号表、类型检查、作用域分析 | `SEMANTIC-*` | 记录错误，继续符号表构建 |
| 第6层: 规则检查 | `RuleEngine`<br/>各种Rule类 | 代码质量、风格、最佳实践检查 | `CALINT-*` | 记录问题，继续其他规则 |

### 2.3 LinterEngine的统一协调

`LinterEngine`作为核心协调器，统一管理各层的错误检测和处理：

```java
public class LinterEngine {
    public LinterResult analyze(List<SourceFile> sourceFiles, AppConfiguration configuration) {
        List<IssueObject> allIssues = new ArrayList<>();

        // 第1层：输入处理（包含预处理）
        InputProcessingResult inputResult = inputProcessor.processFiles(sourceFiles);
        allIssues.addAll(inputResult.getIssues());  // 预处理错误

        // 第2、3层：词法和语法分析
        ParsingResult parsingResult = lexerParser.parse(inputResult.getSourceFiles());
        allIssues.addAll(parsingResult.getIssues());  // 词法、语法错误

        // 第4层：AST构建
        AstConstructionResult astResult = astBuilder.buildAst(parsingResult);
        allIssues.addAll(astResult.getIssues());  // AST构建错误

        // 第5层：语义分析
        SemanticAnalysisResult semanticResult = semanticEngine.analyze(astResult, configuration);
        allIssues.addAll(semanticResult.getIssues());  // 语义错误

        // 第6层：规则检查
        List<IssueObject> ruleIssues = ruleEngine.analyze(astResult.getAbstractSyntaxTrees(), configuration);
        allIssues.addAll(ruleIssues);  // 代码质量问题

        return new LinterResult(allIssues);
    }
}
```

### 2.4 各层错误处理机制详解

#### 2.4.1 第1层：预处理阶段错误处理

**核心组件**：`PreprocessingInputProcessor`, `MacroExpander`

**错误处理机制**：
```java
public class PreprocessingInputProcessor {
    public InputProcessingResult processFiles(List<SourceFile> sourceFiles) {
        List<IssueObject> allIssues = new ArrayList<>();

        for (SourceFile file : sourceFiles) {
            PreprocessorResult result = preprocessor.process(file);

            if (!result.isSuccessful()) {
                // 直接返回原始的预处理issues，保持IssueObject格式
                return new InputProcessingResult(
                    Collections.emptyList(),
                    result.getIssues()  // 原始错误，不包装
                );
            }
        }

        return new InputProcessingResult(processedFiles, allIssues);
    }
}
```

**错误恢复策略**：跳过错误宏，使用原始文本继续处理

#### 2.4.2 第2层：词法分析阶段错误处理

**核心组件**：`CAPLLexer`, `CaplAntlrErrorListener`

**错误处理机制**：
```java
public class CaplAntlrErrorListener extends BaseErrorListener {
    @Override
    public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol,
                           int line, int charPositionInLine, String msg, RecognitionException e) {

        if (recognizer instanceof CAPLLexer) {
            if (e instanceof LexerNoViableAltException) {
                // 处理词法错误
                char badChar = (char) ((LexerNoViableAltException) e).getInputStream().LA(1);
                String improvedMsg = I18n.l("error.lexer.invalidChar", String.valueOf(badChar));
                ruleId = "LEXER-INVALID-CHAR";
            }
        }

        // 创建统一的IssueObject
        IssueObject issue = new IssueObject(ruleId, improvedMsg, SeverityLevel.ERROR, location);
        issues.add(issue);
    }
}
```

**错误恢复策略**：跳过错误字符，继续tokenize

#### 2.4.3 第3层：语法分析阶段错误处理

**核心组件**：`CAPLParser`, `CaplAntlrErrorListener`

**上下文感知错误处理**：
```java
// 在CaplAntlrErrorListener中处理语法错误
if (e instanceof NoViableAltException) {
    // 上下文感知的错误消息生成
    String contextSpecificMessage = analyzeContextForSpecificErrors(e, (Parser) recognizer);

    if (contextSpecificMessage != null) {
        improvedMsg = contextSpecificMessage;
        ruleId = "PARSER-CONTEXT-SPECIFIC";
    } else {
        // 回退到通用消息
        improvedMsg = I18n.l("error.parser.noViableAlt.extracted", extractedSnippet);
        ruleId = "PARSER-NO-VIABLE-ALT";
    }
}

private String analyzeContextForSpecificErrors(NoViableAltException e, Parser parser) {
    TokenStream tokens = parser.getTokenStream();
    int errorIndex = e.getOffendingToken().getTokenIndex();

    // 检查CAPL特定的错误模式
    if (isPrecedingToken(tokens, errorIndex, "message")) {
        return "message类型声明方式不对。正确语法：message <MessageType> <variableName>;";
    }

    if (isPrecedingToken(tokens, errorIndex, "timer")) {
        return "timer类型声明方式不对。正确语法：timer <variableName>; 或 msTimer <variableName>;";
    }

    if (isPrecedingToken(tokens, errorIndex, "signal")) {
        return "signal类型声明方式不对。正确语法：signal <SignalType> <variableName>;";
    }

    return null;
}
```

**错误恢复策略**：使用错误恢复规则，同步到下一个语句

#### 2.4.4 第4层：AST构建阶段错误处理

**核心组件**：`AntlrToCustomAstVisitor`, Helper类

**错误处理机制**：
```java
public class AntlrToCustomAstVisitor extends CAPLBaseVisitor<AstNode> {
    @Override
    public AstNode visitVariableDeclaration(CAPLParser.VariableDeclarationContext ctx) {
        try {
            // 使用Helper类构建AST节点
            return DeclarationHelper.visitVariableDeclaration(ctx, this);
        } catch (AstConstructionException e) {
            // AST构建错误处理
            IssueObject issue = new IssueObject(
                "AST-CONSTRUCTION-001",
                "AST节点构建失败: " + e.getMessage(),
                SeverityLevel.ERROR,
                location(ctx)
            );
            issues.add(issue);

            // 返回错误占位符节点，允许继续分析
            return createErrorPlaceholderNode(ctx);
        }
    }

    private AstNode createErrorPlaceholderNode(ParserRuleContext ctx) {
        // 创建占位符节点，保持AST结构完整性
        return new ErrorPlaceholderNode(location(ctx), ctx.getText());
    }
}
```

**错误恢复策略**：创建错误占位符节点，保持AST结构完整性

#### 2.4.5 第5层：语义分析阶段错误处理

**核心组件**：`SemanticAnalyzer`, `SymbolTable`, Semantic Helper类

**错误处理机制**：
```java
public class SemanticAnalyzer extends RuleBaseAstVisitor<Void, SymbolTable> {

    @Override
    public Void visitIdentifierReference(IdentifierReferenceNode node, SymbolTable symTab) {
        String name = node.getName();

        // 符号解析
        Optional<Symbol> symbol = symTab.resolve(name);
        if (!symbol.isPresent()) {
            // 报告未声明变量错误
            reportUndeclaredVariable(name, node.getSourceLocation());
        } else {
            // 类型检查
            performTypeChecking(node, symbol.get(), symTab);
        }

        return null;
    }

    private void reportUndeclaredVariable(String varName, SourceLocation location) {
        IssueObject issue = new IssueObject(
            "SEMANTIC-UNDECLARED-001",
            I18n.l("semantic.error.undeclared.variable", varName),
            SeverityLevel.ERROR,
            location,
            "请检查变量名拼写，或确保变量已在当前作用域中声明"
        );
        issues.add(issue);
    }

    private void performTypeChecking(IdentifierReferenceNode node, Symbol symbol, SymbolTable symTab) {
        // 类型兼容性检查
        if (node.getExpectedType() != null && !isTypeCompatible(symbol.getType(), node.getExpectedType())) {
            IssueObject issue = new IssueObject(
                "SEMANTIC-TYPE-MISMATCH-001",
                I18n.l("semantic.error.type.mismatch", symbol.getType(), node.getExpectedType()),
                SeverityLevel.ERROR,
                node.getSourceLocation()
            );
            issues.add(issue);
        }
    }
}
```

**错误恢复策略**：记录错误，继续符号表构建和类型检查

#### 2.4.6 第6层：规则检查阶段错误处理

**核心组件**：`RuleEngine`, 各种Rule类

**错误处理机制**：
```java
public class RuleEngine {
    public List<IssueObject> analyze(Map<SourceFile, AstNode> astTrees, AppConfiguration config) {
        List<IssueObject> allIssues = new ArrayList<>();

        for (Map.Entry<SourceFile, AstNode> entry : astTrees.entrySet()) {
            AstNode rootNode = entry.getValue();

            // 执行所有启用的规则
            for (Rule rule : getEnabledRules(config)) {
                try {
                    List<IssueObject> ruleIssues = rule.check(rootNode);
                    allIssues.addAll(ruleIssues);
                } catch (Exception e) {
                    // 规则执行错误处理
                    LOGGER.warn("规则 {} 执行失败: {}", rule.getRuleId(), e.getMessage());
                    // 继续执行其他规则
                }
            }
        }

        return allIssues;
    }
}

// 具体规则示例
public class NamingConventionRule implements Rule {
    @Override
    public List<IssueObject> check(AstNode node) {
        List<IssueObject> issues = new ArrayList<>();

        if (node instanceof VariableDeclarationNode) {
            String varName = ((VariableDeclarationNode) node).getName();
            if (!isValidVariableName(varName)) {
                issues.add(new IssueObject(
                    "CALINT-NAMING-VARIABLE-001",
                    "变量名不符合命名规范: " + varName,
                    SeverityLevel.WARNING,
                    node.getSourceLocation(),
                    "建议使用camelCase命名风格，如：myVariableName"
                ));
            }
        }

        // 递归检查子节点
        for (AstNode child : node.getChildren()) {
            issues.addAll(check(child));
        }

        return issues;
    }
}
```

**错误恢复策略**：记录问题，继续执行其他规则

### 2.5 统一错误报告系统

所有层次的错误最终都通过统一的`IssueObject`和报告系统处理：

```java
// 统一的错误对象
public class IssueObject {
    private String ruleId;           // 规则ID，标识错误来源层次
    private String message;          // 错误消息
    private SeverityLevel severity;  // 严重级别
    private SourceLocation location; // 源码位置
    private String suggestion;       // 修复建议
    private String codeContext;      // 代码上下文
}

// 统一的报告生成
public class ConsoleReportGenerator implements ReportGenerator {
    @Override
    public void generateReport(List<IssueObject> issues, ReportConfiguration config) {
        for (IssueObject issue : issues) {
            // 统一格式：文件名:行号:列号: 级别: 消息 [规则ID]
            String output = String.format("%s:%d:%d: %s: %s [%s]",
                issue.getLocation().getFilePath(),
                issue.getLocation().getStartLine(),
                issue.getLocation().getStartColumn(),
                issue.getSeverity().toString().toLowerCase(),
                issue.getMessage(),
                issue.getRuleId()
            );
            System.out.println(output);

            // 显示代码上下文和修复建议
            if (issue.getCodeContext() != null) {
                displayCodeContext(issue);
            }
            if (issue.getSuggestion() != null) {
                System.out.println("  建议: " + issue.getSuggestion());
            }
        }
    }
}
```

### 2.6 分层系统的优势

#### 2.6.1 职责分离
- **单一职责原则**：每一层专注于特定类型的错误检测
- **降低复杂度**：将复杂的错误检测任务分解为多个简单的子任务
- **提高可维护性**：每层的修改不会影响其他层的功能

#### 2.6.2 错误隔离
- **容错性**：某一层的错误不会阻止其他层的分析
- **全面性**：即使存在语法错误，仍能进行部分语义分析和规则检查
- **渐进式分析**：从基础错误到高级问题的渐进式检测

#### 2.6.3 可扩展性
- **插件化设计**：可以独立地在任何层添加新的错误检测规则
- **规则热插拔**：支持动态启用/禁用特定的检测规则
- **自定义扩展**：用户可以添加自定义的检测规则

#### 2.6.4 性能优化
- **按需分析**：可以根据需要跳过某些层的分析
- **并行处理**：不同文件可以并行进行分析
- **增量分析**：支持增量分析和结果缓存

### 2.7 实际应用示例

#### 2.7.1 完整的错误检测流程

以代码 `message i;` 为例，展示分层系统的完整处理过程：

**输入代码**：
```capl
variables {
    message i;  // 错误的message声明
}
```

**各层处理过程**：

1. **第1层：预处理阶段**
   - 检查宏：无宏使用
   - 检查包含文件：无包含文件
   - **结果**：无错误，继续下一层

2. **第2层：词法分析阶段**
   - Token识别：`variables`, `{`, `message`, `i`, `;`, `}`
   - **结果**：所有token识别成功，无词法错误

3. **第3层：语法分析阶段**
   - 语法匹配：检测到 `NoViableAltException`
   - 上下文分析：识别为message声明错误
   - **结果**：生成具体的错误消息

4. **第4层：AST构建阶段**
   - 尝试构建AST节点：失败
   - **结果**：创建错误占位符节点，继续分析

5. **第5层：语义分析阶段**
   - 符号表构建：跳过错误节点
   - **结果**：无额外语义错误

6. **第6层：规则检查阶段**
   - 代码风格检查：可能的相关规则检查
   - **结果**：可能的风格建议

**最终输出**：
```
test.can:2:5: error: message类型声明方式不对。正确语法：message <MessageType> <variableName>; [PARSER-MESSAGE-DECLARATION-001]
      2 |     message i;
        |     ^
  建议: 请指定具体的消息类型，例如：message EngineData engineData;
```

#### 2.7.2 多层错误的综合示例

**输入代码**：
```capl
#include "nonexistent.cin"  // 预处理错误

variables {
    message msg;            // 语法错误
    int BadVariableName;    // 风格问题
    int unusedVar;          // 未使用变量
}

on start {
    undeclaredVar = 5;      // 语义错误
    BadVariableName = 10;
}
```

**各层检测结果**：

1. **预处理错误**：
   ```
   test.can:1:1: error: 包含文件不存在: "nonexistent.cin" [CAPL-INCLUDE-001]
   ```

2. **语法错误**：
   ```
   test.can:4:5: error: message类型声明方式不对。正确语法：message <MessageType> <variableName>; [PARSER-MESSAGE-DECLARATION-001]
   ```

3. **语义错误**：
   ```
   test.can:9:5: error: 变量 'undeclaredVar' 未声明就使用 [SEMANTIC-UNDECLARED-001]
   ```

4. **代码质量问题**：
   ```
   test.can:5:9: warning: 变量名不符合命名规范: BadVariableName [CALINT-NAMING-VARIABLE-001]
   test.can:6:9: info: 变量 'unusedVar' 已声明但未使用 [CALINT-UNUSED-VARIABLE-001]
   ```

这个示例展示了分层系统如何在一次分析中检测出多种不同类型的问题，为开发者提供全面的代码质量反馈。

## 3. 错误分类体系

### 3.1 主要分类

```
CAPL代码问题分类
├── 1. 语法层面的问题（ANTLR阶段）
│   ├── 1.1 词法错误
│   │   ├── 非法字符：无法识别的字符或符号
│   │   └── 未识别的token序列：不符合CAPL词法规则的字符组合
│   └── 1.2 语法错误
│       ├── 语法结构错误：如缺少分号、括号不匹配、语句结构不完整
│       ├── 关键字使用错误：如 message i; 这种不完整的类型声明
│       └── 表达式语法错误：运算符使用不当、表达式结构错误
├── 2. 语义层面的问题（AST构建+语义分析阶段）
│   ├── 2.1 符号表相关问题
│   │   ├── 未声明变量使用：使用了未在当前作用域链中声明的变量
│   │   ├── 符号重定义：在同一作用域中重复定义相同名称的符号
│   │   └── 作用域违规：在错误的作用域中访问变量或函数
│   ├── 2.2 类型检查问题
│   │   ├── 类型不匹配：赋值、运算、函数调用中的类型不兼容
│   │   ├── 函数调用参数错误：参数数量或类型与函数定义不匹配
│   │   └── 无效赋值操作：对常量、函数名等不可变实体赋值
│   └── 2.3 声明和定义问题
│       ├── 变量声明语法错误：如不完整的message、timer、signal声明
│       ├── 函数定义问题：参数声明、返回类型等问题
│       └── 结构体/枚举定义问题：成员声明、类型定义等问题
├── 3. CAPL特定的问题
│   ├── 3.1 CAPL对象类型问题
│   │   ├── Message类型声明错误：message i; 应该是 message MessageType variableName;
│   │   ├── Timer类型声明错误：timer声明缺少必要的类型信息
│   │   └── Signal类型声明错误：signal声明语法不正确
│   ├── 3.2 事件处理问题
│   │   ├── 事件函数定义错误：on timer、on key、on message等事件处理函数的语法问题
│   │   └── 事件参数问题：事件处理函数的参数类型或数量错误
│   └── 3.3 网络和通信相关问题
│       ├── 网络节点定义错误：node、network相关定义的语法问题
│       └── 数据库对象引用错误：对CAN数据库对象的错误引用
├── 4. 代码质量和风格问题（规则引擎阶段）
│   ├── 4.1 命名规范问题
│   │   ├── 变量命名不规范：不符合预设的命名模式
│   │   ├── 函数命名不规范：函数名不符合约定
│   │   └── 常量命名问题：常量命名不符合规范
│   ├── 4.2 代码结构问题
│   │   ├── 未使用的变量/函数：声明但从未使用的实体
│   │   ├── 魔术数字：代码中直接使用的未命名数字常量
│   │   └── 函数复杂度过高：函数体过长或嵌套层级过深
│   └── 4.3 CAPL最佳实践问题
│       ├── 事件函数中的耗时操作：在事件处理函数中使用可能阻塞的操作
│       └── 资源管理问题：timer、message等资源的不当使用
└── 5. 预处理相关问题
    ├── 5.1 宏处理问题
    │   ├── 未知宏使用：使用了未定义的CAPL宏
    │   └── 宏语法错误：宏的使用语法不正确
    └── 5.2 包含文件问题
        ├── 包含文件路径错误：#include指令中的文件路径问题
        └── 循环包含：文件之间的循环依赖
```

### 3.2 错误检测的层次结构

```
1. 预处理阶段
   ├── 宏展开错误
   └── 包含文件处理错误

2. 词法分析阶段（ANTLR Lexer）
   ├── 非法字符
   └── 无法识别的token

3. 语法分析阶段（ANTLR Parser）
   ├── 语法结构错误
   ├── 缺少必要的语法元素
   └── 语法规则违规

4. AST构建阶段
   ├── 节点构建失败
   └── 语法树结构问题

5. 语义分析阶段
   ├── 符号表构建
   ├── 作用域分析
   ├── 类型检查
   └── 引用解析

6. 规则检查阶段
   ├── 代码风格检查
   ├── 最佳实践检查
   └── 质量度量
```

### 3.3 严重级别分类

| 级别 | 描述 | 影响 | 示例 |
|------|------|------|------|
| **ERROR** | 严重错误，阻止编译 | 代码无法编译或运行 | 语法错误、未声明变量 |
| **WARNING** | 潜在问题，不阻止编译 | 可能导致运行时问题 | 未使用变量、类型转换 |
| **INFO** | 改进建议 | 影响代码质量和可维护性 | 命名规范、代码风格 |

## 4. 应用层次对应关系

### 4.1 预处理阶段 (Preprocessing Stage)

**对应分类**: 5. 预处理相关问题
**应用组件**: `PreprocessingInputProcessor`, `MacroExpander`

**检测能力**:
- **5.1 宏处理问题**
  - 未知宏使用：验证宏是否在预定义列表中
  - 宏语法错误：检查宏的使用语法是否正确
- **5.2 包含文件问题**
  - 包含文件路径错误：验证#include指令中的文件路径
  - 循环包含：检测文件之间的循环依赖

**错误示例**:
```capl
// CAPL-MACRO-001: 未知宏使用
write("%UNKNOWN_MACRO%");

// CAPL-INCLUDE-001: 包含文件不存在
#include "nonexistent.cin"

// CAPL-INCLUDE-002: 循环包含
#include "file_a.cin"  // file_a.cin 也包含当前文件
```

### 4.2 词法分析阶段 (Lexical Analysis Stage)

**对应分类**: 1.1 词法错误
**应用组件**: `CAPLLexer`, `CaplAntlrErrorListener`

**检测能力**:
- **非法字符**：无法识别的字符或符号
- **未识别的token序列**：不符合CAPL词法规则的字符组合
- Token边界确定
- 字符编码问题
- 注释和字符串处理

**错误示例**:
```capl
// LEXER-INVALID-CHAR: 非法字符
int var@ = 5;  // '@' 不是合法的标识符字符

// LEXER-ERROR: 未闭合字符串
char* msg = "unclosed string

// LEXER-ERROR: 无法识别的token序列
int 123abc = 5;  // 标识符不能以数字开头
```

### 4.3 语法分析阶段 (Syntax Analysis Stage)

**对应分类**: 1.2 语法错误
**应用组件**: `CAPLParser`, `CaplAntlrErrorListener`

**检测能力**:
- **语法结构错误**：如缺少分号、括号不匹配、语句结构不完整
- **关键字使用错误**：如 `message i;` 这种不完整的类型声明
- **表达式语法错误**：运算符使用不当、表达式结构错误
- 语法规则匹配验证
- 语句结构完整性检查

**错误示例**:
```capl
// PARSER-NO-VIABLE-ALT: 关键字使用错误
message i;  // 应该是: message MessageType variableName;

// PARSER-INPUT-MISMATCH: 语法结构错误 - 缺少分号
int counter
}

// PARSER-NO-VIABLE-ALT: 表达式语法错误
int result = 5 + * 3;  // 运算符使用不当

// PARSER-INPUT-MISMATCH: 括号不匹配
if (condition {
    // 缺少右括号
}
```

### 4.4 AST构建阶段 (AST Construction Stage)

**对应分类**: 2.3 声明和定义问题（部分）
**应用组件**: `AntlrToCustomAstVisitor`, Helper类

**检测能力**:
- 节点类型验证
- AST结构完整性检查
- 语法树转换错误检测
- 节点属性验证
- 复杂语法结构的AST构建

**错误示例**:
```capl
// AST-CONSTRUCTION-001: 节点构建失败
// 复杂表达式或声明的AST构建问题

// AST-CONSTRUCTION-002: 节点属性验证失败
// 当语法正确但AST节点属性不完整时
```

### 4.5 语义分析阶段 (Semantic Analysis Stage)

**对应分类**: 2. 语义层面的问题 + 3. CAPL特定的问题（部分）
**应用组件**: `SemanticAnalyzer`, `SymbolTable`, Semantic Helpers

**检测能力**:
- **2.1 符号表相关问题**
  - 未声明变量使用：检查变量是否在当前作用域链中声明
  - 符号重定义：检测同一作用域中的重复定义
  - 作用域违规：验证变量和函数的作用域访问规则
- **2.2 类型检查问题**
  - 类型不匹配：验证赋值、运算、函数调用中的类型兼容性
  - 函数调用参数错误：检查参数数量和类型匹配
  - 无效赋值操作：检测对常量、函数名等不可变实体的赋值
- **2.3 声明和定义问题**
  - 变量声明语法错误：完整的message、timer、signal声明验证
  - 函数定义问题：参数声明、返回类型验证
  - 结构体/枚举定义问题：成员声明、类型定义验证
- **3.1 CAPL对象类型问题**
  - Message类型声明错误：验证message声明的完整性
  - Timer类型声明错误：检查timer声明的类型信息
  - Signal类型声明错误：验证signal声明语法

**错误示例**:
```capl
// SEMANTIC-UNDECLARED-001: 未声明变量使用
variables {
    int declared_var;
}

on start {
    undeclared_var = 5;  // 错误：未声明的变量
}

// SEMANTIC-TYPE-MISMATCH-001: 类型不匹配
variables {
    int number;
    char text[10];
}

on start {
    number = text;  // 错误：类型不兼容
}

// SEMANTIC-REDEFINITION-001: 符号重定义
variables {
    int counter;
    int counter;  // 错误：重复定义
}

// SEMANTIC-CAPL-MESSAGE-001: Message类型声明错误
variables {
    message msg;  // 错误：缺少消息类型
    // 正确：message EngineData engineMsg;
}
```

### 4.6 规则检查阶段 (Rule Checking Stage)

**对应分类**: 4. 代码质量和风格问题 + 3. CAPL特定的问题（部分）
**应用组件**: `RuleEngine`, 各种Rule类

**检测能力**:
- **4.1 命名规范问题**
  - 变量命名不规范：检查是否符合预设的命名模式
  - 函数命名不规范：验证函数名是否符合约定
  - 常量命名问题：检查常量命名规范
- **4.2 代码结构问题**
  - 未使用的变量/函数：检测声明但从未使用的实体
  - 魔术数字：识别代码中直接使用的未命名数字常量
  - 函数复杂度过高：检查函数体长度和嵌套层级
- **4.3 CAPL最佳实践问题**
  - 事件函数中的耗时操作：检测事件处理函数中的阻塞操作
  - 资源管理问题：检查timer、message等资源的使用
- **3.2 事件处理问题**
  - 事件函数定义错误：验证on timer、on key、on message等语法
  - 事件参数问题：检查事件处理函数的参数
- **3.3 网络和通信相关问题**
  - 网络节点定义错误：检查node、network相关定义
  - 数据库对象引用错误：验证CAN数据库对象引用

**错误示例**:
```capl
// CALINT-NAMING-001: 命名规范违规
variables {
    int BadVariableName;  // 应该使用camelCase
}

// CALINT-MAGIC-NUMBER-001: 魔术数字
on timer myTimer {
    setTimer(myTimer, 12345);  // 应该使用命名常量
}

// CALINT-UNUSED-VARIABLE-001: 未使用的变量
variables {
    int unusedVar;  // 声明但从未使用
}

// CALINT-EVENT-BLOCKING-001: 事件函数中的耗时操作
on message EngineData {
    sleep(1000);  // 错误：事件处理函数中不应有阻塞操作
}
```

## 5. 具体错误类型详解

### 5.1 语法层面的问题详解

#### 5.1.1 词法错误

**分类**: 1.1 词法错误
**检测阶段**: 词法分析阶段
**应用组件**: `CAPLLexer`, `CaplAntlrErrorListener`

**非法字符示例**:
```capl
// LEXER-INVALID-CHAR: 非法字符
int var@ = 5;        // '@' 不是合法的标识符字符
float price$ = 10.5; // '$' 不是合法字符
```

**未识别的token序列示例**:
```capl
// LEXER-ERROR: 标识符不能以数字开头
int 123abc = 5;

// LEXER-ERROR: 未闭合字符串
char* msg = "unclosed string

// LEXER-ERROR: 无效的数字格式
float value = 12.34.56;
```

#### 5.1.2 语法错误

**分类**: 1.2 语法错误
**检测阶段**: 语法分析阶段
**应用组件**: `CAPLParser`, `CaplAntlrErrorListener`

**语法结构错误示例**:
```capl
// PARSER-INPUT-MISMATCH: 缺少分号
int counter
}

// PARSER-NO-VIABLE-ALT: 括号不匹配
if (condition {
    writeln("missing closing parenthesis");
}

// PARSER-INPUT-MISMATCH: 缺少右大括号
variables {
    int var1;
    int var2;
// 缺少 }
```

**关键字使用错误示例**:
```capl
// PARSER-NO-VIABLE-ALT: 不完整的类型声明
variables {
    message msg;    // 错误：应该是 message MessageType variableName;
    timer;          // 错误：应该是 timer timerName;
    signal sig;     // 错误：应该是 signal SignalType variableName;
}
```

**表达式语法错误示例**:
```capl
// PARSER-NO-VIABLE-ALT: 运算符使用不当
int result = 5 + * 3;     // 连续运算符
int value = (5 + ) * 2;   // 不完整的表达式
int calc = 10 / / 2;      // 重复运算符
```

### 5.2 语义层面的问题详解

#### 5.2.1 符号表相关问题

**分类**: 2.1 符号表相关问题
**检测阶段**: 语义分析阶段
**应用组件**: `SemanticAnalyzer`, `SymbolTable`

**未声明变量使用**:
```capl
// SEMANTIC-UNDECLARED-001: 未声明变量使用
variables {
    int declared_var;
}

on start {
    declared_var = 5;    // 正确：已声明的变量
    undeclared_var = 10; // 错误：未声明的变量
}
```

**符号重定义**:
```capl
// SEMANTIC-REDEFINITION-001: 符号重定义
variables {
    int counter;
    int counter;  // 错误：在同一作用域中重复定义
}

void testFunction() {
    int localVar = 5;
    int localVar = 10;  // 错误：局部作用域中重复定义
}
```

**作用域违规**:
```capl
// SEMANTIC-SCOPE-VIOLATION-001: 作用域违规
variables {
    int globalVar;
}

void testFunction() {
    int localVar = 10;
}

on start {
    globalVar = 5;   // 正确：全局变量可访问
    localVar = 15;   // 错误：局部变量超出作用域
}
```

#### 5.2.2 类型检查问题

**分类**: 2.2 类型检查问题
**检测阶段**: 语义分析阶段
**应用组件**: `SemanticAnalyzer`, Type Checking Helpers

**类型不匹配**:
```capl
// SEMANTIC-TYPE-MISMATCH-001: 赋值类型不匹配
variables {
    int number;
    char text[20];
    float decimal;
}

on start {
    number = decimal;  // 警告：可能的精度丢失
    number = text;     // 错误：类型不兼容
    text = number;     // 错误：不能将int赋值给char数组
}
```

**函数调用参数错误**:
```capl
// SEMANTIC-FUNCTION-CALL-001: 函数调用参数错误
void myFunction(int param1, char param2) {
    // 函数实现
}

on start {
    myFunction(5);           // 错误：参数数量不足
    myFunction(5, 10, 15);   // 错误：参数数量过多
    myFunction("text", 5);   // 错误：参数类型不匹配
    myFunction(5, 'a');      // 正确：参数类型和数量匹配
}
```

**无效赋值操作**:
```capl
// SEMANTIC-INVALID-ASSIGNMENT-001: 无效赋值操作
const int CONSTANT_VALUE = 100;

void myFunction() {
    // 函数实现
}

on start {
    CONSTANT_VALUE = 200;  // 错误：不能给常量赋值
    myFunction = 5;        // 错误：不能给函数名赋值
}
```

#### 5.2.3 声明和定义问题

**分类**: 2.3 声明和定义问题
**检测阶段**: 语义分析阶段
**应用组件**: `SemanticAnalyzer`, Declaration Helpers

**变量声明语法错误**:
```capl
// SEMANTIC-DECLARATION-001: 变量声明语法错误
variables {
    message msg;           // 错误：缺少消息类型
    timer;                 // 错误：缺少变量名
    signal sig;            // 错误：缺少信号类型

    // 正确的声明
    message EngineData engineMsg;
    timer myTimer;
    signal EngineSpeed speedSignal;
}
```

**函数定义问题**:
```capl
// SEMANTIC-FUNCTION-DEF-001: 函数定义问题
void myFunction(int, char) {  // 错误：参数缺少名称
    // 函数体
}

// 正确的函数定义
void myFunction(int param1, char param2) {
    // 函数体
}
```

### 5.3 CAPL特定的问题详解

#### 5.3.1 CAPL对象类型问题

**分类**: 3.1 CAPL对象类型问题
**检测阶段**: 语法分析阶段 + 语义分析阶段
**应用组件**: `CAPLParser`, `SemanticAnalyzer`

**Message类型声明错误**:
```capl
// PARSER-MESSAGE-DECLARATION-001: Message类型声明错误
variables {
    message msg;  // 错误：应该是 message MessageType variableName;
}

// 正确示例
variables {
    message EngineData engineMsg;
    message * genericMsg;  // 通用消息指针
}
```

**Timer类型声明错误**:
```capl
// PARSER-TIMER-DECLARATION-001: Timer类型声明错误
variables {
    timer;        // 错误：缺少变量名
    msTimer;      // 错误：缺少变量名
}

// 正确示例
variables {
    timer myTimer;
    msTimer preciseTimer;
}
```

**Signal类型声明错误**:
```capl
// PARSER-SIGNAL-DECLARATION-001: Signal类型声明错误
variables {
    signal sig;  // 错误：应该是 signal SignalType variableName;
}

// 正确示例
variables {
    signal EngineSpeed engineSpeed;
    signal * genericSignal;  // 通用信号指针
}
```

#### 5.3.2 事件处理问题

**分类**: 3.2 事件处理问题
**检测阶段**: 语法分析阶段 + 规则检查阶段
**应用组件**: `CAPLParser`, `RuleEngine`

**事件函数定义错误**:
```capl
// PARSER-EVENT-FUNCTION-001: 事件函数定义错误
on timer {           // 错误：缺少timer名称
    writeln("timer");
}

on key {             // 错误：缺少key规范
    writeln("key");
}

// 正确示例
on timer myTimer {
    writeln("timer fired");
}

on key 'a' {
    writeln("key 'a' pressed");
}

on message EngineData {
    writeln("EngineData received");
}
```

**事件参数问题**:
```capl
// SEMANTIC-EVENT-PARAM-001: 事件参数问题
on message EngineData(int wrongParam) {  // 错误：事件函数不应有自定义参数
    writeln("message received");
}

// 正确示例
on message EngineData {
    writeln("message received: %d", this.data);
}
```

#### 5.3.3 网络和通信相关问题

**分类**: 3.3 网络和通信相关问题
**检测阶段**: 语义分析阶段 + 规则检查阶段
**应用组件**: `SemanticAnalyzer`, `RuleEngine`

**网络节点定义错误**:
```capl
// SEMANTIC-NETWORK-NODE-001: 网络节点定义错误
node invalidNode;  // 错误：节点定义语法不正确

// 正确示例
node TestNode {
    // 节点配置
}
```

**数据库对象引用错误**:
```capl
// SEMANTIC-DB-REFERENCE-001: 数据库对象引用错误
on message NonExistentMessage {  // 错误：引用不存在的数据库消息
    writeln("message");
}

// 正确示例（假设EngineData在数据库中定义）
on message EngineData {
    writeln("EngineData received");
}
```

### 5.4 代码质量和风格问题详解

#### 5.4.1 命名规范问题

**分类**: 4.1 命名规范问题
**检测阶段**: 规则检查阶段
**应用组件**: `RuleEngine`, Naming Rules

**变量命名不规范**:
```capl
// CALINT-NAMING-VARIABLE-001: 变量命名不规范
variables {
    int BadVariableName;    // 错误：应该使用camelCase
    int _private_var;       // 错误：不符合命名约定
    int var123ABC;          // 错误：混合命名风格

    // 正确示例
    int goodVariableName;
    int counter;
    int maxRetryCount;
}
```

**函数命名不规范**:
```capl
// CALINT-NAMING-FUNCTION-001: 函数命名不规范
void bad_function_name() {  // 错误：应该使用PascalCase或camelCase
    // 函数体
}

// 正确示例
void GoodFunctionName() {   // PascalCase
    // 函数体
}

void goodFunctionName() {   // camelCase
    // 函数体
}
```

#### 5.4.2 代码结构问题

**分类**: 4.2 代码结构问题
**检测阶段**: 规则检查阶段
**应用组件**: `RuleEngine`, Structure Rules

**未使用的变量/函数**:
```capl
// CALINT-UNUSED-VARIABLE-001: 未使用的变量
variables {
    int usedVar;
    int unusedVar;    // 警告：声明但从未使用
}

// CALINT-UNUSED-FUNCTION-001: 未使用的函数
void unusedFunction() {  // 警告：定义但从未调用
    // 函数体
}

on start {
    usedVar = 5;  // usedVar被使用，不会报警告
}
```

**魔术数字**:
```capl
// CALINT-MAGIC-NUMBER-001: 魔术数字
on timer myTimer {
    setTimer(myTimer, 12345);  // 错误：应该使用命名常量

    if (value > 100) {         // 错误：魔术数字
        // 处理逻辑
    }
}

// 正确示例
const int TIMER_INTERVAL = 12345;
const int MAX_VALUE = 100;

on timer myTimer {
    setTimer(myTimer, TIMER_INTERVAL);  // 正确：使用命名常量

    if (value > MAX_VALUE) {            // 正确：使用命名常量
        // 处理逻辑
    }
}
```

**函数复杂度过高**:
```capl
// CALINT-COMPLEXITY-HIGH-001: 函数复杂度过高
void complexFunction() {
    // 超过50行的函数体
    if (condition1) {
        if (condition2) {
            if (condition3) {
                if (condition4) {
                    // 嵌套层级过深（超过4层）
                }
            }
        }
    }
}
```

#### 5.4.3 CAPL最佳实践问题

**分类**: 4.3 CAPL最佳实践问题
**检测阶段**: 规则检查阶段
**应用组件**: `RuleEngine`, Best Practice Rules

**事件函数中的耗时操作**:
```capl
// CALINT-EVENT-BLOCKING-001: 事件函数中的耗时操作
on message EngineData {
    sleep(1000);           // 错误：阻塞操作

    for (int i = 0; i < 10000; i++) {  // 警告：可能的耗时循环
        // 大量计算
    }
}

// 正确示例
on message EngineData {
    // 快速处理，不阻塞
    processDataQuickly(this);
}
```

**资源管理问题**:
```capl
// CALINT-RESOURCE-LEAK-001: 资源管理问题
variables {
    timer myTimer;
}

on start {
    setTimer(myTimer, 1000);
    // 错误：没有在适当的地方取消定时器
}

// 正确示例
on start {
    setTimer(myTimer, 1000);
}

on stop {
    cancelTimer(myTimer);  // 正确：清理资源
}
```

### 5.5 预处理相关问题详解

#### 5.5.1 宏处理问题

**分类**: 5.1 宏处理问题
**检测阶段**: 预处理阶段
**应用组件**: `PreprocessingInputProcessor`, `MacroExpander`

**未知宏使用**:
```capl
// CAPL-MACRO-001: 未知宏使用
write("%UNKNOWN_MACRO%");        // 错误：未定义的宏
writeln("file = %s", %INVALID%); // 错误：无效的宏名

// 正确示例（使用预定义的CAPL宏）
write("%FILE_NAME%");            // 正确：预定义宏
writeln("line = %d", %LINE_NUMBER%); // 正确：预定义宏
```

**宏语法错误**:
```capl
// CAPL-MACRO-002: 宏语法错误
write("%FILE_NAME");   // 错误：缺少结束的%
write("FILE_NAME%");   // 错误：缺少开始的%
write("%%FILE_NAME%%"); // 错误：多余的%符号

// 正确示例
write("%FILE_NAME%");  // 正确：完整的宏语法
```

#### 5.5.2 包含文件问题

**分类**: 5.2 包含文件问题
**检测阶段**: 预处理阶段
**应用组件**: `PreprocessingInputProcessor`, Include Resolver

**包含文件路径错误**:
```capl
// CAPL-INCLUDE-001: 包含文件不存在
#include "nonexistent.cin"     // 错误：文件不存在
#include "wrong/path/file.cin" // 错误：路径错误

// 正确示例
#include "common.cin"          // 正确：文件存在
#include "../shared/utils.cin" // 正确：相对路径正确
```

**循环包含**:
```capl
// 文件 A.cin
#include "B.cin"

// 文件 B.cin
#include "A.cin"  // CAPL-INCLUDE-002: 循环包含错误

// 正确示例：避免循环包含
// 文件 common.cin - 包含共同的定义
// 文件 A.cin - 包含 common.cin
// 文件 B.cin - 包含 common.cin
```

## 6. 分层错误处理流程

### 6.1 分层错误检测流程图

```mermaid
graph TD
    A[源代码输入] --> B[第1层: 预处理阶段]
    B --> B1{宏处理}
    B1 -->|错误| B2[记录宏错误]
    B1 -->|成功| B3{包含文件处理}
    B3 -->|错误| B4[记录包含错误]
    B3 -->|成功| C[第2层: 词法分析阶段]

    C --> C1{Token识别}
    C1 -->|错误| C2[记录词法错误<br/>错误恢复]
    C1 -->|成功| D[第3层: 语法分析阶段]
    C2 --> D

    D --> D1{语法匹配}
    D1 -->|错误| D2[上下文分析<br/>生成具体错误消息]
    D1 -->|成功| E[第4层: AST构建阶段]
    D2 --> E

    E --> E1{AST节点构建}
    E1 -->|错误| E2[创建错误占位符节点]
    E1 -->|成功| F[第5层: 语义分析阶段]
    E2 --> F

    F --> F1{符号表构建}
    F1 --> F2{类型检查}
    F2 --> F3{作用域分析}
    F3 -->|错误| F4[记录语义错误<br/>继续分析]
    F3 -->|成功| G[第6层: 规则检查阶段]
    F4 --> G

    G --> G1{代码风格检查}
    G1 --> G2{最佳实践检查}
    G2 --> G3{质量度量}
    G3 --> H[统一错误汇总]

    B2 --> H
    B4 --> H
    C2 --> H
    D2 --> H
    E2 --> H
    F4 --> H

    H --> I[生成统一报告]
```

### 6.2 分层错误恢复策略

| 层次 | 错误类型 | 恢复策略 | 继续分析 | 影响范围 |
|------|---------|---------|---------|---------|
| **第1层** | 预处理错误 | 跳过错误宏，使用原始文本 | ✓ | 当前文件 |
| **第2层** | 词法错误 | 跳过错误字符，继续tokenize | ✓ | 当前token |
| **第3层** | 语法错误 | 错误恢复规则，同步到下一语句 | ✓ | 当前语句 |
| **第4层** | AST构建错误 | 创建错误占位符节点 | ✓ | 当前节点 |
| **第5层** | 语义错误 | 记录错误，继续符号表构建 | ✓ | 当前符号 |
| **第6层** | 规则检查问题 | 记录问题，继续其他规则 | ✓ | 当前规则 |

### 6.3 错误传播和隔离机制

#### 6.3.1 错误传播原则

1. **向上传播**：下层的错误会影响上层的分析质量，但不会阻止上层分析
2. **错误隔离**：每层的错误处理都是独立的，不会相互干扰
3. **最大化分析**：即使存在错误，也要尽可能多地进行分析

#### 6.3.2 错误隔离示例

```java
// LinterEngine中的错误隔离处理
public LinterResult analyze(List<SourceFile> sourceFiles, AppConfiguration configuration) {
    List<IssueObject> allIssues = new ArrayList<>();
    boolean continueAnalysis = true;

    try {
        // 第1层：预处理 - 错误不阻止后续分析
        InputProcessingResult inputResult = inputProcessor.processFiles(sourceFiles);
        allIssues.addAll(inputResult.getIssues());

        // 即使有预处理错误，也继续分析
        List<SourceFile> processedFiles = inputResult.hasIssues() ?
            sourceFiles : inputResult.getSourceFiles();

    } catch (Exception e) {
        // 预处理阶段的致命错误
        allIssues.add(createFatalError("PREPROCESSING-FATAL", e.getMessage()));
        continueAnalysis = false;
    }

    if (continueAnalysis) {
        try {
            // 第2、3层：词法和语法分析
            ParsingResult parsingResult = lexerParser.parse(processedFiles);
            allIssues.addAll(parsingResult.getIssues());

            // 即使有语法错误，也尝试AST构建
            if (parsingResult.hasParseTrees()) {
                // 第4层：AST构建
                AstConstructionResult astResult = astBuilder.buildAst(parsingResult);
                allIssues.addAll(astResult.getIssues());

                // 第5层：语义分析
                SemanticAnalysisResult semanticResult = semanticEngine.analyze(astResult, configuration);
                allIssues.addAll(semanticResult.getIssues());

                // 第6层：规则检查
                List<IssueObject> ruleIssues = ruleEngine.analyze(astResult.getAbstractSyntaxTrees(), configuration);
                allIssues.addAll(ruleIssues);
            }

        } catch (Exception e) {
            allIssues.add(createFatalError("ANALYSIS-FATAL", e.getMessage()));
        }
    }

    return new LinterResult(allIssues);
}
```

### 6.3 上下文感知错误消息

#### 6.3.1 错误消息增强策略

```java
// 伪代码示例
public class ContextAwareErrorAnalyzer {

    public String analyzeMessageDeclarationError(NoViableAltException e, Parser parser) {
        TokenStream tokens = parser.getTokenStream();
        int errorIndex = e.getOffendingToken().getTokenIndex();

        // 检查前面的token是否为"message"
        if (isPrecedingToken(tokens, errorIndex, "message")) {
            return "message类型声明方式不对。正确语法：message <MessageType> <variableName>;";
        }

        return null; // 不是message声明错误
    }

    public String analyzeTimerDeclarationError(NoViableAltException e, Parser parser) {
        // 类似的timer声明错误分析
    }
}
```

#### 6.3.2 错误消息模板

```properties
# 语法错误消息模板
error.parser.message.declaration.invalid = message类型声明方式不对。正确语法：message <MessageType> <variableName>;
error.parser.timer.declaration.invalid = timer类型声明方式不对。正确语法：timer <variableName>; 或 msTimer <variableName>;
error.parser.signal.declaration.invalid = signal类型声明方式不对。正确语法：signal <SignalType> <variableName>;

# 语义错误消息模板
error.semantic.undeclared.variable = 变量 ''{0}'' 未声明就使用
error.semantic.type.mismatch = 类型不匹配：无法将 ''{0}'' 类型赋值给 ''{1}'' 类型
error.semantic.function.call.args = 函数 ''{0}'' 调用参数错误：期望 {1} 个参数，实际 {2} 个

# 代码质量消息模板
warning.quality.naming.convention = 标识符 ''{0}'' 不符合命名规范
warning.quality.magic.number = 发现魔术数字 ''{0}''，建议使用命名常量
info.quality.unused.variable = 变量 ''{0}'' 已声明但未使用
```

## 7. 扩展性设计

### 7.1 新错误类型添加流程

1. **定义错误类型**：在相应的枚举或常量类中添加新的错误ID
2. **实现检测逻辑**：在对应的分析器或Helper类中添加检测代码
3. **添加错误消息**：在国际化资源文件中添加错误消息模板
4. **编写测试用例**：为新的错误类型编写单元测试和集成测试
5. **更新文档**：更新本设计文档和用户文档

### 7.2 规则引擎扩展

```java
// 新规则接口
public interface CaplRule {
    String getRuleId();
    String getDescription();
    SeverityLevel getDefaultSeverity();
    List<IssueObject> check(AstNode node, RuleContext context);
    boolean isApplicable(NodeType astNodeType);
}

// 规则注册机制
public class RuleRegistry {
    private Map<String, CaplRule> rules = new HashMap<>();

    public void registerRule(CaplRule rule) {
        rules.put(rule.getRuleId(), rule);
    }

    public List<CaplRule> getApplicableRules(NodeType astNodeType) {
        return rules.values().stream()
            .filter(rule -> rule.isApplicable(astNodeType))
            .collect(Collectors.toList());
    }
}
```

### 7.3 配置驱动的错误检测

```yaml
# 错误检测配置示例
error_detection:
  preprocessing:
    enabled: true
    macro_validation: strict
    include_path_validation: true

  syntax:
    enabled: true
    context_aware_messages: true
    error_recovery: true

  semantic:
    enabled: true
    symbol_table_validation: true
    type_checking: strict
    scope_analysis: true

  quality_rules:
    naming_conventions:
      enabled: true
      variable_pattern: "^[a-z][a-zA-Z0-9]*$"
      function_pattern: "^[A-Z][a-zA-Z0-9]*$"

    complexity:
      enabled: true
      max_function_length: 50
      max_nesting_depth: 4

    best_practices:
      enabled: true
      check_unused_variables: true
      check_magic_numbers: true
```

## 8. 总结

本文档详细描述了CAPL Linter的错误检测分类体系和应用对应关系。通过系统化的分类和分层的错误检测架构，系统能够从基础的语法错误到高级的代码质量问题进行全面的检测和报告。

### 8.1 错误检测能力概览

CAPL Linter能够检测的问题类型包括：

1. **语法层面的问题**（ANTLR阶段）
   - 词法错误：非法字符、未识别的token序列
   - 语法错误：语法结构错误、关键字使用错误、表达式语法错误

2. **语义层面的问题**（AST构建+语义分析阶段）
   - 符号表相关问题：未声明变量使用、符号重定义、作用域违规
   - 类型检查问题：类型不匹配、函数调用参数错误、无效赋值操作
   - 声明和定义问题：变量声明语法错误、函数定义问题、结构体/枚举定义问题

3. **CAPL特定的问题**
   - CAPL对象类型问题：Message、Timer、Signal类型声明错误
   - 事件处理问题：事件函数定义错误、事件参数问题
   - 网络和通信相关问题：网络节点定义错误、数据库对象引用错误

4. **代码质量和风格问题**（规则引擎阶段）
   - 命名规范问题：变量、函数、常量命名不规范
   - 代码结构问题：未使用的变量/函数、魔术数字、函数复杂度过高
   - CAPL最佳实践问题：事件函数中的耗时操作、资源管理问题

5. **预处理相关问题**
   - 宏处理问题：未知宏使用、宏语法错误
   - 包含文件问题：包含文件路径错误、循环包含

### 8.2 分层错误处理系统的核心特点

#### 8.2.1 架构特点
- **分层设计**：采用6层分层架构，每层专注特定类型的错误检测
- **职责分离**：每层有明确的职责边界，降低系统复杂度
- **错误隔离**：层间错误隔离，某层错误不阻止其他层分析
- **统一协调**：LinterEngine统一协调各层，提供一致的分析流程

#### 8.2.2 检测特点
- **全面性**：覆盖从预处理到代码质量的各个层面
- **渐进式**：从基础语法错误到高级语义问题的渐进式检测
- **上下文感知**：第3层提供CAPL特定的上下文感知错误消息
- **精确性**：提供准确的错误位置和详细的上下文信息

#### 8.2.3 技术特点
- **错误恢复**：每层都有相应的错误恢复策略，确保分析连续性
- **容错性**：即使存在错误也能继续分析，提供最大化的错误检测
- **可扩展性**：支持新错误类型和检测规则的动态添加
- **性能优化**：支持按需分析、并行处理和增量分析

#### 8.2.4 用户体验特点
- **统一报告**：所有层的错误通过统一的IssueObject和报告系统输出
- **清晰消息**：提供具体、可操作的错误消息和修复建议
- **CAPL特定**：针对CAPL语言特性设计的专门检测规则和错误消息
- **分级处理**：ERROR、WARNING、INFO三级错误分类

### 8.3 分层系统与现有架构的关系

#### 8.3.1 架构映射关系

| 分层系统 | 现有架构组件 | 核心接口/类 | 职责描述 |
|---------|-------------|------------|---------|
| **第1层：预处理** | Input Processing Module | `PreprocessingInputProcessor`<br/>`MacroExpander` | 宏展开、文件包含处理 |
| **第2层：词法分析** | Lexer Module | `CAPLLexer`<br/>`CaplAntlrErrorListener` | Token识别、词法错误处理 |
| **第3层：语法分析** | Parser Module | `CAPLParser`<br/>`CaplAntlrErrorListener` | 语法匹配、上下文感知错误 |
| **第4层：AST构建** | AST Construction Module | `AntlrToCustomAstVisitor`<br/>Helper Classes | 自定义AST构建、节点验证 |
| **第5层：语义分析** | Semantic Analysis Engine | `SemanticAnalyzer`<br/>`SymbolTable` | 符号表、类型检查、作用域 |
| **第6层：规则检查** | Rule Engine | `RuleEngine`<br/>Rule Classes | 代码质量、风格、最佳实践 |
| **统一协调** | Core Engine | `LinterEngine` | 分层协调、错误汇总 |
| **报告生成** | Report Generator | `ReportGenerator`<br/>`IssueObject` | 统一错误报告 |

#### 8.3.2 数据流转关系

```mermaid
graph LR
    A[SourceFile] --> B[PreprocessingInputProcessor]
    B --> C[InputProcessingResult]
    C --> D[AntlrLexerParser]
    D --> E[ParsingResult]
    E --> F[AstConstructionEngine]
    F --> G[AstConstructionResult]
    G --> H[SemanticAnalysisEngine]
    H --> I[SemanticAnalysisResult]
    G --> J[RuleEngine]
    J --> K[List&lt;IssueObject&gt;]
    I --> L[LinterResult]
    K --> L
    L --> M[ReportGenerator]
    M --> N[统一报告输出]
```

#### 8.3.3 错误对象统一化

所有层次的错误都通过统一的`IssueObject`进行标准化：

```java
// 统一的错误对象结构
public class IssueObject {
    private String ruleId;           // 标识错误来源层次：CAPL-*, LEXER-*, PARSER-*, AST-*, SEMANTIC-*, CALINT-*
    private String message;          // 层次特定的错误消息
    private SeverityLevel severity;  // ERROR/WARNING/INFO
    private SourceLocation location; // 精确的源码位置
    private String suggestion;       // 层次特定的修复建议
    private String codeContext;      // 错误上下文代码
}
```

### 8.4 应用价值

#### 8.4.1 开发效率价值
- **早期错误发现**：在编译前发现语法、语义和质量问题
- **精确错误定位**：6层分析提供精确的错误位置和上下文
- **智能错误恢复**：即使存在错误也能继续分析，提供全面反馈
- **减少调试时间**：清晰的错误消息和修复建议

#### 8.4.2 代码质量价值
- **多层次检查**：从语法正确性到代码质量的全方位检查
- **CAPL特定优化**：针对CAPL语言特性的专门检测规则
- **统一标准**：团队内统一的代码风格和最佳实践
- **质量度量**：复杂度、可维护性等质量指标

#### 8.4.3 团队协作价值
- **标准化报告**：统一的错误报告格式和规则ID
- **可配置规则**：团队可以自定义检测规则和严重级别
- **集成友好**：支持IDE、CI/CD等开发工具集成
- **知识传递**：错误消息和建议帮助团队成员学习最佳实践

### 8.4 未来改进方向

- **增强上下文感知**：实现更智能的错误消息生成
- **扩展CAPL规则**：添加更多CAPL特定的语义检查规则
- **智能修复建议**：提供自动化的错误修复建议
- **性能优化**：支持增量分析和缓存机制
- **IDE集成**：提供更好的开发工具集成支持
- **自定义规则**：支持用户自定义的检查规则和配置
