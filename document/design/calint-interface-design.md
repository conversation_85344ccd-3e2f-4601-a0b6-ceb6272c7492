### 2.4.1 输入处理器 (InputProcessor) 接口定义

**职责**:
`InputProcessor` 负责根据用户提供的文件路径或目录路径，定位并读取所有相关的 CAPL 源文件内容。它处理文件系统的交互、字符编码的检测与转换，并将结果封装成统一的 `SourceFile` 对象列表，供后续的词法分析器使用。

1.  接收一个或多个文件/目录的绝对路径作为输入。
2.  如果路径是目录，则递归查找该目录下符合条件的 CAPL 源文件（例如，扩展名为 `.can`, `.cin`, `.capl` 的文件，具体扩展名可配置）。
3.  读取每个有效源文件的内容。
4.  处理文件的字符编码（例如，UTF-8, GBK），优先使用文件本身的 BOM (Byte Order Mark)，否则使用用户指定的默认编码或系统默认编码。
5.  将读取到的文件路径和内容封装成 `SourceFile` 对象列表返回。

**接口定义 (Java)**:

```java
package com.polelink.calint.input; // 或 com.polelink.calint.engine;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;

/**
 * 表示一个已读取的源文件及其基本信息。
 * 这是一个简单的数据持有者 (POJO/Record)。
 */
// @Record (示意，具体实现可以是 class 或 record)
// public record SourceFile(
//     String absolutePath,  // 文件的绝对路径
//     String content,       // 文件内容
//     Charset detectedCharset // 检测到或使用的字符集
// ) {}

/**
 * 输入处理器接口。
 * 负责处理输入文件和目录，读取源代码内容。
 */
public interface InputProcessor {

    /**
     * 处理给定的文件和目录路径列表，加载所有有效的 CAPL 源文件。
     * <p>
     * 对于目录路径，此方法会递归地查找所有匹配预定义扩展名（如 .can, .cin, .capl）的文件。
     * 它会尝试检测文件的字符编码（例如通过BOM），如果无法检测，则使用指定的默认字符集。
     * </p>
     *
     * @param paths 包含一个或多个文件绝对路径或目录绝对路径的列表。
     * @param defaultCharset 当无法从文件BOM中确定编码时使用的默认字符集。
     * @return 一个 {@code List<SourceFile>}，其中每个 {@code SourceFile} 对象包含文件的绝对路径、
     *         其文本内容以及用于读取的字符集。如果某个文件无法读取或不是预期的源文件类型，
     *         可以被跳过（并可能记录一个警告/错误），或者根据实现策略抛出异常。
     * @throws IOException 如果在访问文件系统时发生不可恢复的 I/O 错误。
     *                     个别文件读取错误可以被捕获并作为警告处理，而不是中止整个过程。
     */
    List<SourceFile> loadSourceFiles(List<String> paths, Charset defaultCharset) throws IOException;
}
```

**主要方法说明**:

*   `loadSourceFiles(List<String> paths, Charset defaultCharset)`:
    *   `paths`: 一个字符串列表，每个字符串是待处理文件的绝对路径或包含源文件的目录的绝对路径。
    *   `defaultCharset`: `java.nio.charset.Charset` 类型，指定当文件没有BOM时应使用的默认字符编码。
    *   返回值: `List<SourceFile>`。`SourceFile` 是一个简单的数据结构 (例如 Java Record 或 POJO)，包含至少三个字段：
        *   `absolutePath` (String): 文件的绝对路径。
        *   `content` (String): 文件的文本内容。
        *   `detectedCharset` (Charset): 实际用于读取文件内容的字符集。
    *   异常: 可能会抛出 `IOException` 如果遇到严重的、无法恢复的文件系统访问问题。个别文件读取问题（如权限不足、文件不存在但路径在列表中）可以内部处理为警告，并从返回列表中排除该文件。

**注意**: `SourceFile` 结构在这里被提及，它的具体实现（如作为 `record` 或普通 `class`）将在后续实现阶段确定。其目的是封装单个源文件的基本信息以供后续模块使用。

### 2.4.2 词法/语法分析器 (LexerParser)

**职责**:
`LexerParser` 组件负责将输入的源代码文本转换为解析树 (Parse Tree) 或更准确地说是具体语法树 (Concrete Syntax Tree, CST)，并在此过程中捕获词法错误和语法错误。该组件将重度依赖 ANTLR v4 生成的词法分析器 (`CAPLLexer`) 和语法分析器 (`CAPLParser`)。

1.  接收 `SourceFile` 对象列表（由 `InputProcessor` 提供）。
2.  对于每个 `SourceFile`：
    *   使用 ANTLR 的 `CharStreams.fromString()` 从文件内容创建字符流。
    *   实例化 `CAPLLexer` 并将字符流传递给它，生成 Token 流。
    *   实例化 `CAPLParser` 并将 Token 流传递给它。
    *   调用解析器的入口规则（例如，`programUnit` 或 `compilationUnit`，根据 `CAPL.g4` 定义）来生成 Parse Tree。
3.  捕获 ANTLR 在词法分析和语法分析过程中报告的错误。这通常通过注册自定义的 `ANTLRErrorListener` 来实现。
4.  将捕获到的词法和语法错误转换为 `IssueObject` 列表。
5.  输出每个源文件对应的 Parse Tree (或其根节点) 和收集到的 `IssueObject` 列表。

**接口/交互模式 (Java - 概念性)**:

由于此模块主要编排 ANTLR 生成的类，它可能不是一个严格的独立接口，而是一个封装了此过程的类或方法。其核心功能可以概念化如下：

```java
package com.polelink.calint.parser; // 或 com.polelink.calint.engine;

import com.polelink.calint.input.SourceFile;
import com.polelink.calint.parser.ParsingResult;

import java.util.List;

/**
 * 表示词法/语法分析阶段的输出结果。
 * 包含每个成功解析的源文件对应的 ParseTree，以及所有收集到的问题。
 */
// @Record (示意)
// public record ParsingResult(
//     Map<SourceFile, ParseTree> parseTrees, // 成功解析的源文件到其ParseTree的映射
//     List<IssueObject> issues             // 在词法/语法分析阶段发现的所有问题
// ) {}

/**
 * 封装了词法分析和语法分析逻辑的组件。
 */
public interface LexerParser {

    /**
     * 对提供的源文件列表进行词法分析和语法分析。
     *
     * @param sourceFiles 从 InputProcessor 获取的源文件列表。
     * @return 一个 {@code ParsingResult} 对象，其中包含：
     *         1. 一个映射 (Map)，键是成功解析的 {@code SourceFile}，值是对应的 ANTLR {@code ParseTree} 的根节点。
     *            如果某个文件解析失败（例如，仅有词法错误但无法构建有效的 ParseTree），则可能不在此映射中，
     *            或者对应一个表示错误的特殊 ParseTree 节点（取决于错误恢复策略）。
     *         2. 一个 {@code List<IssueObject>}，包含所有在词法分析和语法分析阶段检测到的问题。
     */
    ParsingResult parse(List<SourceFile> sourceFiles);

    // 内部可能需要辅助方法来配置 ANTLR 错误监听器等。
}
```

**主要交互和输出**:

*   **输入**: `List<SourceFile>`。
*   **输出**: `ParsingResult` 对象，包含：
    *   `Map<SourceFile, ParseTree>`: 将每个（成功解析的）源文件映射到其对应的 ANTLR Parse Tree。 `ParseTree` 是 `org.antlr.v4.runtime.tree.ParseTree` 接口类型，具体实现由 ANTLR 生成的解析器提供 (例如 `ProgramUnitContext`)。
    *   `List<IssueObject>`: 在此阶段发现的词法错误和语法错误，已转换为统一的 `IssueObject` 格式。

**错误处理**: 
通过实现 `org.antlr.v4.runtime.ANTLRErrorListener` 接口并将其注册到 Lexer 和 Parser 实例上，可以捕获标准的 ANTLR 错误信息 (如 `syntaxError` 方法)。这些信息随后被转换成 `IssueObject`，包含错误消息、位置（行号、列号）、文件名等。

**注意**:
*   `ParsingResult` 是一个建议的数据结构，用于封装此阶段的多种输出。
*   这个组件的核心是正确配置和调用 ANTLR 生成的 `CAPLLexer` 和 `CAPLParser`。


### 2.4.3 AST 构建器 (AstBuilder)

**职责**:
`AstBuilder` 组件负责将 `LexerParser` 生成的 ANTLR Parse Tree 转换为项目自定义的抽象语法树 (AST)。这个自定义 AST 将作为后续语义分析和规则检查的基础。

1.  接收 `ParsingResult` 对象（由 `LexerParser` 提供），其中包含源文件到其 Parse Tree 的映射。
2.  对于每个源文件的 Parse Tree：
    *   使用 ANTLR 提供的 Visitor 或 Listener 模式遍历 Parse Tree。
    *   根据 Parse Tree 节点的类型 (Context 对象) 和内容，创建相应的自定义 `AstNode` 子类实例 (其层级结构在 `core-data-structures.md` 中定义或将在后续细化)。
    *   将这些 `AstNode` 实例链接起来，形成一个完整的 AST 结构，根节点通常代表整个程序单元或编译单元。
3.  在遍历和构建过程中，可能会遇到无法识别的 Parse Tree 结构或转换逻辑未覆盖的情况，这些应被记录为 `IssueObject`。
4.  输出每个源文件对应的自定义 AST (通常是其根 `AstNode`) 和在 AST 构建过程中收集到的 `IssueObject` 列表。

**接口/交互模式 (Java - 概念性)**:

```java
package com.polelink.calint.ast; // 或 com.polelink.calint.engine;

import com.polelink.calint.input.SourceFile;
import parser.com.polelink.calint.ParsingResult; // 从 LexerParser 获取
import com.polelink.calint.engine.ast.AstNode;     // 假设的 AstNode 定义
import com.polelink.calint.ast.AstBuildResult;


/**
 * 表示 AST 构建阶段的输出结果。
 * 包含每个成功构建 AST 的源文件对应的 AstNode (根节点)，以及所有收集到的问题。
 * 注意: AstNode 的具体定义在 core-data-structures.md 中。
 */
// @Record (示意，具体定义在 core-data-structures.md 或相关实现中)
// public record AstBuildResult(
//     Map<SourceFile, AstNode> asts,      // 源文件到其 AST 根节点的映射
//     List<IssueObject> issues            // AST 构建阶段的问题
// ) {}

/**
 * 负责将 ANTLR Parse Tree 转换为自定义抽象语法树 (AST) 的组件。
 * AST 是后续语义分析和规则检查的基础。
 */
public interface AstBuilder {

    /**
     * 将给定的 {@code ParsingResult} (包含 Parse Trees) 转换为自定义的 AST 结构。
     *
     * @param parsingResult 从 {@code LexerParser} 组件获取的解析结果，
     *                      其中包含每个源文件对应的 Parse Tree 以及任何词法/语法错误。
     * @return 一个 {@code AstBuildResult} 对象，该对象包含：
     *         1. 一个映射 ({@code Map<SourceFile, AstNode>})，其中键是 {@code SourceFile} 对象，
     *            值是为该文件构建的自定义 AST 的根 {@code AstNode}。
     *         2. 一个问题列表 ({@code List<IssueObject>})，包含在 AST 构建过程中遇到的任何问题
     *            (例如，无法转换的 Parse Tree 节点或结构性问题)。
     */
    AstBuildResult build(ParsingResult parsingResult);
}
```

### 2.4.4 CommandLineInterface (CLI) Handler

**职责**:
`CommandLineInterface` (CLI) Handler 负责解析用户通过命令行提供的参数和选项。它将处理诸如 `--help`、`--version` 等标准命令，以及 `--init` 用于生成初始配置文件。解析后的参数（如输入文件/目录、配置文件路径、报告格式等）将被传递给应用程序的主控制流程或配置管理器，以指导后续的分析行为。

1.  解析命令行参数和选项。
2.  处理 `--help`, `--version`，并直接输出相关信息后退出。
3.  处理 `--init` 命令，触发默认配置文件的生成，并随后退出。
4.  收集所有其他有效的配置参数（如输入路径、自定义配置文件、报告格式、特定规则、严重级别等）。
5.  将收集到的配置信息传递给应用核心逻辑。

**推荐实现库**:
-   Java: Picocli (一个功能强大且易于使用的命令行参数解析框架)。

**命令行接口定义**:

-   **主命令**: `calint`
-   **参数**:
    -   `<file_or_directory>`: (必需) 指定要分析的单个 CAPL 文件或包含 CAPL 文件的目录。可以接受多个文件或目录。
-   **选项**:
    -   `-c, --config <filePath>`: 指定一个自定义配置文件的路径 (例如 `calint.json` 或 `calint.yaml`)。如果未提供，工具会尝试在标准位置查找默认配置文件。
    -   `-f, --format <format>`: 指定分析报告的输出格式。支持的格式包括:
        -   `CONSOLE` (默认): 将结果直接输出到控制台。
        -   `JSON`: 以 JSON 格式输出结果。
        -   `HTML`: 以 HTML 格式生成报告。
        (注: `ReportFormat` 枚举中还定义了 `XML` 和 `SARIF`，这些可以在后续版本中添加到 CLI 支持中。)
    -   `-o, --output <filePath>`: 指定报告输出文件的路径。如果指定了此选项但格式为 `CONSOLE`，则此选项可能被忽略或产生警告。如果格式不是 `CONSOLE` 且未指定此选项，工具可以将报告输出到具有预定义名称的文件 (例如 `calint-report.json`, `calint-report.html`)。
    -   `-r, --rules <ruleId1,ruleId2,...>`: 一个逗号分隔的规则 ID 列表，用于仅运行这些指定的规则。如果未提供，则运行配置文件中所有启用的规则。
    -   `-s, --severity <level>`: 指定报告的最低问题严重级别。支持的级别 (与 `SeverityLevel` 枚举对应，例如):
        -   `ERROR`
        -   `WARNING`
        -   `INFO`
        -   `STYLE`
        (例如，如果设置为 `WARNING`，则 `ERROR` 和 `WARNING` 级别的问题将被报告。)
    -   `--init`: 在当前目录生成一个默认的配置文件 (例如 `calint-config.json` 或 `calint-config.yaml`)。执行此操作后，工具应退出。
    -   `-h, --help`: 显示帮助信息并退出。
    -   `-V, --version`: 显示工具的版本信息并退出。

**交互与配置**:
CLI Handler 解析参数后，会将这些设置传递给 `Configuration` 对象（或通过 `ConfigurationManager` 加载和合并配置）。这些配置将指导 `InputProcessor`, `RuleEngine` 等其他组件的行为。


**主要交互和输出**:

*   **输入**: `ParsingResult` (来自 `LexerParser`)。
*   **输出**: `AstConstructionResult` 对象，概念上包含：
    *   `Map<SourceFile, AstNode>`: 将每个源文件映射到其自定义 AST 的根节点。`AstNode` 是在 `core-data-structures.md` 中定义的 AST 节点接口/基类的实例。
    *   `List<IssueObject>`: 在 AST 构建阶段发现的任何问题。

**错误处理**:
在将 Parse Tree 转换为 AST 的过程中，可能会出现转换逻辑未覆盖所有 Parse Tree 变体的情况，或者遇到与预期不符的结构。这些情况应被捕获并转化为 `IssueObject`，详细说明问题发生的位置和性质。

**注意**:
*   `AstConstructionResult` 是一个建议的数据结构，用于封装此阶段的输出。其具体定义（例如，作为 Java Record 或 POJO）及其包含的 `AstNode` 类型，应与 `core-data-structures.md` 中关于 AST 的定义保持一致。
*   此组件的核心是实现一个健壮的 Visitor 或 Listener，能够正确地将 ANTLR 生成的 Parse Tree 映射到自定义的 AST 结构。


### 2.4.4 语义分析引擎 (SemanticAnalysisEngine)

**职责**:
`SemanticAnalysisEngine` 组件负责对 `AstBuilder` 生成的抽象语法树 (AST) 进行深入分析，以检测语义层面的错误和潜在问题。这包括但不限于：

1.  **符号解析与符号表构建**:
    *   遍历 AST，识别声明（如变量、函数、参数、自定义类型等）。
    *   构建和填充符号表 (`SymbolTable`)，记录符号的名称、类型、作用域等信息。
    *   解析标识符引用，将其链接到符号表中的相应声明。
2.  **作用域分析**:
    *   确定代码中不同部分的作用域（全局、文件、函数、块等）。
    *   确保符号在其有效作用域内被访问。
3.  **类型检查**:
    *   根据 CAPL 的类型系统 (在 `core-data-structures.md` 和相关设计文档中定义)，验证表达式、赋值、函数调用等操作中的类型兼容性。
    *   识别类型不匹配、非法类型转换等问题。
4.  **规则执行**:
    *   加载并应用在配置中启用的语义规则和风格规则。
    *   规则通常会遍历 AST (或特定类型的节点) 并使用符号表和类型信息进行检查。
5.  收集所有在语义分析过程中发现的问题，并将其转换为 `IssueObject` 列表。

**接口/交互模式 (Java - 概念性)**:

```java
package com.polelink.calint.analysis; // 或 com.polelink.calint.engine;

import ast.com.polelink.calint.AstBuildResult; // 从 AstBuilder 获取
import com.polelink.calint.config.Configuration;    // 假设的配置对象
// import com.polelink.calint.symbol.ProjectIndex;   // 假设的跨文件符号索引


/**
 * 表示语义分析阶段的输出结果。
 * 主要包含在语义分析过程中发现的所有问题。
 * 语义分析的结果（如填充的符号表、类型信息等）可能会被存储在
 * 一个更广泛的分析上下文中，或直接附加到 AST 节点上，而不是作为此结果对象的直接部分。
 */
// @Record (示意)
// public record SemanticAnalysisResult(
//     List<IssueObject> issues // 语义分析阶段发现的所有问题
//     // อาจจะมีข้อมูลเพิ่มเติมเกี่ยวกับ SymbolTable หรือ ProjectIndex ที่ถูกปรับปรุง
// ) {}

/**
 * 负责对 AST 进行语义分析，包括符号解析、类型检查和规则执行的组件。
 */
public interface SemanticAnalysisEngine {

    /**
     * 对提供的 AST 集合进行语义分析。
     *
     * @param astResult 包含 ASTs (来自 {@code AstBuilder}) 和之前阶段可能产生的问题。
     * @param configuration 当前分析的配置信息，例如启用的规则、规则参数等。
     * @param projectIndex (可选，可能为 null) 一个项目级别的符号索引，用于跨文件分析。
     *                     如果分析是单文件模式或此功能未实现，则可能不使用。
     * @return 一个 {@code SemanticAnalysisResult} 对象，其中主要包含在语义分析过程中新发现的
     *         {@code IssueObject} 列表。
     *         注意：此方法可能会修改传入的 AST (例如，通过附加符号或类型信息)，
     *         或者更新 {@code projectIndex}。
     */
    SemanticAnalysisResult analyze(AstBuildResult astResult,
                                   Configuration configuration
            /*, ProjectIndex projectIndex */);
}
```

**主要交互和输出**:

*   **输入**:
    *   `AstConstructionResult`: 包含 ASTs 和构建阶段的问题。
    *   `Configuration`: 当前的分析配置。
    *   `ProjectIndex` (可选): 用于跨文件符号解析。
*   **输出**: `SemanticAnalysisResult` 对象，主要包含：
    *   `List<IssueObject>`: 在语义分析阶段新发现的问题。
*   **副作用**:
    *   可能会构建/填充符号表。
    *   可能会向 AST 节点附加类型信息或符号链接。
    *   可能会更新 `ProjectIndex`。

**错误处理**:
语义分析过程中识别的各种问题（如未声明的变量、类型不匹配、违反规则等）都将被转换为 `IssueObject` 并包含在结果中。

**注意**:
*   `SemanticAnalysisResult` 是一个建议的数据结构。语义分析的具体产物（如符号表）可能不会直接作为此结果的一部分返回，而是作为分析上下文的一部分被其他组件（如规则）访问。
*   `SemanticAnalysisEngine` 是一个较为复杂的组件，其内部可能包含独立的子组件，如符号表管理器 (`SymbolTableManager`)、类型检查器 (`TypeChecker`)、作用域分析器 (`ScopeAnalyzer`) 和规则执行器 (`RuleExecutor`)。这些子组件的接口可能会在更详细的设计阶段定义。
*   `Configuration` 和 `ProjectIndex` 是假设的辅助对象/接口，它们的具体定义将在相应模块设计时确定。


### 2.4.5 报告生成器 (ReportGenerator)

**职责**:
`ReportGenerator` 组件负责将分析过程中收集到的所有 `IssueObject`（来自词法/语法分析、AST构建、语义分析等各个阶段）格式化并输出到用户指定的媒介。

1.  接收最终的 `IssueObject` 列表。
2.  接收配置信息，该信息指定了所需的输出格式（例如 Console, JSON, HTML）和任何特定于格式的选项（例如，输出文件路径）。
3.  根据指定的格式，将 `IssueObject` 列表转换为相应的文本或文件表示。
    *   **Console**: 将问题直接打印到标准输出，通常会包括文件名、行号、列号、严重级别、规则ID和消息。
    *   **JSON**: 将问题列表序列化为 JSON 数组，每个对象代表一个 `IssueObject`。
    *   **HTML**: 生成一个包含问题列表的 HTML报告，可能带有排序、过滤等交互功能。
4.  将生成的报告输出到指定位置（例如，控制台或文件系统中的文件）。

**接口/交互模式 (Java - 概念性)**:

```java
package com.polelink.calint.report; // 或 com.polelink.calint.engine;

import com.polelink.calint.config.Configuration; // 假设的配置对象
import issue.com.polelink.calint.IssueObject;   // 假设的 IssueObject 包

import java.io.IOException;
import java.util.List;

/**
 * 定义报告输出的格式。
 * (这也可以在 core-data-structures.md 中定义)
 */
enum ReportFormat {
    CONSOLE,
    JSON,
    HTML
    // 更多格式可在此添加
}

/**
 * 负责将分析结果 (IssueObject 列表) 格式化并输出为用户可读的报告。
 */
public interface ReportGenerator {

    /**
     * 生成并输出基于提供的 IssueObject 列表的报告。
     * 输出的目标（例如，控制台或特定文件）和格式通常由 {@code Configuration} 对象决定。
     *
     * @param issues 要包含在报告中的 {@code IssueObject} 列表。
     * @param configuration 包含报告配置（如输出格式、输出文件路径等）的配置对象。
     * @throws IOException 如果在写入报告到文件系统时发生 I/O 错误。
     */
    void generateReport(List<IssueObject> issues, Configuration configuration) throws IOException;

    // 根据需要，可以有更具体的方法，例如：
    // String generateJsonReport(List<IssueObject> issues);
    // void writeHtmlReport(List<IssueObject> issues, Path outputPath) throws IOException;
}
```

**主要交互和输出**:

*   **输入**:
    *   `List<IssueObject>`: 所有阶段收集到的问题。
    *   `Configuration`: 包含报告格式、输出路径等配置。
*   **输出**:
    *   报告被发送到指定的输出（控制台或文件）。此方法本身可能没有直接的返回值（`void`），或者如果设计为返回报告内容的字符串，则返回 `String`。
*   **副作用**:
    *   可能会在文件系统上创建或写入文件。

**错误处理**:
如果在写入报告文件时发生 I/O 错误，应抛出 `IOException`。配置错误（例如，无效的输出格式）应在配置加载阶段或此方法开始时处理。

**注意**:
*   `ReportFormat` 枚举用于明确支持的输出格式。
*   `Configuration` 对象将扮演关键角色，用于传递报告生成的具体参数。
*   对于复杂的报告格式（如 HTML），此组件内部可能需要使用模板引擎或其他库。


### 2.4.6 配置管理器 (ConfigurationManager)

**职责**:
`ConfigurationManager` 组件负责加载、解析、验证和提供整个 linter 工具的配置信息。这些配置信息指导着工具的各个方面，从输入处理到规则执行再到报告生成。

1.  **加载配置源**:
    *   确定配置文件的位置。这可能涉及查找项目根目录下的特定文件（如 `calint.json`, `.calintrc`），或者接受通过命令行参数指定的配置文件路径。
    *   如果未找到用户指定的配置文件，则加载默认配置。
2.  **解析配置**:
    *   读取配置文件内容（通常为 JSON 或 YAML 格式）。
    *   将文件内容解析为内部的配置数据结构 (e.g., a `Configuration` object)。
3.  **验证配置**:
    *   检查配置的有效性，例如确保指定的规则存在，参数类型正确，路径有效等。
    *   报告配置错误。
4.  **合并配置**:
    *   (可选) 如果配置可以来自多个来源（例如，默认配置、用户配置文件、命令行参数），则需要定义合并策略。
5.  **提供配置访问**:
    *   向工具的其他组件提供对已加载和验证的配置信息的访问接口。

**接口/交互模式 (Java - 概念性)**:

```java
package com.polelink.calint.config; // 或 com.polelink.calint.engine;

import issue.com.polelink.calint.IssueObject; // 用于报告配置加载问题

import java.nio.file.Path;
import java.util.List;
import java.util.Optional;

/**
 * 代表整个工具的配置信息。
 * 这是一个数据持有对象 (DTO) 或一组相关 DTOs。
 * 它的具体字段将在设计文档的配置部分详细定义。
 * (这个结构定义也可以放在 core-data-structures.md 中)
 *
 * 示例字段 (概念性):
 * - List<String> enabledRuleIds;
 * - Map<String, Map<String, Object>> ruleParameters; // RuleId -> (ParamName -> ParamValue)
 * - ReportFormat reportFormat;
 * - Optional<Path> reportOutputPath;
 * - List<Path> includePaths;
 * - SeverityLevel defaultSeverity;
 * - Map<String, SeverityLevel> severityOverrides; // RuleId -> SeverityLevel
 */
// @Record (示意)
// public record Configuration(
//     List<String> enabledRuleIds,
//     Map<String, Map<String, Object>> ruleParameters,
//     ReportFormat reportFormat,
//     Optional<Path> reportOutputPath,
//     List<Path> includePaths,
//     // ... 其他配置项
// ) {}


/**
 * 负责加载和管理 calint 工具的配置。
 */
public interface ConfigurationManager {

    /**
     * 加载并解析指定路径的配置文件。
     * 如果 configFile 为空或未找到，则尝试加载默认配置。
     *
     * @param configFile (可选) 配置文件的路径。如果为 {@code Optional.empty()}，
     *                   则会尝试在预定义位置查找配置文件或加载内置默认配置。
     * @return 一个 {@code Configuration} 对象，包含加载和解析后的配置信息。
     * @throws ConfigurationException 如果配置文件存在但格式错误、包含无效数据，
     *                                或在加载过程中发生其他不可恢复的错误。
     *                                此异常应包含详细的错误信息或 {@code IssueObject} 列表。
     */
    Configuration loadConfiguration(Optional<Path> configFile) throws ConfigurationException;

    /**
     * 加载默认配置。
     * 当没有提供用户配置文件时调用。
     *
     * @return 一个包含默认设置的 {@code Configuration} 对象。
     */
    Configuration loadDefaultConfiguration();

    // (可选) 保存当前配置到文件的方法
    // void saveConfiguration(Configuration configuration, Path configFile) throws IOException;

    // (可选) 初始化项目，例如创建一个默认的配置文件
    // void initializeProjectConfiguration(Path projectRoot) throws IOException;
}

/**
 * 自定义异常，用于表示在配置加载或解析过程中发生的错误。
 */
class ConfigurationException extends Exception {
    private final List<IssueObject> issues;

    public ConfigurationException(String message, List<IssueObject> issues) {
        super(message);
        this.issues = issues;
    }

    public ConfigurationException(String message, Throwable cause, List<IssueObject> issues) {
        super(message, cause);
        this.issues = issues;
    }

    public List<IssueObject> getIssues() {
        return issues;
    }
}
```

**主要交互和输出**:

*   **输入**:
    *   (可选) 配置文件的路径。
*   **输出**:
    *   `Configuration` 对象: 一个包含所有配置设置的数据结构。
*   **异常**:
    *   `ConfigurationException`: 如果加载或解析配置失败。

**错误处理**:
配置加载和验证过程中发生的任何问题（如文件未找到但被明确指定、JSON/YAML 格式错误、未知规则ID、无效参数值等）都应通过 `ConfigurationException` 报告，该异常可包含一个 `IssueObject` 列表以提供详细信息。

**注意**:
*   `Configuration` 是一个核心的数据结构，封装了所有可配置的参数。它的具体内容需要详细设计，并可能在 `core-data-structures.md` 中进一步阐述。
*   该模块可能需要与 `rule-metadata.yaml` (在 `SYS-ARCH-004` 中定义) 交互，以验证规则ID和获取规则的默认参数/严重级别。
