好的，这是一个针对您提供的 CAPL 语言 g4 描述文件，开发词法/语法检查工具的详细设计文档。

项目名称: CAPL静态代码分析与质量检查工具 (calint)

文档版本: 1.0

日期: 2025-05-15

# 目录

1. 引言
   1.1 项目背景
   1.2 项目目标
   1.3 设计原则
   1.4 范围
   1.5 读者对象
   1.6 术语与缩写

2. 系统架构
   2.1 总体架构图
   2.2 核心模块说明

3. 模块详细设计
   3.1 输入处理模块 (Input Processor)
   3.2 词法分析模块 (Lexer Module)
   3.3 语法分析模块 (Parser Module)
   3.4 抽象语法树 (AST) 构建模块
   3.5 语义分析与规则检查引擎 (Semantic Analyzer & Rule Engine)
      3.5.1 符号表 (Symbol Table)
      3.5.2 类型检查器 (Type Checker)
      3.5.3 作用域分析器 (Scope Analyzer)
      3.5.4 规则库 (Rule Repository)
      3.5.5 规则执行器 (Rule Executor)
   3.6 报告生成模块 (Report Generator)
   3.7 配置管理模块 (Configuration Manager)

4. 核心数据结构
   4.1 Token (词法单元)
   4.2 AST 节点 (Abstract Syntax Tree Node)
   4.3 符号表条目 (Symbol Table Entry)
   4.4 问题/错误对象 (Issue/Error Object)

5. 检查规则设计 (示例)
   5.1 词法错误
   5.2 语法错误
   5.3 语义错误示例
      5.3.1 变量未声明使用
      5.3.2 类型不匹配
      5.3.3 函数调用参数不匹配
      5.3.4 无效的赋值操作
   5.4 代码风格与最佳实践示例
      5.4.1 命名规范检查
      5.4.2 魔术数字检查
      5.4.3 未使用的变量/函数
      5.4.4 代码复杂度初步评估 (例如，过长的函数)
      5.4.5 on timer 或 on key 等事件函数中避免耗时操作 (提示)

6. 错误处理与报告机制
   6.1 错误分类与级别
   6.2 错误信息格式
   6.3 报告输出格式 (Console, JSON, HTML等)

7. 可扩展性与可配置性
   7.1 规则动态加载与配置
   7.2 插件化架构 (可选，用于未来扩展)
   7.3 代码内规则控制 (In-Code Rule Control)

8. 技术选型
   8.1 编程语言
   8.2 解析器生成器
   8.3 构建工具
   8.4 测试框架

9. 部署与使用
   9.1 命令行接口 (CLI)
   9.2 配置文件

10. 未来展望

11. 非功能性需求
    11.1 性能
    11.2 准确性
    11.3 易用性
    11.4 可维护性

# 1. 引言

## 1.1 项目背景

CAPL (Communication Access Programming Language) 是 Vector 公司为其 CANoe/CANalyzer 等工具开发的专用脚本语言，广泛应用于汽车电子ECU测试和仿真。随着项目复杂度的增加，CAPL 代码量也随之增长，手动进行代码审查和质量保证变得困难且低效。一个自动化的静态代码分析工具能够显著提高 CAPL 代码的质量、可维护性，并减少潜在的运行时错误。

## 1.2 项目目标

- 基于用户提供的 CAPL g4 语法文件，开发一个词法和语法检查工具。
- 实现对 CAPL 代码的静态分析，识别潜在的编码错误、不规范写法和风险点。
- 提供清晰、准确的错误和警告报告，帮助开发者定位和修复问题。
- 提高 CAPL 代码的整体质量和一致性。
- 工具应具备良好的可配置性和一定的可扩展性。

## 1.3 设计原则

- 准确性: 优先保证分析结果的准确性。
- 高效性: 工具应能快速处理较大规模的 CAPL 代码文件。
- 易用性: 提供简洁明了的命令行接口和报告。
- 可配置性: 允许用户根据项目需求定制检查规则和参数。
- 模块化: 各功能模块应低耦合，易于维护和扩展。

## 1.4 范围

- 输入: 单个或多个 .can / .cin 。
- 核心功能:
  - 词法分析 (基于g4)。
  - 语法分析 (基于g4)。
  - 基本的语义分析 (如变量声明、类型检查等)。
  - 可配置的编码规范和最佳实践检查。
- 输出:
  - 错误和警告列表，包含位置信息 (文件名、行号、列号)。
  - 支持多种输出格式 (如控制台文本、JSON)。
- 不包含:
  - 运行时行为分析。
  - 代码自动修复 (初期版本不包含，可作为未来扩展)。
  - 完整的 CAPL 编译器或解释器功能。

## 1.5 读者对象

本文档主要面向项目开发人员、测试人员

## 1.6 术语与缩写

- CAPL: Communication Access Programming Language
- g4: ANTLR v4 语法文件格式
- ANTLR: ANother Tool for Language Recognition (一个强大的解析器生成器)
- AST: Abstract Syntax Tree (抽象语法树)
- CLI: Command Line Interface (命令行界面)
- Linter: 静态代码分析工具的俗称

# 2. 系统架构

## 2.1 总体架构图

```mermaid
graph TD
    EntryPoint[CLI / LSP Gateway] --> ConfigManager{配置管理模块};
    EntryPoint --> InputProcessor{输入处理模块};

    ConfigManager -- 有效配置 --> AnalysisEngine{语义分析与规则检查引擎};
    ConfigManager -.-> UserFeedback_ConfigError[用户 (配置错误)];

    InputProcessor -- 所有相关源文件路径 --> ProjectIndexBuilder[项目索引构建器];
    ProjectIndexBuilder -- 构建 --> ProjectIndexData[(项目索引)];
    
    InputProcessor -- 主文件路径 + Include配置 --> IncludeResolver{Include 解析模块};
    IncludeResolver -- 解析后代码流 + 源映射 --> Lexer{词法分析模块 (ANTLR Lexer)};
    Lexer -- Token序列 --> Parser{语法分析模块 (ANTLR Parser)};
    Parser -- Parse Tree --> ASTBuilder{AST 构建模块};
    ASTBuilder -- 自定义AST --> AnalysisEngine;
    
    ProjectIndexData -- 项目级符号/类型信息 --> AnalysisEngine;
    
    AnalysisEngine -- 执行规则, 管理符号表, 类型检查 --> AnalysisEngine;
    AnalysisEngine -- 问题列表 --> ReportGenerator{报告生成模块};
    ReportGenerator -- 报告 --> UserFeedback_AnalysisResult[用户 (分析结果)];

    %% Styling (optional, for better readability if rendered tool supports it)
    classDef entrypointStyle fill:#f9f,stroke:#333,stroke-width:2px;
    classDef configModuleStyle fill:#ccf,stroke:#333,stroke-width:2px;
    classDef inputModuleStyle fill:#cfc,stroke:#333,stroke-width:2px;
    classDef coreModuleStyle fill:#fcc,stroke:#333,stroke-width:2px;
    classDef outputDestStyle fill:#ffc,stroke:#333,stroke-width:2px;
    classDef dataStoreStyle fill:#eef,stroke:#333,stroke-width:1px,color:#000,font-style:italic;

    class EntryPoint entrypointStyle;
    class ConfigManager configModuleStyle;
    class InputProcessor inputModuleStyle;
    class ProjectIndexBuilder,IncludeResolver,Lexer,Parser,ASTBuilder,AnalysisEngine,ReportGenerator coreModuleStyle;
    class UserFeedback_ConfigError,UserFeedback_AnalysisResult outputDestStyle;
    class ProjectIndexData dataStoreStyle;
```

## 2.2 核心模块说明

- **CLI / LSP Gateway (EntryPoint)**: 系统的主要入口点。负责解析命令行参数或处理来自语言服务器协议 (LSP) 的请求。它初始化并协调后续的核心模块，如配置管理和输入处理。

- **配置管理模块 (ConfigManager)**: 在分析流程开始前被调用。负责加载和解析配置文件 (如 `calint-config.yaml`, `rule-metadata.yaml`)。校验配置的有效性，如果存在问题则直接向用户报告。为后续的分析引擎提供有效的配置对象，包括启用的规则、规则参数、严重级别等。

- **输入处理模块 (InputProcessor)**: 负责根据入口指令 (如文件路径、目录) 收集所有相关的 CAPL 源文件 (.can, .cin)。它将收集到的完整文件列表传递给项目索引构建器，并将指定的主分析文件路径及 Include 搜索路径等配置传递给 Include 解析模块。

- **Include 解析模块 (IncludeResolver)**: 接收主文件路径和 Include 配置。负责处理源文件中的 `#include` 指令，递归地读取并合并被包含文件的内容。生成一个统一的、连续的代码字符流，并维护原始文件与合并后代码流之间的位置映射关系 (Source Map)，以便后续能准确定位问题。处理 `#pragma library` 指令，将其信息传递给项目索引或符号表系统。该模块的输出是传递给词法分析器的实际代码内容。

- **项目索引构建器 (ProjectIndexBuilder)**: 作为一个预处理步骤或语义分析引擎的早期阶段，负责构建项目范围的符号概览。它接收来自输入处理模块的所有相关源文件路径，对这些文件进行初步解析（可能仅识别顶层声明如全局变量、函数、消息、`#pragma library`等），并将这些信息汇集到项目索引中。

- **项目索引 (ProjectIndexData)**: (数据存储) 作为项目全局符号上下文的快照，存储由项目索引构建器收集的顶层符号信息、类型定义和库声明。它使得在对单个文件进行详细分析时，能够解析对其他文件中符号的引用。

- **词法分析模块 (Lexer)**: 由 ANTLR 根据 g4 语法文件自动生成。接收来自 Include 解析模块的统一代码字符流，将其转换为 Token 序列。

- **语法分析模块 (Parser)**: 由 ANTLR 根据 g4 语法文件自动生成。接收来自词法分析模块的 Token 序列，构建 Parse Tree (解析树)。ANTLR 本身会报告语法错误。

- **AST 构建模块 (ASTBuilder)**: 将 ANTLR 生成的 Parse Tree 转换为更适合分析和遍历的自定义抽象语法树 (AST)。自定义 AST 节点将包含源码位置、节点类型及其他语义分析所需的信息。本项目将采用自定义 AST。

- **语义分析与规则检查引擎 (AnalysisEngine)**: 系统的核心分析部件。它接收来自配置管理模块的有效配置、来自 AST 构建模块的自定义 AST，并利用项目索引 (ProjectIndexData) 作为全局符号上下文。主要职责包括：
  - **符号表构建与语义分析**: 在遍历单个文件的 AST 时，构建详细的、具有层级作用域的符号表。此符号表会结合项目索引中的全局信息来解析外部引用、检查类型兼容性以及变量声明与使用等。
  - **规则执行**: 根据加载的配置，执行启用的代码质量、风格及语义规则。规则的执行会依赖于 AST、符号表及类型信息。
  - **问题收集**: 将检查过程中发现的问题（包含位置、描述、严重性、规则ID等）收集起来，传递给报告生成模块。

- **报告生成模块 (ReportGenerator)**: 接收来自语义分析与规则检查引擎的问题列表。根据用户配置（或默认设置），将这些问题格式化为指定的输出形式 (如控制台文本、JSON 文件、HTML 报告等) 并呈现给用户。

## 2.3 核心数据结构定义

[核心数据结构的详细定义请参见](./core-data-structures.md)

## 2.4 核心组件接口定义 (Core Component Interface Definitions)

[核心组件接口的详细定义请参见](./core-component-interfaces.md)
# 3. 模块详细设计

## 3.1 输入处理模块 (Input Processor)

- 功能:
  - 接收命令行参数指定的文件路径或目录。
  - 如果是目录，则递归查找指定扩展名的 CAPL 文件 (如 .can, .cin)。
  - 读取文件内容为字符串，传递给词法分析器。
  - 处理文件读取过程中的 IO 异常。
- 接口:
  - process(paths: List[str]) -> List[FileContent]

## 3.2 词法分析模块 (Lexer Module)

- 功能:
  - 使用 ANTLR 从 g4 文件生成的 Lexer 类。
  - 输入为 CAPL 源代码字符串。
  - 输出为 Token 流。
  - 自动处理词法错误（如非法字符），ANTLR 会提供错误监听机制。
- 主要组件: YourCaplLexer.g4 (用户提供) -> ANTLR Tool -> YourCaplLexer.java / YourCaplLexer.py (根据选择的语言)

## 3.3 语法分析模块 (Parser Module)

- 功能:
  - 使用 ANTLR 从 g4 文件生成的 Parser 类。
  - 输入为词法分析器生成的 Token 流。
  - 输出为 Parse Tree。
  - 自动处理语法错误，ANTLR 提供错误监听机制，可以捕获并报告语法错误。为了提升 Linter 在面对不规范代码时的分析能力，应重视解析器的错误恢复机制，可能需要通过自定义 ANTLR 错误策略（ErrorStrategy）来增强其在遇到语法错误后继续解析并报告后续问题的能力。
- 主要组件: YourCaplParser.g4 (用户提供) -> ANTLR Tool -> YourCaplParser.java / YourCaplParser.py

## 3.4 抽象语法树 (AST) 构建模块

- 功能:
  - 构建一个自定义的、更简洁、更易于分析的 AST。自定义 AST 节点可以携带更丰富的语义信息，并去除纯粹的语法结构节点 (如括号)。
- 实现:
  - 可以实现 ANTLR 提供的 Listener 或 Visitor 模式来遍历 Parse Tree。
  - 每个 AST 节点应包含节点类型、源代码位置 (行、列)、子节点等信息。

## 3.5 语义分析与规则检查引擎 (Semantic Analyzer & Rule Engine)

语义分析过程通常涉及多个遍历（Passes）AST。例如，首先进行“声明收集遍”以填充符号表，随后进行“名称解析与类型检查遍”以验证符号使用和类型兼容性。规则检查可以在这些遍历过程中或之后进行。
这是工具的核心。它通常通过遍历 AST (或 Parse Tree) 来工作。

### 3.5.1 符号表 (Symbol Table)
* **功能:** 存储代码中声明的标识符（变量、函数、参数、自定义类型等）及其属性（类型、作用域、声明位置等）。
* **结构:** 通常是一个栈式结构或树状结构，以支持嵌套作用域。每个作用域有一个独立的符号表。
* **操作:**
    * `enterScope()`: 进入新的作用域。
    * `exitScope()`: 退出当前作用域。
    * `addSymbol(name, type, properties)`: 在当前作用域添加符号。
    * `lookupSymbol(name)`: 从当前作用域开始向上查找符号。

### 3.5.2 类型检查器 (Type Checker)
* **功能:** 遍历 AST，检查表达式、赋值语句、函数调用等操作中的类型兼容性。
* **依赖:** 符号表 (获取变量/函数类型)、预定义的 CAPL 内置类型和操作符规则。
* **示例:**
    * 赋值语句 `a = b;` -> 检查 `a` 和 `b` 的类型是否兼容。
    * 表达式 `x + y` -> 检查 `x` 和 `y` 是否支持 `+` 操作，并推断结果类型。

### 3.5.3 作用域分析器 (Scope Analyzer)
* **功能:** 结合符号表，分析标识符的声明和使用是否符合作用域规则。
* **示例:**
    * 检查变量是否在使用前已声明。
    * 检查是否有重复声明。

### 3.5.4 规则库 (Rule Repository)
* **功能:** 存储所有可执行的检查规则。每个规则是一个独立的检查逻辑单元。
* **结构:** 可以是一个规则对象的列表或字典。每个规则对象应包含：
    * 规则 ID (唯一标识)。
    * 规则描述。
    * 规则严重级别 (Error, Warning, Info)。
    * 检查逻辑 (通常是一个函数或方法，以 AST 节点为输入)。
    * 是否默认启用。
    * 可配置参数 (如命名规范的正则表达式)。
* **实现:** 规则可以硬编码，也可以通过插件形式动态加载。

### 3.5.5 规则执行器 (Rule Executor)
* **功能:**
    * 根据配置管理模块加载启用的规则。
    * 通常使用 Visitor 模式遍历 AST。
    * 对于 AST 中的每个相关节点，调用适用的已启用规则进行检查。
    * 收集规则检查产生的问题。
* **流程:**
    1.  初始化符号表。
    2.  遍历 AST (通常是深度优先)。
    3.  进入特定节点 (如函数声明、变量声明、表达式) 时：
        * 更新符号表 (如进入新作用域，添加符号)。
        * 执行与该节点类型相关的语义检查 (类型检查、作用域检查)。
        * 调用已启用的、适用于该节点的规则。
    4.  离开特定节点时：
        * 更新符号表 (如退出作用域)。

## 3.6 报告生成模块 (Report Generator)

- 功能:
  - 接收来自语法分析器和规则检查引擎的问题列表。
  - 根据用户配置的格式输出报告。
- 支持格式:
  - Console: [严重级别] 文件名:行号:列号 - 规则ID - 描述信息
  - JSON: 结构化的数据，方便其他工具集成。
```
[
  {
    "file": "path/to/file.can",
    "line": 10,
    "column": 5,
    "severity": "ERROR",
    "ruleId": "VAR_UNDECLARED",
    "message": "Variable 'myVar' is used before declaration."
  },
  // ... more issues
]
```
  - HTML: (可选) 生成更友好的可视化报告。

## 3.7 配置管理模块 (Configuration Manager)

- 功能:
  - 读取配置文件 (如 calint.json 或 .calintc)。
  - 解析配置，包括：
    - 要检查的文件/目录模式。
    - 启用的规则列表。
    - 单个规则的参数 (如命名规范的正则表达式、最大行长等)。
    - 报告输出格式。
- 配置文件格式: 推荐使用 JSON 或 YAML。
```
// 示例 calint.json
{
  "extends": [], // 可以继承其他配置文件
  "files": ["**/*.can", "**/*.cin"],
  "outputFormat": "console", // "json", "html"
  "rules": {
    "CALINT-VAR_UNDECLARED": "error", // "error", "warning", "off"
    "CALINT-TYPE_MISMATCH": "error",
    "CALINT-NAMING_CONVENTION_VAR": ["warning", {"pattern": "^g[A-Z][a-zA-Z0-9]*$" }], // 全局变量以g开头
    "CALINT-MAGIC_NUMBER": "warning",
    "CALINT-UNUSED_VARIABLE": "info"
  }
}
```

# 4. 核心数据结构

## 4.1 Token (词法单元)

由 ANTLR 自动定义和生成。通常包含：
- type: Token 类型 (如 IDENTIFIER, KEYWORD_INT, OPERATOR_PLUS)。
- text: Token 的文本值。
- line: 源代码中的行号。
- column: 源代码中的列号。
- channel: (如 HIDDEN 用于空白和注释)。

## 4.2 AST 节点 (Abstract Syntax Tree Node)

基类/接口 AstNode:
- astNodeType: 枚举类型，表示节点类型 (如 VariableDeclaration, FunctionCall, IfStatement, Expression)。
- location: SourceLocation { file: str, startLine: int, startColumn: int, endLine: int, endColumn: int }
- parent: AstNode (父节点引用)
- children: List[AstNode] (子节点列表)
- accept(visitor: AstVisitor): 用于 Visitor 模式遍历。
  - astNodeLink: Object (可选，对 ANTLR ParseTree 节点或 Token 的引用，便于精确溯源和高级分析)
  - scopeRef: Scope (可选，指向此节点所属的作用域对象，以简化作用域查找)

具体节点类继承 AstNode 并添加特定属性:
- VariableDeclarationNode: variableName: str, variableType: Type, initialValue: AstNode?
- FunctionCallNode: functionName: str, arguments: List[AstNode]
- IdentifierNode: name: str
- LiteralNode: value: any, literalType: Type

## 4.3 符号表条目 (Symbol Table Entry)

- name: str (标识符名称)
- type: Type (标识符的类型信息，可以是自定义的类型对象/枚举)
- kind: SymbolKind (如 VARIABLE, FUNCTION, PARAMETER, MESSAGE, TIMER)
- scope: Scope (指向其所在作用域对象的引用)
- declarationNode: AstNode (指向声明该符号的 AST 节点)
- properties: Dict (其他属性，如是否已使用、是否为常量等)

## 4.4 问题/错误对象 (Issue/Error Object)

- file: str (文件名)
- line: int (行号)
- column: int (列号)
- severity: SeverityLevel (枚举: ERROR, WARNING, INFO)
- ruleId: str (产生此问题的规则 ID)
- message: str (描述信息)

# 5. 检查规则设计 (示例)

## 5.1 词法错误

- 由 ANTLR Lexer 自动捕获，如非法字符。
- 报告: 直接由 ANTLR 的错误监听器报告。

## 5.2 语法错误

- 由 ANTLR Parser 自动捕获，如缺少分号、括号不匹配等。
- 报告: 直接由 ANTLR 的错误监听器报告。

## 5.3 语义错误示例 (需要遍历 AST 和符号表)

### 5.3.1 变量未声明使用 (CALINT-VAR_UNDECLARED)
* 检查: 在访问一个标识符节点时，查询符号表看是否存在该标识符的声明。
* 级别: ERROR

### 5.3.2 类型不匹配 (CALINT-TYPE_MISMATCH)
* 检查: 在赋值语句、二元/一元表达式、函数调用参数传递时，检查操作数或参数的类型是否与期望类型兼容。
* 级别: ERROR

### 5.3.3 函数调用参数不匹配 (CALINT-FUNC_ARG_MISMATCH)
* 检查: 函数调用时，检查传递的参数数量和类型是否与函数定义匹配。
* 级别: ERROR

### 5.3.4 无效的赋值操作 (CALINT-INVALID_ASSIGNMENT)
* 检查: 例如，不能给常量或函数名赋值。
* 级别: ERROR

## 5.4 代码风格与最佳实践示例

### 5.4.1 命名规范检查 (CALINT-NAMING_CONVENTION)
* 检查: 检查变量名、函数名、消息名等是否符合预设的正则表达式模式 (可配置)。
* 级别: WARNING/INFO
* 参数: {"variablePattern": "^[a-z][a-zA-Z0-9]*$", "functionPattern": "^[A-Z][a-zA-Z0-9]*$"}

### 5.4.2 魔术数字检查 (CALINT-MAGIC_NUMBER)
* 检查: 查找代码中直接使用的未命名的数字常量 (排除一些常见数字如 0, 1, -1)。
* 级别: WARNING
* 参数: {"allowedNumbers": [0, 1, -1, 100]}

### 5.4.3 未使用的变量/函数 (CALINT-UNUSED_ENTITY)
* 检查: 通过符号表跟踪变量/函数的声明和使用情况。在分析结束时，报告那些被声明但从未被使用的实体。
* 级别: WARNING/INFO

### 5.4.4 代码复杂度初步评估 (CALINT-FUNC_COMPLEXITY_HIGH)
* 检查: 例如，函数体内的语句数量超过阈值，或者嵌套层级过深。
* 级别: WARNING
* 参数: {"maxStatements": 50, "maxNestingDepth": 4}

### 5.4.5 事件函数中避免耗时操作 (CALINT-ON_EVENT_LONG_OP)
* 检查: 识别 on timer, on key, on message 等事件处理函数中是否存在已知的可能导致阻塞或长时间执行的函数调用或循环结构。这可能需要一个已知耗时操作的列表。
* 级别: WARNING (这更偏向于启发式规则)

# 6. 错误处理与报告机制

## 6.1 错误分类与级别

- ERROR: 严重的语法或语义错误，很可能导致 CAPL 脚本编译失败或运行时异常。
- WARNING: 代码中潜在的问题、不好的实践或不符合规范的写法，可能不会立即导致错误，但影响可读性、可维护性或存在潜在风险。
- INFO: 代码改进建议，不影响功能但有助于提升代码质量。

## 6.2 错误信息格式
每条错误/警告信息应包含：

- 文件名
- 行号
- 列号
- 严重级别
- 规则ID (方便用户查询规则详情或配置)
- 清晰的描述性消息，解释问题所在以及可能的修复建议。

## 6.3 报告输出格式 (Console, JSON, HTML等)

- Console: 默认格式，易于阅读。
- JSON: 方便机器解析和集成到其他工具链 (如 CI/CD)。
- HTML: (可选) 生成交互式的报告，可以包含代码片段高亮等。

# 7. 可扩展性与可配置性

## 7.1 规则动态加载与配置

- 规则的启用/禁用、严重级别调整、参数设置应通过外部配置文件 (如 JSON/YAML) 进行管理。
- 工具启动时加载配置文件，并据此构建活动的规则集。

## 7.2 插件化架构 (可选，用于未来扩展)

- 允许用户或第三方开发者编写自定义规则插件。
- 可以定义清晰的规则插件接口 (例如，一个继承自特定基类的类，实现 check(node: AstNode, context: AnalysisContext) 方法)。
- 工具可以动态发现并加载指定目录下的插件。

## 7.3 代码内规则控制 (In-Code Rule Control)

- **功能**: 允许开发者在源代码中通过特定格式的注释来临时禁用或启用某些规则的检查。
- **机制**:
  - 单行禁用: 例如 `// calint-disable-next-line RULE_ID_1, RULE_ID_2`
  - 块级禁用/启用: 例如 `/* calint-disable RULE_ID_1 */ ... code ... /* calint-enable RULE_ID_1 */`
- **优点**: 提高了灵活性，便于处理特定代码片段的例外情况或逐步引入 Linter 到现有项目中。
- **实现**: 需要在规则执行器遍历 AST 时，检查与当前节点/行相关的注释，并据此跳过相应规则的执行。

# 8. 技术选型

## 8.1 编程语言

- Java 11

## 8.2 解析器生成器

- ANTLR v4: 用户已提供 g4 文件，这是必然选择。

## 8.3 构建工具

- Java: Maven 

## 8.4 测试框架

- Java: JUnit 

# 9. 部署与使用

## 9.1 命令行接口 (CLI)

- 基本用法: calint <file_or_directory> [options]
- 选项示例:
  - --config <path_to_config_file>: 指定配置文件。
  - --format <console|json|html>: 指定输出格式。
  - --output <output_file_path>: 将报告输出到文件。
  - --rules <rule1,rule2...>: 仅运行指定的规则。
  - --severity <ERROR|WARNING|INFO>: 仅报告指定级别及以上的问题。
  - --init: 生成一个默认的配置文件。
  - --help: 显示帮助信息。

## 9.2 配置文件

- 工具应能自动查找项目根目录下的标准配置文件名 (如 .calintc, calint.json, pyproject.toml 中的特定节)。
- 用户可以在配置文件中覆盖规则的默认严重级别，例如将某个 `WARNING` 级别的规则提升为 `ERROR`，或将某个规则设置为 `off` 以禁用它。

# 10. 未来展望

- 更高级的语义分析: 数据流分析 (如检测未初始化变量的实际使用路径)、控制流分析。
- 自动修复 (Auto-fixing): 对某些简单问题 (如格式化、部分命名规范) 提供自动修复建议或能力。
- IDE 集成: 开发 LSP (Language Server Protocol) 服务器，以便集成到 VS Code、Eclipse 等IDE中，提供实时检查。
- 复杂度度量: 集成更复杂的代码度量标准，如圈复杂度。
- CAPL特定库函数检查: 检查Vector特定库函数的使用是否正确 (如参数个数、类型)。
- 与其他工具集成: 如 SonarQube。

# 11. 非功能性需求

## 11.1 性能

- 对于中等规模的 CAPL 项目 (如数万行代码)，分析时间应在可接受范围内 (例如，秒级或分钟级)。
- 内存占用应合理。

## 11.2 准确性

- 误报率 (False Positives) 和漏报率 (False Negatives) 应尽可能低。

## 11.3 易用性

- CLI 命令简单直观。
- 错误报告清晰易懂，定位准确。
- 配置过程简单。

## 11.4 可维护性

- 代码结构清晰，模块化设计。
- 有良好的注释和文档。
- 易于添加新规则或修改现有规则。
- 包含单元测试和集成测试。

# 12. 补充说明

## 12.1 Include/依赖处理策略

- 新增 **Include 解析模块 (Include Resolver)**，位于 *输入处理模块* 之后。  
- 负责解析 `includes`、`#pragma library` 等指令，按照以下顺序搜索文件：
  1. CLI `--include-path` 指定目录  
  2. 当前文件所在目录  
  3. 项目根目录下的 `include/`  
- 支持循环包含检测并给出 ERROR 级别提示。  
- 解析完成后把所有源文件加入 **ProjectIndex**（见 12.2）。
- **`#pragma library` 处理**: 对于 `#pragma library "libname.cbl"`，Linter 主要识别该指令并记录库依赖。由于 `.cbl` 是已编译文件，Linter 不会尝试解析其内部。但如果项目约定了对应的符号声明文件（如 `libname.cin` 或特定格式的头文件），`Include Resolver` 可尝试查找并解析这些声明文件，以便在 `ProjectIndex` 中包含这些库暴露的符号，从而支持对库函数/变量使用的检查。
- **其他预处理指令**: 除了 `includes` 和 `#pragma library`，需要调研 CAPL 是否存在其他常见的预处理指令（如条件编译 `#ifdef`，宏定义 `#define`）。如果存在且影响语法解析，`Include Resolver` 或一个专门的预处理步骤需要能够识别并适当处理它们，例如通过文本替换或条件性地包含/排除代码段，然后再送入词法分析器。

## 12.2 ProjectIndex（跨文件符号索引）

- 维护整个项目级别的符号表快照，支持跨文件查找与增量分析。  
- 主要接口  
  - `addCompilationUnit(file, symbolTable)`  
  - `lookup(name) -> Symbol?`  
- 触发条件：当文件变更时间戳更新或哈希变化时重新分析。
- **存储粒度**: 在设计 `ProjectIndex` 时，需权衡存储内容的粒度。存储完整的 ASTs 便于深度分析但可能消耗较多内存；仅存储符号信息（名称、类型、位置、声明节点引用等）则更节省内存，AST 可在需要时按需为单个文件解析生成。

## 12.3 类型系统补充

- 基础类型：`char`, `byte`, `word`, `dword`, `long`, `float`, `double`, `message`, `msTimer`, `time`, `char[]`。  
- 复合类型：`struct`, `enum`。  
- 隐式转换：同 Java 宽化规则 + CAPL 特有 `byte → word → dword → long`。  
- 不允许浮点与 message 互转，违规时报 `TYPE_MISMATCH`。

## 12.4 RuleId 命名约定

- 格式：`CALINT-{CATEGORY}-{NNN}`  
  - CATEGORY 取值：`E`(Error), `W`(Warning), `I`(Info)  
  - NNN 为三位递增数字  
  - 例如：`CALINT-E001` 变量未声明；`CALINT-W015` 魔术数字  
- 保存在 `rule-metadata.yaml` 统一管理，CLI `--list-rules` 输出完整清单。该文件除了管理 RuleId 和描述外，还应定义每个规则的默认严重级别 (如 `ERROR`, `WARNING`, `INFO`)。

## 12.5 性能与并发

- 文件级并行：使用 Java *ForkJoinPool* 按文件划分任务。  
- 内存上限通过 `-Xmx` 参数配置，默认 2 GB；超限时任务队列退避。

## 12.6 测试与 CI

- 单元测试：JUnit 5，覆盖率 ≥ 80 %。  
- 集成测试：对 `examples/` 下项目运行并对比基线 JSON。  
- 若检测到 ERROR 级别问题，任务返回非 0 退出码以阻断合并。