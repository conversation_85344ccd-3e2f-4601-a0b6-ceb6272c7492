# CAPL 统一预处理机制设计文档

## 1. 概述

本文档描述了 CAPL Linter 的统一预处理机制设计，专为静态分析优化，旨在解决 CLI 和 LSP 输入不一致、#include 预处理缺失、宏处理文件上下文以及错误定位不准确等问题。

### 1.1 设计目标

- **统一性**: CLI 和 LSP 使用相同的预处理逻辑，确保分析结果一致
- **静态分析优化**: 专为代码质量检查设计，保留所有代码路径用于全面分析
- **文件上下文准确性**: 宏展开保持原始文件上下文，确保文件名和行号的准确性
- **精确源映射**: 提供精确的源映射和错误定位
- **架构简洁**: 移除编译器相关处理，专注于静态分析核心需求

### 1.2 核心问题解决

#### **静态分析优化**
- 移除条件编译处理：静态分析应检查所有代码路径，而非特定编译条件下的代码
- 移除pragma处理：编译器指令与静态分析无关
- 专注于代码结构和内容的完整性

#### **宏展开文件上下文**
- 宏展开在include处理之前：保持原始文件的上下文信息
- 文件相关宏（%FILE_NAME%、%LINE_NUMBER%）显示正确的原始文件信息
- 避免复杂的源映射回溯机制

#### **多文件错误定位**
- 构建完整的源映射系统
- 将分析错误精确映射回原始文件
- 支持 LSP 多文件错误发布

#### **CAPL 语言特性支持**
- 支持 .capl, .can, .cin 三种文件扩展名
- 处理 CAPL 特有的预定义宏
- 智能循环依赖处理

## 2. 统一架构设计

### 2.1 整体架构

```mermaid
flowchart LR
    %% 输入层
    subgraph INPUT ["输入层"]
        direction TB
        CLI[CLI 文件列表]
        LSP[LSP 文档内容]
        TEST[测试输入]
    end

    %% 虚拟文件系统层
    subgraph VFS_LAYER ["虚拟文件系统层"]
        direction TB
        VFS[VirtualFileSystem<br/>统一接口]
        PFS[PhysicalFileSystem]
        LSPFS[LspFileSystem]
        MEMFS[MemoryFileSystem]

        VFS --- PFS
        VFS --- LSPFS
        VFS --- MEMFS
    end

    %% 预处理层
    subgraph PREPROCESS ["预处理层"]
        direction TB
        AU[AnalysisUnit<br/>分析单元]
        PP[PreprocessorPipeline<br/>预处理管道]

        subgraph STAGES ["处理阶段"]
            MES[宏展开]
            IRS[Include解析]
            MES --> IRS
        end

        AU --> PP
        PP --> STAGES
    end

    %% 分析层
    subgraph ANALYSIS ["分析层"]
        direction TB
        UAE[UnifiedAnalysisEngine<br/>统一分析引擎]
        LE[LinterEngine<br/>现有引擎]
        SM[SourceMap<br/>源映射]

        UAE --> LE
        UAE --> SM
    end

    %% 输出层
    subgraph OUTPUT ["输出层"]
        direction TB
        IM[IssueMapper<br/>错误映射]
        CLI_OUT[CLI 输出]
        LSP_OUT[LSP 输出]
        TEST_OUT[测试输出]

        IM --> CLI_OUT
        IM --> LSP_OUT
        IM --> TEST_OUT
    end

    %% 主流程连接
    INPUT --> VFS_LAYER
    VFS_LAYER --> PREPROCESS
    PREPROCESS --> ANALYSIS
    ANALYSIS --> OUTPUT

    %% 源映射流程
    MES -.-> SM
    IRS -.-> SM
    SM -.-> IM

    style INPUT fill:#e1f5fe
    style VFS_LAYER fill:#e8f5e8
    style PREPROCESS fill:#fff3e0
    style ANALYSIS fill:#f3e5f5
    style OUTPUT fill:#e3f2fd
```

### 2.2 设计原则

#### **静态分析优化原则**
- 专为代码质量检查设计，保留所有代码路径
- 移除编译器相关处理（条件编译、pragma），专注于代码结构分析
- 优化预处理顺序以保持文件上下文准确性

#### **统一性原则**
- CLI 和 LSP 使用相同的预处理逻辑和分析引擎
- 通过 VFS 抽象消除文件访问差异
- 统一的错误处理和映射机制

#### **分层架构原则**
- **输入层**: 处理不同来源的输入（CLI/LSP/测试）
- **VFS层**: 提供统一的文件访问抽象
- **预处理层**: 执行宏展开和文件合并操作
- **分析层**: 进行语法和语义分析
- **输出层**: 处理结果输出和错误映射

#### **简洁性原则**
- 最小化预处理阶段，只保留静态分析必需的处理
- 避免过度设计，专注于核心功能
- 清晰的组件职责分离

### 2.3 核心组件

#### 2.3.1 虚拟文件系统 (VFS)

```mermaid
classDiagram
    class VirtualFileSystem {
        <<interface>>
        +readFile(uri: String) String
        +exists(uri: String) boolean
        +listFiles(directoryUri: String) List~String~
        +getLastModified(uri: String) long
    }

    class PhysicalFileSystem {
        +readFile(uri: String) String
        +exists(uri: String) boolean
        +listFiles(directoryUri: String) List~String~
        -uriToPath(uri: String) Path
    }

    class LspFileSystem {
        -openDocuments: Map~String, String~
        -fallback: PhysicalFileSystem
        +readFile(uri: String) String
        +exists(uri: String) boolean
        +updateDocument(uri: String, content: String)
    }

    VirtualFileSystem <|.. PhysicalFileSystem
    VirtualFileSystem <|.. LspFileSystem
    LspFileSystem --> PhysicalFileSystem : fallback
```

**VFS 统一访问策略**:

```mermaid
flowchart TD
    A[文件访问请求] --> B{环境类型}
    B -->|CLI| C[PhysicalFileSystem]
    B -->|LSP| D[LspFileSystem]

    C --> C1[直接读取文件系统]
    D --> D1{文档是否打开?}
    D1 -->|是| D2[读取内存文档]
    D1 -->|否| D3[回退到文件系统]

    C1 --> E[返回文件内容]
    D2 --> E
    D3 --> E

    style C fill:#e1f5fe
    style D fill:#e8f5e8
```

#### 2.3.2 分析单元 (AnalysisUnit)

```mermaid
classDiagram
    class AnalysisUnit {
        -primaryUri: String
        -documents: Map~String, VirtualDocument~
        -scope: AnalysisScope
        -context: PreprocessorContext
        +fromFiles(files: List~File~, config: AppConfiguration)$ AnalysisUnit
        +fromLspDocument(uri: String, content: String, workspace: WorkspaceContext)$ AnalysisUnit
        +fromWorkspace(workspace: WorkspaceContext, config: AppConfiguration)$ AnalysisUnit
        +getPrimaryDocument() VirtualDocument
        +getAllDocuments() Map~String, VirtualDocument~
        +getScope() AnalysisScope
        +isWorkspaceAnalysis() boolean
    }

    class AnalysisScope {
        <<enumeration>>
        SINGLE_FILE
        PROJECT
        WORKSPACE
    }

    class VirtualDocument {
        -uri: String
        -content: String
        -lastModified: long
        -source: DocumentSource
        +getUri() String
        +getContent() String
        +getLastModified() long
        +getSource() DocumentSource
    }

    class PreprocessorContext {
        -includePaths: List~String~
        -defines: Map~String, String~
        -workspaceRoot: String
        -vfs: VirtualFileSystem
        +getIncludePaths() List~String~
        +getDefines() Map~String, String~
        +getVirtualFileSystem() VirtualFileSystem
    }

    AnalysisUnit --> AnalysisScope
    AnalysisUnit --> VirtualDocument
    AnalysisUnit --> PreprocessorContext
```

**分析单元创建流程**:

```mermaid
flowchart TD
    A[输入源] --> B{输入类型}

    B -->|CLI 文件列表| C[fromFiles]
    B -->|LSP 单文档| D[fromLspDocument]
    B -->|LSP Workspace| E[fromWorkspace]

    C --> C1[转换 File 为 URI]
    C1 --> C2[创建 VirtualDocument]
    C2 --> C3[设置 PROJECT 范围]
    C3 --> C4[配置 CLI 上下文]

    D --> D1[提取文档内容]
    D1 --> D2[创建 VirtualDocument]
    D2 --> D3[设置 SINGLE_FILE 范围]
    D3 --> D4[配置 LSP 上下文]

    E --> E1[扫描 Workspace 文件]
    E1 --> E2[创建 VirtualDocument 集合]
    E2 --> E3[设置 WORKSPACE 范围]
    E3 --> E4[配置 Workspace 上下文]

    C4 --> F[创建 AnalysisUnit]
    D4 --> F
    E4 --> F

    style C fill:#e1f5fe
    style D fill:#e8f5e8
    style E fill:#fff3e0
```

### 2.4 完整流程时序图

以下UML顺序图展示了CAPL预处理的完整流程，从用户输入到最终输出的每一个步骤：

```mermaid
sequenceDiagram
    participant User as 用户/IDE
    participant CLI as CalintCli/LSP
    participant WS as CaplWorkspaceService
    participant Scanner as WorkspaceFileScanner
    participant Strategy as WorkspaceAnalysisStrategy
    participant UAE as UnifiedAnalysisEngine
    participant AU as AnalysisUnit
    participant VFS as VirtualFileSystem
    participant PP as PreprocessorPipeline
    participant MES as MacroExpansionStage
    participant IRS as IncludeResolutionStage
    participant SDR as SmartDependencyResolver
    participant TM as TextMerger
    participant SM as SourceMapBuilder
    participant IM as IssueMapper
    participant LE as LinterEngine
    participant LDP as LspDiagnosticsPublisher
    participant LS as LanguageServer

    Note over User,LE: CAPL 完整分析流程 (预处理 + 语法语义分析)

    %% 1. 初始化阶段
    rect rgb(225, 245, 254)
        Note over User,AU: 阶段1: 初始化和输入处理
        User->>CLI: 请求分析 (文件路径/文档内容/Workspace命令)
        CLI->>CLI: 创建VFS (Physical/Lsp)

        alt CLI 文件列表模式
            CLI->>AU: fromFiles() 创建分析单元
            AU->>AU: 设置PROJECT范围
        else LSP 单文档模式
            CLI->>AU: fromLspDocument() 创建分析单元
            AU->>AU: 设置SINGLE_FILE范围
        else LSP Workspace模式
            CLI->>WS: 处理Workspace分析请求
            WS->>Scanner: scanWorkspace() 扫描文件
            Scanner-->>WS: 返回CAPL文件列表
            WS->>Strategy: prepareWorkspaceAnalysis()
            Strategy->>AU: fromWorkspace() 创建分析单元
            AU->>AU: 设置WORKSPACE范围
            Strategy-->>WS: 返回AnalysisUnit
            WS-->>CLI: 返回AnalysisUnit
        end

        AU->>AU: 初始化VirtualDocument集合
        AU->>AU: 创建PreprocessorContext
        AU-->>CLI: 返回完整的AnalysisUnit
    end

    %% 2. 预处理管道启动
    rect rgb(232, 245, 233)
        Note over CLI,PP: 阶段2: 预处理管道启动
        CLI->>UAE: analyze(analysisUnit)
        UAE->>UAE: 提取主文档和上下文
        UAE->>PP: process(primaryDocument, context)
        PP->>PP: 初始化处理阶段
        PP->>SM: 初始化源映射构建器
    end

    %% 3. 宏展开处理
    rect rgb(255, 243, 224)
        Note over PP,MES: 阶段3: 宏展开处理
        PP->>MES: process(content, context)
        MES->>MES: 查找 %MACRO% 模式
        loop 对每个宏引用
            MES->>MES: 验证宏是否定义
            alt 宏已定义
                MES->>MES: 替换为预定义值
                MES->>SM: 记录宏展开映射
            else 宏未定义
                MES->>IM: 添加宏未定义错误
            end
        end
        MES-->>PP: 宏展开完成
    end

    %% 4. Include 解析和依赖处理
    rect rgb(243, 229, 245)
        Note over PP,TM: 阶段4: Include 解析和智能依赖处理
        PP->>IRS: process(content, context)
        IRS->>IRS: 查找 #include 指令
        IRS->>SDR: resolveDependencies(includeList)

        %% 智能依赖解析
        SDR->>SDR: 构建依赖图
        SDR->>SDR: 检测重复引用
        alt 发现重复引用
            SDR->>SDR: 去重处理
            SDR->>IM: 添加去重警告
        end

        SDR->>SDR: 检测循环依赖
        alt 发现循环依赖
            SDR->>SDR: 选择最佳打断点
            SDR->>SDR: 移除循环边
            SDR->>IM: 添加循环打断警告
        end

        SDR->>SDR: 生成拓扑排序
        SDR-->>IRS: 返回解析结果 (包含顺序+警告)

        %% 文件合并
        IRS->>TM: mergeFiles(mainContent, includeOrder)
        loop 对每个 include 文件
            TM->>VFS: readFile(includeUri)
            alt 文件存在
                VFS-->>TM: 文件内容
                TM->>TM: 插入文件内容
                TM->>SM: 记录源映射
            else 文件不存在
                VFS-->>TM: FileNotFoundException
                TM->>TM: 记录错误，继续处理其他文件
            end
        end

        TM-->>IRS: 合并后的内容 (包含错误信息)
        IRS-->>PP: Include 处理完成
    end

    %% 5. 结果构建和错误映射
    rect rgb(255, 243, 224)
        Note over PP,IM: 阶段5: 结果构建和错误映射
        PP->>SM: build() 构建最终源映射
        SM-->>PP: 完整的源映射
        PP->>PP: 收集所有阶段的问题
        PP->>IM: mapIssues(issues, sourceMap)
        IM->>IM: 映射错误位置到原始文件
        IM->>IM: 生成用户友好的错误信息
        IM-->>PP: 映射后的问题列表
        PP->>PP: 创建 PreprocessorResult
        PP-->>UAE: 预处理结果 (包含内容、源映射、问题)
    end

    %% 6. 语法和语义分析
    rect rgb(243, 229, 245)
        Note over UAE,LE: 阶段6: 语法和语义分析
        UAE->>UAE: 检查预处理结果
        alt 预处理成功
            UAE->>LE: analyzeFiles(preprocessedContent, sourceMap)
            LE->>LE: 词法分析 (Lexical Analysis)
            LE->>LE: 语法分析 (Syntax Analysis)
            LE->>LE: AST构建 (AST Construction)
            LE->>LE: 语义分析 (Semantic Analysis)
            LE->>LE: 规则检查 (Rule Checking)
            LE-->>UAE: 语法语义分析结果
            UAE->>IM: 合并预处理和分析问题
            IM->>IM: 统一错误位置映射
            IM-->>UAE: 最终问题列表
        else 预处理失败
            UAE->>UAE: 直接返回预处理错误
        end
        UAE-->>CLI: 完整分析结果
    end

    %% 7. 结果返回
    rect rgb(232, 245, 233)
        Note over CLI,LS: 阶段7: 结果返回
        CLI->>CLI: 格式化输出
        alt CLI 模式
            CLI-->>User: 控制台输出 + 文件报告
        else LSP 单文档模式
            CLI->>LDP: publishDiagnostics(mappedIssues)
            LDP->>LDP: groupIssuesByFile(issues)
            loop 对每个文件
                LDP->>LDP: convertToDiagnostic(issue)
                LDP->>LS: publishDiagnostics(params)
                LS->>User: 发送诊断信息到VS Code
            end
            Note over LDP,LS: 单文档错误发布完成
        else LSP Workspace模式
            CLI->>LDP: publishDiagnostics(mappedIssues)
            LDP->>LDP: groupIssuesByFile(issues)
            loop 对每个文件
                LDP->>LDP: convertToDiagnostic(issue)
                LDP->>LS: publishDiagnostics(params)
                LS->>User: 发送诊断信息到VS Code
            end
            CLI-->>User: 返回Workspace分析完成结果
            Note over LDP,LS: Workspace所有文件错误发布完成
        end
    end

    %% 错误处理说明
    Note over User,LS: 错误处理和发布策略说明
    Note over User,LS: • 预处理ERROR: 中断预处理，不进行语法语义分析
    Note over User,LS: • 预处理WARNING: 继续语法语义分析，合并所有问题
    Note over User,LS: • 语法语义ERROR: 记录问题，继续其他检查
    Note over User,LS: • 所有问题都会通过IssueMapper精确定位到原始文件
    Note over User,LS: • LSP模式: 通过LspDiagnosticsPublisher按文件分组发布诊断
```

#### **流程阶段说明**

| 阶段 | 主要组件 | 核心功能 | 关键特性 |
|------|----------|----------|----------|
| **阶段1** | AnalysisUnit, CaplWorkspaceService, WorkspaceFileScanner, WorkspaceAnalysisStrategy | 初始化和输入处理 | 统一的分析单元创建，支持CLI/LSP单文档/LSP Workspace三种模式 |
| **阶段2** | UnifiedAnalysisEngine, PreprocessorPipeline | 预处理管道启动 | 管道式架构设计 |
| **阶段3** | MacroExpansionStage | 宏展开处理 | 保持原始文件上下文的宏验证和替换 |
| **阶段4** | IncludeResolutionStage, SmartDependencyResolver, TextMerger | Include解析和智能依赖处理 | 循环打断、去重优化 |
| **阶段5** | SourceMapBuilder, IssueMapper | 预处理结果构建和错误映射 | 精确的位置映射 |
| **阶段6** | LinterEngine, IssueMapper | 语法和语义分析 | 词法、语法、AST、语义、规则检查 |
| **阶段7** | UnifiedAnalysisEngine, LspDiagnosticsPublisher | 结果返回 | CLI控制台输出/LSP诊断发布 |

#### **关键设计特性**

- ✅ **静态分析优化**: 专为代码质量检查设计，移除编译器相关处理
- ✅ **文件上下文保持**: 宏展开在include之前，保持原始文件上下文准确性
- ✅ **智能依赖处理**: 自动去重和循环打断，不阻止处理流程
- ✅ **统一VFS抽象**: CLI和LSP使用相同的文件访问接口
- ✅ **简洁预处理流程**: 宏展开 → include解析，只保留静态分析必需的处理
- ✅ **精确错误定位**: 支持多层include嵌套的位置追踪和映射
- ✅ **统一错误处理**: 预处理和语法语义问题的统一收集和映射

## 3. 预处理管道设计

### 3.1 预处理流程

```mermaid
flowchart LR
    %% 输入
    START[开始: VirtualDocument]

    %% 阶段1: 宏展开
    subgraph STAGE1 ["阶段1: 宏展开"]
        direction TB
        MES_START[MacroExpansionStage]
        MES_VALIDATE[验证宏定义]
        MES_FIND[查找宏引用]
        MES_EXPAND[展开宏]
        MES_UPDATE[更新源映射]
        MES_RESULT[宏展开后代码]

        MES_START --> MES_VALIDATE --> MES_FIND --> MES_EXPAND --> MES_UPDATE --> MES_RESULT
    end

    %% 阶段2: Include 解析
    subgraph STAGE2 ["阶段2: Include 解析"]
        direction TB
        IRS_START[IncludeResolutionStage]
        IRS_FIND[查找 #include]
        IRS_RESOLVE[解析路径]
        IRS_CYCLE[智能依赖解析]
        IRS_READ[读取文件]
        IRS_MERGE[合并内容]
        IRS_MAP[构建源映射]
        IRS_RESULT[合并后代码]

        IRS_START --> IRS_FIND --> IRS_RESOLVE --> IRS_CYCLE
        IRS_CYCLE --> IRS_READ --> IRS_MERGE --> IRS_MAP --> IRS_RESULT
    end

    %% 警告处理
    subgraph WARNING ["警告处理"]
        direction TB
        WARN_CYCLE[循环依赖打断警告]
        WARN_DUPLICATE[重复引用警告]
        WARN_RESULT[警告信息]

        WARN_CYCLE --> WARN_RESULT
        WARN_DUPLICATE --> WARN_RESULT
    end

    %% 错误处理
    subgraph ERROR ["错误处理"]
        direction TB
        ERROR_NOT_FOUND[文件未找到错误]
        ERROR_MACRO[宏错误]
        ERROR_RESULT[错误结果]

        ERROR_NOT_FOUND --> ERROR_RESULT
        ERROR_MACRO --> ERROR_RESULT
    end

    %% 结束
    END[结束: PreprocessorResult]

    %% 主流程
    START --> STAGE1
    STAGE1 --> STAGE2
    STAGE2 --> END

    %% 警告流程 (不中断处理)
    IRS_CYCLE -.->|智能打断| WARN_CYCLE
    IRS_MERGE -.->|去重处理| WARN_DUPLICATE
    WARNING -.-> END

    %% 错误流程 (中断处理)
    IRS_RESOLVE -.->|文件未找到| ERROR_NOT_FOUND
    MES_VALIDATE -.->|宏错误| ERROR_MACRO
    ERROR --> END

    style STAGE1 fill:#fff3e0
    style STAGE2 fill:#e8f5e8
    style WARNING fill:#fff3e0
    style ERROR fill:#ffebee
```

### 3.2 预处理管道实现

```mermaid
classDiagram
    class PreprocessorPipeline {
        -stages: List~PreprocessorStage~
        +process(document: VirtualDocument, context: PreprocessorContext) PreprocessorResult
        -initializeStages() List~PreprocessorStage~
    }

    class PreprocessorStage {
        <<interface>>
        +process(input: PreprocessorResult, context: PreprocessorContext) PreprocessorResult
        +getName() String
        +getOrder() int
    }

    class MacroExpansionStage {
        -macroExpander: MacroExpander
        +process(input: PreprocessorResult, context: PreprocessorContext) PreprocessorResult
        -validateAndExpand(content: String, context: MacroContext) PreprocessorResult
        -createMacroContext(context: PreprocessorContext) MacroContext
    }

    class IncludeResolutionStage {
        -smartDependencyResolver: SmartDependencyResolver
        -textMerger: TextMerger
        +process(input: PreprocessorResult, context: PreprocessorContext) PreprocessorResult
        -findIncludes(content: String) List~IncludeDirective~
        -resolveIncludePath(path: String, baseUri: String) String
        -resolveDependencies(files: List~String~) ResolutionResult
        -handleCircularDependencies(cycles: List~CycleBreakInfo~) List~String~
    }

    PreprocessorPipeline --> PreprocessorStage
    PreprocessorStage <|.. MacroExpansionStage
    PreprocessorStage <|.. IncludeResolutionStage
```

**预处理管道执行流程**:

```mermaid
sequenceDiagram
    participant PP as PreprocessorPipeline
    participant MES as MacroExpansionStage
    participant IRS as IncludeResolutionStage

    Note over PP,IRS: 简化的预处理管道：宏展开 → Include解析

    PP->>MES: process(input, context)
    MES->>MES: 验证和展开宏，保持原始文件上下文
    MES-->>PP: result1 (宏展开后，可能包含警告)

    alt 处理成功或仅有警告
        PP->>IRS: process(result1, context)
        IRS->>IRS: 智能依赖解析
        IRS->>IRS: 检测和打断循环依赖
        IRS->>IRS: 文件去重和合并
        IRS-->>PP: final result (include解析后，包含所有警告)
    else 发现阻塞性错误
        PP-->>PP: 返回错误结果 (包含错误和警告)
    end

    Note over PP: 警告不阻止处理流程，只有ERROR级别的问题才会中断处理
    Note over PP: 宏展开在前确保文件上下文准确性，专为静态分析优化
    Note over IRS: 循环依赖智能打断，生成警告但不阻止处理
```

## 4. CAPL #include 合并机制

### 4.1 CAPL 文件结构特点

CAPL 文件具有特定的结构，合并时需要遵循以下规则：

1. **includes section**: 合并后移除，所有 #include 指令被实际内容替换
2. **variables section**: 合并为单一 section，按 include 顺序 + 主文件顺序
3. **函数定义**: 保持原有顺序，按文本顺序合并
4. **事件处理器**: 保持主文件中的顺序

**注意**: 预处理阶段只进行纯文本级别的合并，不检测函数重复定义等语义问题，这些由后续的语义分析阶段处理。

### 4.2 合并示例

**合并前**:
```capl
// main.capl
includes {
  #include "common.cin"
  #include "messages.cin"
}
variables {
  int mainCounter = 0;
}
on start {
  write("Starting main");
}

// common.cin
variables {
  int globalCounter = 0;
}
void initializeSystem() {
  globalCounter = 0;
}

// messages.cin
variables {
  message engineData msg1;
}
void sendMessage(message msg) {
  output(msg);
}
```

**合并后**:
```capl
variables {
  // 来自 common.cin
  int globalCounter = 0;
  // 来自 messages.cin  
  message engineData msg1;
  // 来自 main.capl
  int mainCounter = 0;
}

// 来自 common.cin
void initializeSystem() {
  globalCounter = 0;
}

// 来自 messages.cin
void sendMessage(message msg) {
  output(msg);
}

// 来自 main.capl
on start {
  write("Starting main");
}
```

### 4.3 智能依赖解析和循环处理

#### **4.3.1 智能依赖解析器**

```mermaid
classDiagram
    class SmartDependencyResolver {
        +resolveDependencies(files: List~String~, vfs: VirtualFileSystem) ResolutionResult
        +detectAndBreakCycles(dependencies: Map~String, Set~String~~) CycleBreakResult
        +deduplicateIncludes(dependencies: Map~String, List~String~~) Map~String, Set~String~~
        +buildIncludeOrder(dependencies: Map~String, Set~String~~) List~String~
        -findStronglyConnectedComponents(graph: DependencyGraph) List~Set~String~~
        -selectCycleBreakPoint(cycle: List~String~) String
    }

    class ResolutionResult {
        -includeOrder: List~String~
        -processedDependencies: Map~String, Set~String~~
        -duplicatesRemoved: Map~String, List~String~~
        -cyclesBroken: List~CycleBreakInfo~
        -warnings: List~String~
        +getIncludeOrder() List~String~
        +getDuplicatesRemoved() Map~String, List~String~~
        +getCyclesBroken() List~CycleBreakInfo~
    }

    class CycleBreakInfo {
        -cycle: List~String~
        -breakPoint: String
        -reason: String
        +getCycle() List~String~
        +getBreakPoint() String
        +getReason() String
    }

    class DependencyGraph {
        -nodes: Set~String~
        -edges: Map~String, Set~String~~
        +addNode(file: String)
        +addEdge(from: String, to: String)
        +removeEdge(from: String, to: String)
        +findCycles() List~List~String~~
        +getTopologicalOrder() List~String~
        +getStronglyConnectedComponents() List~Set~String~~
    }

    SmartDependencyResolver --> ResolutionResult
    SmartDependencyResolver --> DependencyGraph
    ResolutionResult --> CycleBreakInfo
```

#### **4.3.2 循环依赖智能处理策略**

传统方案将循环依赖视为错误，我们的智能方案将其视为可解决的问题：

```mermaid
flowchart LR
    subgraph TRADITIONAL ["传统方案"]
        T1[检测循环依赖] --> T2[报告错误] --> T3[停止处理]
    end

    subgraph SMART ["智能方案"]
        S1[检测循环依赖] --> S2[智能打断] --> S3[继续处理] --> S4[生成警告]
    end

    style T2 fill:#ffebee
    style T3 fill:#ffebee
    style S2 fill:#e8f5e8
    style S3 fill:#e8f5e8
    style S4 fill:#fff3e0
```

**循环依赖处理示例**:

```mermaid
graph TB
    subgraph BEFORE ["处理前: 检测到循环"]
        A1[a.cin] --> B1[b.cin]
        B1 --> C1[c.cin]
        C1 --> A1
    end

    subgraph ANALYSIS ["分析阶段"]
        D1["识别强连通分量<br/>{a.cin, b.cin, c.cin}"]
        D2["计算打断成本<br/>c.cin → a.cin: 成本最低"]
        D3["选择打断点<br/>移除 c.cin → a.cin"]
    end

    subgraph AFTER ["处理后: 循环已解决"]
        A2[a.cin] --> B2[b.cin]
        B2 --> C2[c.cin]
    end

    subgraph WARNING ["警告信息"]
        W1[⚠️ 循环依赖已解决]
        W2[📍 移除边: c.cin → a.cin]
        W3[📋 包含顺序: a.cin → b.cin → c.cin]
    end

    BEFORE --> ANALYSIS
    ANALYSIS --> AFTER
    AFTER --> WARNING

    style C1 fill:#ffebee
    style A1 fill:#ffebee
    style A2 fill:#e8f5e8
    style B2 fill:#e8f5e8
    style C2 fill:#e8f5e8
```

**打断点选择策略**:

| 优先级 | 策略 | 原因 |
|--------|------|------|
| **1** | 移除最后添加的边 | 保持原有依赖结构稳定 |
| **2** | 移除依赖深度最大的边 | 减少对整体结构的影响 |
| **3** | 移除文件名字典序最大的边 | 提供确定性的选择 |

#### **4.3.3 循环依赖处理示例**

**场景**: 三个文件形成循环依赖

```capl
// main.capl
#include "utils.cin"
#include "common.cin"

// utils.cin
#include "common.cin"
#include "messages.cin"

// common.cin
#include "messages.cin"

// messages.cin
#include "utils.cin"  // 形成循环: utils.cin → messages.cin → utils.cin
```

**智能处理过程**:

```mermaid
flowchart TD
    subgraph DETECTION ["1. 循环检测"]
        A[构建依赖图]
        B[检测强连通分量]
        C[发现循环: utils.cin ↔ messages.cin]
        A --> B --> C
    end

    subgraph ANALYSIS ["2. 打断分析"]
        D[分析循环结构]
        E[计算打断成本]
        F[选择打断点: messages.cin → utils.cin]
        D --> E --> F
    end

    subgraph RESOLUTION ["3. 循环解决"]
        G[移除循环边]
        H[生成拓扑排序]
        I[确定包含顺序]
        G --> H --> I
    end

    subgraph RESULT ["4. 处理结果"]
        J[✅ 循环已解决]
        K[⚠️ 生成警告信息]
        L[📋 最终包含顺序]
        J --> K --> L
    end

    DETECTION --> ANALYSIS
    ANALYSIS --> RESOLUTION
    RESOLUTION --> RESULT

    style C fill:#fff3e0
    style F fill:#e8f5e8
    style J fill:#e8f5e8
    style K fill:#fff3e0
```

**处理结果**:

| 处理步骤 | 内容 | 说明 |
|----------|------|------|
| **原始依赖** | `utils.cin → messages.cin → utils.cin` | 检测到循环依赖 |
| **智能打断** | 移除 `messages.cin → utils.cin` | 选择成本最低的边 |
| **最终顺序** | `utils.cin → common.cin → messages.cin → main.capl` | 生成有效的拓扑排序 |
| **警告信息** | `CAPL-INCLUDE-002: 循环依赖已智能解决` | 通知用户处理结果 |

**生成的警告信息**:
```
⚠️  循环依赖智能处理报告:
📍 检测到循环: utils.cin → messages.cin → utils.cin
🔧 智能打断: 移除 messages.cin → utils.cin
📋 最终顺序: utils.cin → common.cin → messages.cin → main.capl
💡 建议: 重构include结构以避免循环依赖
```

### 4.4 文本合并器

```mermaid
classDiagram
    class TextMerger {
        -smartDependencyResolver: SmartDependencyResolver
        +mergeFiles(mainContent: String, includes: List~IncludeFile~) MergeResult
        +processIncludeDirective(directive: String, baseUri: String) String
        +insertIncludeContent(content: String, includeContent: String, position: int) String
        +buildSourceMapping(originalUri: String, includeUri: String, insertPosition: int) MappingEntry
        -resolveDependencies(files: List~String~) ResolutionResult
    }

    class IncludeFile {
        -uri: String
        -content: String
        -lastModified: long
        +getUri() String
        +getContent() String
        +getLastModified() long
    }

    class MergeResult {
        -mergedContent: String
        -sourceMap: SourceMap
        -includedFiles: List~String~
        -successful: boolean
        -warnings: List~String~
        -cyclesBroken: List~CycleBreakInfo~
        +getMergedContent() String
        +getSourceMap() SourceMap
        +isSuccessful() boolean
        +getWarnings() List~String~
        +getCyclesBroken() List~CycleBreakInfo~
    }

    class SourceMapBuilder {
        -mappings: List~MappingEntry~
        +addIncludeMapping(line: int, sourceUri: String, originalLine: int)
        +addOriginalMapping(line: int, sourceUri: String, originalLine: int)
        +build() SourceMap
    }

    TextMerger --> IncludeFile
    TextMerger --> MergeResult
    TextMerger --> SourceMapBuilder
    TextMerger --> SmartDependencyResolver
```

**智能依赖解析流程**:

```mermaid
flowchart LR
    subgraph INPUT ["输入处理"]
        direction TB
        A[开始解析]
        B[构建依赖图]
        C[去重处理]
        D[初始化状态]
        A --> B --> C --> D
    end

    subgraph DEDUP ["去重优化"]
        direction TB
        E[检测重复引用]
        F{发现重复?}
        G[移除重复边]
        H[记录去重信息]
        I[继续处理]

        E --> F
        F -->|是| G
        F -->|否| I
        G --> H --> I
    end

    subgraph CYCLE ["循环检测与打断"]
        direction TB
        J[检测强连通分量]
        K{发现循环?}
        L[分析循环结构]
        M[选择打断点]
        N[移除循环边]
        O[记录打断信息]
        P[继续处理]

        J --> K
        K -->|是| L
        K -->|否| P
        L --> M --> N --> O --> P
    end

    subgraph RESULT ["结果生成"]
        direction TB
        Q[生成拓扑排序]
        R[构建包含顺序]
        S[生成解析结果]
        T[返回优化结果]

        Q --> R --> S --> T
    end

    INPUT --> DEDUP
    DEDUP --> CYCLE
    CYCLE --> RESULT

    style G fill:#e8f5e8
    style N fill:#fff3e0
    style T fill:#e3f2fd
```

**文本合并流程**:

```mermaid
flowchart LR
    subgraph INPUT ["输入阶段"]
        direction TB
        A[开始合并]
        B[读取主文件内容]
        C[查找 #include 指令]
        D[智能依赖解析]

        A --> B --> C --> D
    end

    subgraph RESOLVE ["依赖解析阶段"]
        direction TB
        E[构建依赖图]
        F[检测循环依赖]
        G[智能打断循环]
        H[生成包含顺序]

        E --> F --> G --> H
    end

    subgraph MERGE ["文本合并阶段"]
        direction TB
        I[按顺序处理文件]
        J[插入 include 文件内容]
        K[更新源映射]

        I --> J --> K
    end

    subgraph RESULT ["结果阶段"]
        direction TB
        L[构建完整源映射]
        M[创建 MergeResult]
        N[返回合并后文本]

        L --> M --> N
    end

    INPUT --> RESOLVE
    RESOLVE --> MERGE
    MERGE --> RESULT

    style G fill:#fff3e0
    style J fill:#e8f5e8
    style K fill:#e3f2fd
```

**文本合并说明**:

预处理阶段的文件合并是**纯文本级别**的操作，不理解代码语义：

| 处理类型 | 处理内容 | 处理方式 | 示例 |
|----------|----------|----------|------|
| **#include 展开** | 查找并替换 #include 指令 | 文本替换 | `#include "file.cin"` → 插入文件内容 |
| **位置映射** | 记录文本插入位置 | 位置追踪 | 记录每行来自哪个原始文件 |
| **文件去重** | 避免重复包含同一文件 | 简单去重 | 同一文件只包含一次 |
| **错误处理** | 文件未找到、循环依赖 | 文本级错误 | 无法读取文件或循环引用 |

**文本合并的特点**:
- ✅ **纯文本操作**: 不解析 CAPL 语法，只做字符串操作
- ✅ **简单替换**: 将 #include 指令替换为文件内容
- ✅ **位置追踪**: 记录合并后每行的原始来源
- ✅ **错误检测**: 只检测文件级错误（未找到、循环依赖）

**文本合并不做的事情**:
- ❌ 语法解析 (词法/语法分析阶段)
- ❌ 语义理解 (语义分析阶段)
- ❌ 结构验证 (语法分析阶段)
- ❌ 重复声明检测 (语义分析阶段)
- ❌ 类型检查 (语义分析阶段)

## 5. 源映射与错误定位

### 5.1 源映射流程

```mermaid
flowchart TD
    %% 源映射构建
    subgraph "源映射构建 (Source Map Building)"
        SMB_START[SourceMapBuilder 开始]

        subgraph "映射类型"
            ORIGINAL_MAP[原始代码映射<br/>type: ORIGINAL]
            INCLUDE_MAP[Include 文件映射<br/>type: INCLUDED]
            MACRO_MAP[宏展开映射<br/>type: MACRO]
            GENERATED_MAP[生成代码映射<br/>type: GENERATED]
        end

        SMB_BUILD[构建 SourceMap]
        SMB_OPTIMIZE[优化映射表]
        SMB_RESULT[完整源映射]

        SMB_START --> ORIGINAL_MAP
        SMB_START --> INCLUDE_MAP
        SMB_START --> MACRO_MAP
        SMB_START --> GENERATED_MAP

        ORIGINAL_MAP --> SMB_BUILD
        INCLUDE_MAP --> SMB_BUILD
        MACRO_MAP --> SMB_BUILD
        GENERATED_MAP --> SMB_BUILD

        SMB_BUILD --> SMB_OPTIMIZE
        SMB_OPTIMIZE --> SMB_RESULT
    end

    %% 分析引擎处理
    subgraph "分析引擎处理 (Analysis Engine)"
        TEMP_FILE[创建临时文件<br/>预处理后内容]
        LINTER_ANALYSIS[LinterEngine 分析]
        RAW_ISSUES[原始错误列表<br/>基于临时文件位置]

        TEMP_FILE --> LINTER_ANALYSIS
        LINTER_ANALYSIS --> RAW_ISSUES
    end

    %% 错误映射过程
    subgraph "错误映射过程 (Issue Mapping)"
        IM_START[IssueMapper 开始]
        IM_ITERATE[遍历原始错误]
        IM_LOOKUP[查找源映射条目]
        IM_MAP[映射到原始位置]
        IM_CREATE[创建映射后错误]
        IM_RESULT[映射后错误列表]

        IM_START --> IM_ITERATE
        IM_ITERATE --> IM_LOOKUP
        IM_LOOKUP --> IM_MAP
        IM_MAP --> IM_CREATE
        IM_CREATE --> IM_RESULT
    end

    %% LSP 错误发布
    subgraph "LSP 错误发布 (LSP Diagnostics)"
        LSP_GROUP[按文件分组错误]
        LSP_CONVERT[转换为 Diagnostic]
        LSP_PUBLISH[发布到对应文件]

        subgraph "发布目标"
            MAIN_FILE[主文件诊断]
            INCLUDE_FILE[Include 文件诊断]
            OTHER_FILE[其他文件诊断]
        end

        LSP_GROUP --> LSP_CONVERT
        LSP_CONVERT --> LSP_PUBLISH
        LSP_PUBLISH --> MAIN_FILE
        LSP_PUBLISH --> INCLUDE_FILE
        LSP_PUBLISH --> OTHER_FILE
    end

    %% 主流程连接
    SMB_RESULT --> IM_START
    RAW_ISSUES --> IM_ITERATE
    IM_RESULT --> LSP_GROUP

    %% 示例数据流
    subgraph "示例: Include 文件错误定位"
        EX_ORIGINAL[原始错误:<br/>temp_file.capl:15:5]
        EX_LOOKUP[查找映射:<br/>line 15 → include_file.cin:3:5]
        EX_MAPPED[映射后错误:<br/>include_file.cin:3:5]
        EX_PUBLISH[发布诊断:<br/>到 include_file.cin]

        EX_ORIGINAL --> EX_LOOKUP
        EX_LOOKUP --> EX_MAPPED
        EX_MAPPED --> EX_PUBLISH
    end
```

### 5.2 源映射实现

```mermaid
classDiagram
    class SourceMap {
        -mappings: List~MappingEntry~
        +mapToOriginal(processedLine: int, processedColumn: int) SourceLocation
        +getOriginalFile(processedLine: int) String
        +findMapping(line: int, column: int) MappingEntry
        +getAllMappings() List~MappingEntry~
    }

    class MappingEntry {
        -processedLine: int
        -processedColumn: int
        -originalUri: String
        -originalLine: int
        -originalColumn: int
        -type: MappingType
        +getProcessedLine() int
        +getOriginalUri() String
        +getOriginalLine() int
        +getType() MappingType
    }

    class MappingType {
        <<enumeration>>
        ORIGINAL
        INCLUDED
        MACRO
        GENERATED
    }

    class IssueMapper {
        +mapIssuesBackToOriginal(rawIssues: List~IssueObject~, sourceMap: SourceMap) List~IssueObject~
        -createMappedIssue(issue: IssueObject, originalLocation: SourceLocation) IssueObject
    }

    class SourceLocation {
        -filePath: String
        -startLine: int
        -startColumn: int
        -endLine: int
        -endColumn: int
        +getFilePath() String
        +getStartLine() int
        +getStartColumn() int
    }

    SourceMap --> MappingEntry
    MappingEntry --> MappingType
    IssueMapper --> SourceMap
    IssueMapper --> SourceLocation
```

**源映射工作流程**:

```mermaid
sequenceDiagram
    participant UAE as UnifiedAnalysisEngine
    participant PP as PreprocessorPipeline
    participant SMB as SourceMapBuilder
    participant LE as LinterEngine
    participant IM as IssueMapper
    participant SM as SourceMap

    Note over UAE,SM: 完整的源映射构建和使用流程

    %% 预处理阶段 - 构建源映射
    UAE->>PP: process(primaryDocument, context)
    PP->>SMB: 初始化源映射构建器

    loop 预处理各阶段
        PP->>PP: 执行预处理步骤 (宏展开/include解析)
        PP->>SMB: recordMapping(originalPos, processedPos, type)
        SMB->>SMB: 更新映射表
    end

    PP->>SMB: build() 构建最终源映射
    SMB-->>PP: 完整的SourceMap
    PP-->>UAE: PreprocessorResult (包含内容和源映射)

    %% 语法语义分析阶段
    UAE->>LE: analyzeFiles(preprocessedContent, configuration)
    LE-->>UAE: rawIssues (基于预处理后位置)

    %% 错误映射阶段 - 使用源映射
    UAE->>IM: mapIssuesBackToOriginal(rawIssues, sourceMap)

    loop 对每个错误
        IM->>SM: mapToOriginal(processedLine, processedColumn)
        SM-->>IM: originalLocation (文件URI + 行列号)
        IM->>IM: createMappedIssue(issue, originalLocation)
    end

    IM-->>UAE: mappedIssues (映射到原始文件位置)

    Note over UAE,SM: 错误现在精确指向原始文件的正确位置
```

**源映射构建详细流程**:

以下通过一个具体示例展示源映射在各个预处理阶段的构建过程：

#### **原始文件 (main.capl)**
```capl
Line 1: #include "common.cin"
Line 2: write("File: %FILE_NAME%");
Line 3: write("Version: %VERSION%");
Line 4: void main() { ... }
```

#### **common.cin 文件**
```capl
Line 1: int globalVar = 0;
Line 2: void initGlobal() { globalVar = 1; }
```

#### **预处理阶段和源映射构建**

| 阶段 | 处理内容 | 源映射记录 | 说明 |
|------|----------|------------|------|
| **阶段1: 宏展开** | 展开 `%FILE_NAME%` → `"main.capl"`<br/>展开 `%VERSION%` → `"1.0.0"` | `processed:2 → main.capl:2 [MACRO_EXPANDED]`<br/>`processed:3 → main.capl:3 [MACRO_EXPANDED]` | 保持原始文件上下文，宏显示正确的文件名 |
| **阶段2: Include解析** | 读取 common.cin 并插入内容 | `processed:1 → common.cin:1 [INCLUDED]`<br/>`processed:2 → common.cin:2 [INCLUDED]`<br/>`processed:3 → main.capl:2 [MACRO_EXPANDED]`<br/>`processed:4 → main.capl:3 [MACRO_EXPANDED]`<br/>`processed:5 → main.capl:4 [ORIGINAL]` | 替换 #include，插入文件内容 |

#### **最终预处理结果**
```capl
Line 1: int globalVar = 0;           // 来自 common.cin:1
Line 2: void initGlobal() { globalVar = 1; }  // 来自 common.cin:2
Line 3: write("File: main.capl");    // 来自 main.capl:2 (宏展开)
Line 4: write("Version: 1.0.0");     // 来自 main.capl:3 (宏展开)
Line 5: void main() { ... }          // 来自 main.capl:4
```

#### **最终源映射表**
| 预处理后位置 | 原始位置 | 映射类型 | 说明 |
|-------------|----------|----------|------|
| `processed:1` | `common.cin:1` | `INCLUDED` | Include文件内容 |
| `processed:2` | `common.cin:2` | `INCLUDED` | Include文件内容 |
| `processed:3` | `main.capl:2` | `MACRO_EXPANDED` | 宏展开结果，保持原始文件上下文 |
| `processed:4` | `main.capl:3` | `MACRO_EXPANDED` | 宏展开结果，保持原始文件上下文 |
| `processed:5` | `main.capl:4` | `ORIGINAL` | 原始代码行 |

**映射类型示例**:

以下展示错误如何通过源映射从预处理后位置精确定位到原始文件位置：

#### **预处理后文件 (LinterEngine分析的内容)**
```capl
Line 1: int globalVar = 0;           // 来自 common.cin
Line 2: void initGlobal() { globalVar = 1; }  // 来自 common.cin
Line 3: write("File: main.capl");    // 来自 main.capl (宏展开)
Line 4: write("Version: 1.0.0");     // 来自 main.capl (宏展开)
Line 5: void main() { ... }          // 来自 main.capl
```

#### **错误映射过程示例**

| 步骤 | 内容 | 说明 |
|------|------|------|
| **1. LinterEngine发现错误** | `Line 1: 未使用的变量 'globalVar'` | 基于预处理后文件的位置 |
| **2. 查询源映射表** | `processed:1 → common.cin:1 [INCLUDED]` | 找到对应的原始位置 |
| **3. 生成映射后错误** | `common.cin:1: 未使用的变量 'globalVar'` | 精确定位到原始文件 |

#### **不同映射类型的错误定位**

| 映射类型 | 预处理后位置 | 原始位置 | 错误报告示例 |
|----------|-------------|----------|-------------|
| **INCLUDED** | `processed:1` | `common.cin:1` | `common.cin:1: 未使用的变量 'globalVar'` |
| **INCLUDED** | `processed:2` | `common.cin:2` | `common.cin:2: 函数未被调用` |
| **MACRO_EXPANDED** | `processed:3` | `main.capl:2` | `main.capl:2: 字符串格式错误` |
| **MACRO_EXPANDED** | `processed:4` | `main.capl:3` | `main.capl:3: 宏展开后类型错误` |
| **ORIGINAL** | `processed:5` | `main.capl:4` | `main.capl:4: 函数缺少返回值` |

#### **跨文件错误定位的价值**

- ✅ **精确定位**: 错误直接指向用户编辑的原始文件
- ✅ **上下文保持**: 保留完整的文件路径和行号信息
- ✅ **开发体验**: 用户可以直接跳转到问题所在的原始位置
- ✅ **调试效率**: 避免在预处理后的临时文件中查找问题

### 5.3 LSP 错误发布

按照完整流程时序图第9阶段的定义，LSP错误发布由 `CalintCli/LSP` 直接调用 `LspDiagnosticsPublisher` 完成：

```mermaid
classDiagram
    class LspDiagnosticsPublisher {
        -languageServer: LanguageServer
        +publishDiagnostics(issues: List~IssueObject~)
        -convertToDiagnostic(issue: IssueObject) Diagnostic
        -groupIssuesByFile(issues: List~IssueObject~) Map~String, List~IssueObject~~
    }

    class LanguageServer {
        +publishDiagnostics(uri: String, diagnostics: List~Diagnostic~)
    }

    LspDiagnosticsPublisher --> LanguageServer
```

**LSP 错误发布流程**（严格对齐时序图第9阶段）:

```mermaid
sequenceDiagram
    participant CLI as CalintCli/LSP
    participant LDP as LspDiagnosticsPublisher
    participant LS as LanguageServer
    participant User as VS Code

    Note over CLI,User: LSP模式错误发布流程

    CLI->>LDP: publishDiagnostics(mappedIssues)
    LDP->>LDP: groupIssuesByFile(issues)

    loop 对每个文件
        LDP->>LDP: convertToDiagnostic(issue)
        LDP->>LS: publishDiagnostics(uri, diagnostics)
        LS->>User: 发送诊断信息到VS Code
    end

    Note over CLI,User: 多文件错误分发完成
```

**错误分组和发布示例**:

```mermaid
flowchart TD
    A[映射后的错误列表] --> B[按文件分组]

    B --> C[main.capl 错误]
    B --> D[common.cin 错误]
    B --> E[utils.cin 错误]

    C --> C1[转换为 Diagnostic]
    D --> D1[转换为 Diagnostic]
    E --> E1[转换为 Diagnostic]

    C1 --> C2[发布到 main.capl]
    D1 --> D2[发布到 common.cin]
    E1 --> E2[发布到 utils.cin]

    C2 --> F[VS Code 显示错误]
    D2 --> F
    E2 --> F

    style C fill:#e1f5fe
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#f3e5f5
```

**LSP错误发布的核心功能**:

| 功能 | 说明 | 对应时序图步骤 |
|------|------|----------------|
| **按文件分组** | 将映射后的错误按原始文件URI分组 | `groupIssuesByFile(issues)` |
| **格式转换** | 将内部IssueObject转换为LSP Diagnostic格式 | `convertToDiagnostic(issue)` |
| **错误发布** | 调用LanguageServer发布诊断信息 | `publishDiagnostics(uri, diagnostics)` |
| **多文件支持** | 支持主文件和include文件的错误分发 | `loop 对每个文件` |

## 6. 预处理错误处理机制

预处理阶段主要处理文件级和文本级错误，语义级错误（如重复声明）由 LinterEngine 的语义分析阶段处理。

### 6.1 错误分类与处理策略

#### **错误严重级别分类**

| 严重级别 | 错误类型 | 处理策略 | 示例 |
|----------|----------|----------|------|
| **ERROR** | 阻止继续处理的错误 | 停止处理，返回错误 | 文件未找到、语法错误 |
| **WARNING** | 可以继续处理的问题 | 智能处理，记录警告，继续处理 | 循环依赖打断、重复引用去重 |
| **INFO** | 信息性提示 | 记录信息，正常处理 | 宏展开信息、优化提示 |

#### **统一错误处理流程**

```mermaid
flowchart TD
    A[检测到问题] --> B{问题严重级别}

    B -->|ERROR<br/>阻塞性错误| C[停止当前阶段]
    B -->|WARNING<br/>可恢复问题| D[智能处理 + 记录警告]
    B -->|INFO<br/>信息提示| E[记录信息]

    C --> F[收集所有问题]
    D --> G[继续处理流程]
    E --> G

    F --> H[返回失败结果<br/>包含错误和警告]
    G --> I{还有后续阶段?}

    I -->|是| J[进入下一阶段]
    I -->|否| K[返回成功结果<br/>包含所有警告]

    J --> A

    subgraph SMART_HANDLING ["智能处理示例"]
        L[循环依赖 → 智能打断]
        M[重复引用 → 自动去重]
        N[文件缺失 → 提供建议]
    end

    D --> SMART_HANDLING

    style C fill:#ffebee
    style D fill:#fff3e0
    style E fill:#e3f2fd
    style H fill:#ffebee
    style K fill:#e8f5e8
    style SMART_HANDLING fill:#e8f5e8
```

#### **错误处理一致性原则**

| 处理阶段 | ERROR级别处理 | WARNING级别处理 | INFO级别处理 |
|----------|---------------|-----------------|--------------|
| **宏展开** | 未知宏 → 停止 | 上下文缺失 → 使用默认值 | 展开信息 → 记录 |
| **Include解析** | 文件未找到 → 停止 | 循环依赖 → 智能打断 | 包含信息 → 记录 |

**核心原则**:
- ✅ **ERROR**: 阻止继续处理，立即返回失败结果
- ✅ **WARNING**: 智能处理问题，记录警告，继续流程
- ✅ **INFO**: 记录信息，不影响处理流程
- ✅ **一致性**: 所有阶段遵循相同的错误处理策略

### 6.2 文件级错误处理

#### **文件访问错误分类**

```mermaid
classDiagram
    class FileAccessError {
        <<abstract>>
        -uri: String
        -location: SourceLocation
        -searchPaths: List~String~
        +getUri() String
        +getLocation() SourceLocation
        +getSearchPaths() List~String~
    }

    class FileNotFoundError {
        -attemptedPaths: List~String~
        +getAttemptedPaths() List~String~
        +generateSuggestions() List~String~
    }

    class AccessDeniedError {
        -permissions: String
        -requiredPermissions: String
        +getPermissions() String
        +getRequiredPermissions() String
    }

    class FileReadError {
        -ioException: String
        -fileSize: long
        +getIoException() String
        +getFileSize() long
    }

    class CircularIncludeError {
        -includePath: List~String~
        -cycleStartIndex: int
        +getIncludePath() List~String~
        +getCycleStartIndex() int
    }

    FileAccessError <|-- FileNotFoundError
    FileAccessError <|-- AccessDeniedError
    FileAccessError <|-- FileReadError
    FileAccessError <|-- CircularIncludeError
```

#### **文件错误处理流程**

```mermaid
flowchart TD
    A[文件访问请求] --> B[VFS.readFile]
    B --> C{文件是否存在?}

    C -->|否| D[FileNotFoundError]
    C -->|是| E{是否有读取权限?}

    E -->|否| F[AccessDeniedError]
    E -->|是| G[尝试读取文件]

    G --> H{读取是否成功?}
    H -->|否| I[FileReadError]
    H -->|是| J[检查循环引用]

    J --> K{是否循环引用?}
    K -->|是| L[CircularIncludeError]
    K -->|否| M[返回文件内容]

    D --> N[生成错误报告]
    F --> N
    I --> N
    L --> N

    N --> O[添加搜索路径信息]
    O --> P[返回详细错误信息]

    style D fill:#ffebee
    style F fill:#ffebee
    style I fill:#ffebee
    style L fill:#ffebee
    style M fill:#e8f5e8
    style P fill:#fff3e0
```

#### **错误信息生成策略**

| 错误类型 | 信息内容 | 辅助信息 | 用户建议 |
|----------|----------|----------|----------|
| **文件未找到** | 文件路径、搜索路径列表 | 相似文件名建议 | 检查文件路径、确认文件存在 |
| **访问权限** | 当前权限、所需权限 | 文件所有者信息 | 修改文件权限或联系管理员 |
| **读取失败** | IO异常详情、文件大小 | 磁盘空间、文件状态 | 检查磁盘空间、文件完整性 |
| **循环引用** | 完整引用路径、循环起点 | 建议的打断点 | 重构include结构 |

### 6.3 宏处理错误

#### **统一的宏处理架构**

采用统一的管道式宏处理架构，所有宏处理都通过 `MacroExpansionStage` 完成：

```mermaid
classDiagram
    class MacroExpansionStage {
        -macroExpander: MacroExpander
        +process(input: PreprocessorResult, context: PreprocessorContext) PreprocessorResult
        -createMacroContext(context: PreprocessorContext) MacroContext
        -createMacroExpansionIssue(exception: MacroExpansionException, fileName: String) IssueObject
    }

    class MacroExpander {
        -MACRO_PATTERN: Pattern
        -builtInMacros: Map~String, Function~MacroContext, String~~
        +expandMacros(sourceCode: String, context: MacroContext) String
        +validateMacros(sourceCode: String, filePath: String) List~IssueObject~
        +containsMacros(text: String) boolean
        +isKnownMacro(macroName: String) boolean
        +getBuiltInMacroNames() Set~String~
        -resolveMacro(macroName: String, context: MacroContext, lineNumber: int) String
        -getDefaultValueForMacro(macroName: String) String
    }

    class MacroExpansionException {
        +MacroExpansionException(message: String)
        +MacroExpansionException(message: String, cause: Throwable)
    }

    class MacroContext {
        -sourceFileName: String
        -baseFileName: String
        -nodeName: String
        -networkName: String
        -busType: String
        -channel: int
        -userDefinedMacros: Map~String, String~
        +forTesting(fileName: String)$ MacroContext
        +forFile(fileName: String)$ MacroContext
        +builder()$ Builder
    }

    class PreprocessorPipelineFactory {
        +createMacroOnlyPipeline()$ PreprocessorPipeline
        +preprocessMacrosOnly(sourceCode: String, context: MacroContext)$ PreprocessorResult
        +createTestingPipeline()$ PreprocessorPipeline
    }

    MacroExpansionStage --> MacroExpander
    MacroExpander --> MacroExpansionException
    MacroExpander --> MacroContext
    PreprocessorPipelineFactory --> MacroExpansionStage
```

#### **宏处理流程**

实际的宏处理采用两阶段验证和展开策略：

```mermaid
flowchart TD
    A[开始预处理] --> B[validateMacros 验证阶段]
    B --> C[扫描所有宏引用]
    C --> D{发现未知宏?}

    D -->|是| E[创建 IssueObject]
    D -->|否| F[检查是否包含宏]

    E --> G[收集所有错误]
    G --> H[返回失败结果]

    F --> I{包含宏?}
    I -->|否| J[跳过展开，返回原始代码]
    I -->|是| K[expandMacros 展开阶段]

    K --> L[逐行处理宏]
    L --> M[resolveMacro 解析宏值]
    M --> N{宏已知?}

    N -->|否| O[抛出 MacroExpansionException]
    N -->|是| P[获取宏值]

    P --> Q{上下文有值?}
    Q -->|是| R[使用上下文值]
    Q -->|否| S[使用默认值]

    R --> T[替换宏引用]
    S --> T
    T --> U[返回展开后代码]

    O --> V[异常处理]
    V --> H

    style E fill:#ffebee
    style H fill:#ffebee
    style O fill:#ffebee
    style V fill:#ffebee
    style J fill:#e3f2fd
    style U fill:#e8f5e8
```

#### **支持的CAPL宏**

根据实际代码实现，支持以下预定义的CAPL宏：

| 宏名称 | 描述 | 默认值 | 上下文来源 |
|--------|------|--------|------------|
| `%BASE_FILE_NAME%` | 编译文件名 | `unknown.can` | `MacroContext.getBaseFileName()` |
| `%BASE_FILE_NAME_NO_EXT%` | 编译文件名（无扩展名） | `unknown` | `MacroContext.getBaseFileNameNoExt()` |
| `%FILE_NAME%` | 源代码文件名 | `unknown.cin` | `MacroContext.getSourceFileName()` |
| `%FILE_NAME_NO_EXT%` | 源文件名（无扩展名） | `unknown` | `MacroContext.getSourceFileNameNoExt()` |
| `%NODE_NAME%` | 节点名称 | `UnknownNode` | `MacroContext.getNodeName()` |
| `%NETWORK_NAME%` | 网络名称 | `UnknownNetwork` | `MacroContext.getNetworkName()` |
| `%BUS_TYPE%` | 总线类型 | `CAN` | `MacroContext.getBusType()` |
| `%CHANNEL%` | 通道号 | `1` | `MacroContext.getChannel()` |
| `%LINE_NUMBER%` | 行号 | 实际行号 | 动态计算 |

#### **宏验证和错误处理**

```mermaid
sequenceDiagram
    participant MES as MacroExpansionStage
    participant ME as MacroExpander
    participant MC as MacroContext

    Note over MES,MC: 管道中的宏处理流程

    MES->>ME: validateMacros(sourceCode, filePath)
    ME->>ME: 使用MACRO_PATTERN扫描宏

    loop 对每个发现的宏
        ME->>ME: isKnownMacro(macroName)
        alt 宏未知
            ME->>ME: 创建IssueObject (CAPL-MACRO-001)
            ME->>ME: 添加错误建议
        end
    end

    ME-->>MES: 返回错误列表

    alt 有错误
        MES-->>MES: 返回失败的PreprocessorResult
    else 无错误且包含宏
        MES->>ME: expandMacros(sourceCode, context)

        loop 对每行代码
            ME->>ME: 查找宏模式
            loop 对每个宏
                ME->>ME: resolveMacro(macroName, context, lineNumber)
                alt 内置宏
                    ME->>MC: 调用对应的resolver函数
                    alt 上下文有值
                        MC-->>ME: 返回上下文值
                    else 上下文无值
                        ME->>ME: getDefaultValueForMacro()
                    end
                else 用户定义宏
                    ME->>MC: getUserDefinedMacro(macroName)
                else 未知宏
                    ME->>ME: 抛出MacroExpansionException
                end
                ME->>ME: 替换宏引用
            end
        end

        ME-->>MES: 返回展开后的代码
        MES-->>MES: 返回成功的PreprocessorResult
    end
```

#### **错误处理机制**

**错误类型和处理策略**:

| 错误类型 | 错误代码 | 严重级别 | 处理方式 | 示例 |
|----------|----------|----------|----------|------|
| **未知宏** | CAPL-MACRO-001 | ERROR | 停止预处理，返回错误 | `%UNKNOWN_MACRO%` |
| **语法错误** | - | ERROR | 抛出MacroExpansionException | 格式不正确的宏 |
| **上下文缺失** | - | INFO | 使用默认值，继续处理 | 上下文未提供节点名 |

**实际错误处理示例**:

```java
// MacroExpansionStage 中的错误处理
@Override
public PreprocessorResult process(PreprocessorResult input, PreprocessorContext context) {
    // 1. 验证阶段 - 发现未知宏
    List<IssueObject> macroIssues = macroExpander.validateMacros(
        input.getPreprocessedSource(),
        context.getSourceFileName()
    );

    if (!macroIssues.isEmpty()) {
        // 创建包含错误的失败结果
        return PreprocessorResult.builder()
            .from(input)
            .successful(false)
            .issues(macroIssues)
            .build();
    }

    // 2. 展开阶段 - 处理已知宏
    try {
        MacroContext macroContext = createMacroContext(context);
        String expandedSource = macroExpander.expandMacros(
            input.getPreprocessedSource(),
            macroContext
        );

        return PreprocessorResult.builder()
            .from(input)
            .preprocessedSource(expandedSource)
            .successful(true)
            .build();

    } catch (MacroExpansionException e) {
        // 理论上不应该发生，因为已经验证过
        IssueObject issue = createMacroExpansionIssue(e, context.getSourceFileName());
        return PreprocessorResult.builder()
            .from(input)
            .successful(false)
            .addIssue(issue)
            .build();
    }
}
```

**错误信息格式**:

```java
// 未知宏错误示例
IssueObject issue = new IssueObject(
    "CAPL-MACRO-001",
    "Unknown CAPL macro '%INVALID_MACRO%'. Only predefined CAPL macros are allowed.",
    SeverityLevel.ERROR,
    new SourceLocation(filePath, lineNumber, startColumn, lineNumber, endColumn),
    "Remove the unknown macro or check if it's a typo. Valid CAPL macros are: " +
    String.join(", ", macroExpander.getBuiltInMacroNames()),
    line.trim() // 代码上下文
);
```

#### **测试和使用示例**

**成功的宏处理**:

```capl
// 输入代码
write("Node: %NODE_NAME%, File: %FILE_NAME%");

// 使用统一管道API
PreprocessorResult result = PreprocessorPipelineFactory.preprocessForTesting(sourceCode, "test.cin");

// 输出结果
write("Node: TestNode, File: test.cin");
```

**错误的宏使用**:

```capl
// 输入代码
write("Invalid: %UNKNOWN_MACRO%");

// 验证结果（使用底层API）
MacroExpander expander = new MacroExpander();
List<IssueObject> issues = expander.validateMacros(sourceCode, "test.cin");
// 结果：1个错误
// CAPL-MACRO-001: Unknown CAPL macro '%UNKNOWN_MACRO%' at line 1.
```

**统一管道API使用示例**:

```java
// 1. 基本宏处理（通过管道）
MacroContext context = MacroContext.builder()
    .sourceFileName("engine.cin")
    .nodeName("EngineECU")
    .networkName("PowertrainCAN")
    .busType("CAN")
    .channel(1)
    .build();

PreprocessorPipeline pipeline = PreprocessorPipelineFactory.createMacroOnlyPipeline();
PreprocessorResult result = pipeline.process(sourceCode, context);

// 2. 便利方法（内部使用管道）
PreprocessorResult result = PreprocessorPipelineFactory.preprocessMacrosOnly(sourceCode, context);

// 3. 测试友好的预处理
MacroContext testContext = MacroContext.forTesting("test.cin");
PreprocessorResult testResult = PreprocessorPipelineFactory.preprocessMacrosOnly(sourceCode, testContext);

// 4. 直接使用MacroExpander（底层API）
MacroExpander expander = new MacroExpander();
List<IssueObject> issues = expander.validateMacros(sourceCode, "test.cin");
boolean hasMacros = expander.containsMacros(sourceCode);
```

#### **统一管道架构的优势**

**架构简化**：
- ✅ **单一处理路径**：所有宏处理都通过 `MacroExpansionStage`
- ✅ **消除重复**：移除 `CaplPreprocessor`，避免功能重复
- ✅ **架构一致**：完全符合时序图的管道设计
- ✅ **易于扩展**：添加新的预处理阶段更容易

**便利工厂类设计**：

```java
// PreprocessorPipelineFactory - 提供便利方法
public class PreprocessorPipelineFactory {

    /**
     * 创建只包含宏处理的管道（用于独立宏处理）
     */
    public static PreprocessorPipeline createMacroOnlyPipeline() {
        return PreprocessorPipeline.builder()
            .addStage(new MacroExpansionStage())
            .build();
    }

    /**
     * 创建完整的预处理管道
     */
    public static PreprocessorPipeline createFullPipeline() {
        return PreprocessorPipeline.builder()
            .addStage(new ConditionalCompilationStage())
            .addStage(new IncludeResolutionStage())
            .addStage(new MacroExpansionStage())
            .addStage(new PragmaProcessingStage())
            .build();
    }

    /**
     * 便利方法：只处理宏展开
     */
    public static PreprocessorResult preprocessMacrosOnly(String sourceCode, MacroContext context) {
        PreprocessorPipeline pipeline = createMacroOnlyPipeline();
        PreprocessorContext preprocessorContext = createPreprocessorContext(context);
        return pipeline.process(sourceCode, preprocessorContext);
    }

    /**
     * 便利方法：测试友好的宏处理
     */
    public static PreprocessorResult preprocessForTesting(String sourceCode, String fileName) {
        MacroContext context = MacroContext.forTesting(fileName);
        return preprocessMacrosOnly(sourceCode, context);
    }

    private static PreprocessorContext createPreprocessorContext(MacroContext macroContext) {
        return PreprocessorContext.builder()
            .sourceFileName(macroContext.getSourceFileName())
            .baseFileName(macroContext.getBaseFileName())
            .nodeName(macroContext.getNodeName())
            .networkName(macroContext.getNetworkName())
            .busType(macroContext.getBusType())
            .channel(macroContext.getChannel())
            .build();
    }
}
```

**迁移策略**：
1. **保持API兼容**：通过工厂类提供相同的便利方法
2. **渐进式迁移**：现有代码可以逐步迁移到新的管道API
3. **测试支持**：提供专门的测试便利方法
4. **性能优化**：单阶段管道避免不必要的开销

## 7. LSP Workspace 分析支持

### 7.1 Workspace 分析需求

LSP模式需要支持三种分析范围：

#### **分析范围对比**

| 分析范围 | 触发方式 | 分析目标 | 使用场景 |
|----------|----------|----------|----------|
| **SINGLE_FILE** | didOpen/didChange | 单个打开的文档 | 实时编辑反馈 |
| **PROJECT** | CLI命令行 | 指定的文件列表 | 批量分析 |
| **WORKSPACE** | executeCommand | 整个工作区的所有CAPL文件 | 全面代码检查 |

### 7.2 Workspace 文件发现机制

```mermaid
flowchart TD
    A[Workspace分析请求] --> B[获取WorkspaceContext]
    B --> C[扫描Workspace目录]
    C --> D[过滤CAPL文件]
    D --> E{文件数量检查}

    E -->|文件过多| F[分批处理]
    E -->|文件适中| G[创建VirtualDocument集合]

    F --> F1[按目录分组]
    F1 --> F2[创建批次AnalysisUnit]
    F2 --> H[并发分析]

    G --> G1[设置WORKSPACE范围]
    G1 --> G2[创建单个AnalysisUnit]
    G2 --> I[统一分析]

    H --> J[合并分析结果]
    I --> J
    J --> K[发布Workspace诊断]

    style A fill:#e3f2fd
    style D fill:#fff3e0
    style J fill:#e8f5e8
    style K fill:#f3e5f5
```

### 7.3 Workspace 分析实现

#### **7.3.1 文件发现算法**

```mermaid
classDiagram
    class WorkspaceFileScanner {
        -supportedExtensions: Set~String~
        -excludePatterns: List~String~
        -maxFileCount: int
        +scanWorkspace(workspace: WorkspaceContext) List~VirtualDocument~
        +filterCaplFiles(files: List~Path~) List~Path~
        +createVirtualDocuments(files: List~Path~, vfs: VirtualFileSystem) List~VirtualDocument~
        -shouldExcludeFile(path: Path) boolean
        -isWithinSizeLimit(files: List~Path~) boolean
    }

    class WorkspaceAnalysisStrategy {
        <<interface>>
        +prepareWorkspaceAnalysis(workspace: WorkspaceContext, config: AppConfiguration) List~AnalysisUnit~
    }

    class BatchedWorkspaceAnalysis {
        -batchSize: int
        -maxConcurrency: int
        +prepareWorkspaceAnalysis(workspace: WorkspaceContext, config: AppConfiguration) List~AnalysisUnit~
        -createBatches(documents: List~VirtualDocument~) List~List~VirtualDocument~~
        -createBatchAnalysisUnit(batch: List~VirtualDocument~, config: AppConfiguration) AnalysisUnit
    }

    class UnifiedWorkspaceAnalysis {
        +prepareWorkspaceAnalysis(workspace: WorkspaceContext, config: AppConfiguration) List~AnalysisUnit~
        -createWorkspaceAnalysisUnit(documents: List~VirtualDocument~, workspace: WorkspaceContext) AnalysisUnit
    }

    WorkspaceAnalysisStrategy <|.. BatchedWorkspaceAnalysis
    WorkspaceAnalysisStrategy <|.. UnifiedWorkspaceAnalysis
    WorkspaceFileScanner --> WorkspaceAnalysisStrategy
```

#### **7.3.2 Workspace 分析流程**

```mermaid
sequenceDiagram
    participant Client as VS Code
    participant CLI as CalintCli/LSP
    participant WS as CaplWorkspaceService
    participant Scanner as WorkspaceFileScanner
    participant Strategy as WorkspaceAnalysisStrategy
    participant AU as AnalysisUnit
    participant UAE as UnifiedAnalysisEngine
    participant LDP as LspDiagnosticsPublisher

    Client->>CLI: executeCommand("capl.analyze.workspace")
    CLI->>WS: 处理Workspace分析请求
    WS->>WS: 获取WorkspaceContext
    WS->>Scanner: scanWorkspace(workspace)
    Scanner->>Scanner: 扫描目录结构
    Scanner->>Scanner: 过滤CAPL文件(.capl, .can, .cin)
    Scanner->>Scanner: 应用排除规则
    Scanner-->>WS: 返回文件列表

    WS->>Strategy: prepareWorkspaceAnalysis(workspace, config)

    alt 文件数量适中 (< 100)
        Strategy->>AU: fromWorkspace(workspace, config)
        AU->>AU: 创建WORKSPACE范围的分析单元
        AU-->>Strategy: 返回AnalysisUnit
        Strategy-->>WS: 返回准备好的AnalysisUnit
        WS-->>CLI: 返回AnalysisUnit
        CLI->>UAE: analyze(analysisUnit)
        UAE-->>CLI: 返回AnalysisResult
    else 文件数量过多 (>= 100)
        Strategy->>Strategy: 创建文件批次
        loop 对每个批次
            Strategy->>AU: fromFiles(batchFiles, config)
            AU-->>Strategy: 返回批次AnalysisUnit
        end
        Strategy-->>WS: 返回批次AnalysisUnit列表
        WS-->>CLI: 返回批次AnalysisUnit列表
        loop 对每个批次
            CLI->>UAE: analyze(batchAnalysisUnit)
            UAE-->>CLI: 返回批次AnalysisResult
        end
        CLI->>CLI: 合并所有批次结果
    end

    CLI->>LDP: publishDiagnostics(mappedIssues)
    LDP->>LDP: groupIssuesByFile(issues)
    loop 对每个文件
        LDP->>LDP: convertToDiagnostic(issue)
        LDP->>Client: publishDiagnostics(uri, diagnostics)
    end
    CLI-->>Client: 返回Workspace分析完成结果

    Note over CLI,Client: Workspace分析完成，所有文件错误已发布
```

#### **7.3.3 性能优化策略**

```mermaid
flowchart LR
    subgraph OPTIMIZATION ["性能优化策略"]
        direction TB
        A[文件数量限制<br/>最大1000个文件]
        B[分批处理<br/>每批50-100个文件]
        C[并发分析<br/>线程池管理]
        D[增量分析<br/>只分析变更文件]
        E[缓存机制<br/>复用分析结果]
        F[内存管理<br/>及时清理临时数据]
    end

    subgraph THRESHOLDS ["阈值配置"]
        direction TB
        G[小型项目: < 50文件<br/>统一分析]
        H[中型项目: 50-200文件<br/>分批分析]
        I[大型项目: > 200文件<br/>增量分析]
    end

    OPTIMIZATION --> THRESHOLDS

    style A fill:#e3f2fd
    style D fill:#e8f5e8
    style G fill:#fff3e0
```

### 7.4 配置和定制

#### **7.4.1 Workspace 分析配置**

```mermaid
classDiagram
    class WorkspaceAnalysisConfig {
        -maxFileCount: int
        -batchSize: int
        -maxConcurrency: int
        -excludePatterns: List~String~
        -includePatterns: List~String~
        -enableIncrementalAnalysis: boolean
        -cacheEnabled: boolean
        +getMaxFileCount() int
        +getBatchSize() int
        +shouldExcludeFile(path: String) boolean
        +shouldIncludeFile(path: String) boolean
    }

    class WorkspaceContext {
        -analysisConfig: WorkspaceAnalysisConfig
        -lastFullAnalysis: long
        -changedFiles: Set~String~
        +getAnalysisConfig() WorkspaceAnalysisConfig
        +markFileChanged(uri: String)
        +getChangedFilesSince(timestamp: long) Set~String~
        +needsFullAnalysis() boolean
    }

    WorkspaceContext --> WorkspaceAnalysisConfig
```

#### **7.4.2 增量分析支持**

```mermaid
flowchart TD
    A[文件变更事件] --> B{变更类型}

    B -->|文件修改| C[标记文件为已变更]
    B -->|文件创建| D[添加到分析队列]
    B -->|文件删除| E[清理相关缓存]

    C --> F[检查依赖关系]
    D --> F
    E --> G[更新依赖图]

    F --> H{影响范围}
    H -->|仅当前文件| I[单文件增量分析]
    H -->|影响多文件| J[批量增量分析]

    G --> K[发布诊断更新]
    I --> K
    J --> K

    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#f3e5f5
```

## 8. CLI vs LSP 实现对比

### 8.1 统一流程设计

```mermaid
flowchart LR
    %% CLI 输入 - 左侧
    subgraph CLI_INPUT ["CLI 输入"]
        direction TB
        CLI_START[CLI 启动]
        CLI_PARSE[解析参数]
        CLI_FILES[文件列表]
        CLI_VFS[PhysicalFileSystem]

        CLI_START --> CLI_PARSE --> CLI_FILES --> CLI_VFS
    end

    %% LSP 输入 - 右侧
    subgraph LSP_INPUT ["LSP 输入"]
        direction TB
        LSP_START[LSP 启动]
        LSP_INIT[初始化工作区]
        LSP_CHANGE[文档变更事件]
        LSP_VFS[LspFileSystem]

        LSP_START --> LSP_INIT --> LSP_CHANGE --> LSP_VFS
    end

    %% 统一处理流程 - 中间
    subgraph UNIFIED ["统一处理流程"]
        direction TB
        AU[AnalysisUnit<br/>统一分析单元]
        CONTEXT[PreprocessorContext<br/>统一上下文]
        PIPELINE[PreprocessorPipeline<br/>统一预处理]
        ENGINE[UnifiedAnalysisEngine<br/>统一分析引擎]
        LINTER[LinterEngine<br/>语法语义分析]
        MAPPER[IssueMapper<br/>统一错误映射]

        AU --> CONTEXT --> PIPELINE --> ENGINE
        ENGINE --> LINTER --> MAPPER
    end

    %% 输出处理 - 底部
    subgraph OUTPUT ["输出处理"]
        direction LR
        CLI_OUTPUT[CLI 控制台输出]
        LSP_OUTPUT[LSP 诊断发布]
    end

    %% 连接关系
    CLI_VFS --> AU
    LSP_VFS --> AU
    MAPPER --> CLI_OUTPUT
    MAPPER --> LSP_OUTPUT

    style CLI_INPUT fill:#e1f5fe
    style LSP_INPUT fill:#e8f5e8
    style UNIFIED fill:#f3e5f5
    style OUTPUT fill:#e3f2fd
```

**关键设计原则**:
- ✅ **统一入口**: CLI 和 LSP 都通过 AnalysisUnit 进入统一处理流程
- ✅ **统一上下文**: 都使用 PreprocessorContext 配置预处理参数
- ✅ **统一处理**: 完全相同的预处理管道、分析引擎和错误映射
- ✅ **完整分析**: 预处理 → 语法语义分析 → 错误映射的完整流程
- ✅ **差异化输出**: 仅在最终输出阶段区分 CLI 和 LSP

### 8.2 统一设计 vs 差异化配置

#### **统一的处理流程** ✅

| 处理阶段 | CLI | LSP | 说明 |
|----------|-----|-----|------|
| **分析单元创建** | AnalysisUnit.fromFiles() | AnalysisUnit.fromLspDocument()<br/>AnalysisUnit.fromWorkspace() | 统一的 AnalysisUnit 接口 |
| **预处理上下文** | PreprocessorContext | PreprocessorContext | 完全相同的上下文配置 |
| **预处理管道** | PreprocessorPipeline | PreprocessorPipeline | 完全相同的处理逻辑 |
| **统一分析引擎** | UnifiedAnalysisEngine | UnifiedAnalysisEngine | 完全相同的分析协调逻辑 |
| **语法语义分析** | LinterEngine | LinterEngine | 完全相同的分析引擎 |
| **错误映射** | IssueMapper | IssueMapper | 完全相同的映射逻辑 |

#### **差异化的配置参数** ⚙️

| 配置方面 | CLI | LSP | 原因 |
|----------|-----|-----|------|
| **文件系统** | PhysicalFileSystem | LspFileSystem | 文件来源不同 |
| **分析范围** | 可配置 (PROJECT/WORKSPACE) | SINGLE_FILE/WORKSPACE | 使用场景不同 |
| **输出方式** | 控制台 + 文件 | publishDiagnostics | 输出目标不同 |
| **性能策略** | 批量处理优化 | 实时响应优化 | 性能要求不同 |

**关键原则**:
- 🎯 **核心逻辑统一**: 预处理、语法语义分析、错误映射完全一致
- ⚙️ **配置参数化**: 通过配置适应不同使用场景
- 🔧 **接口抽象**: 通过 VFS 抽象文件访问差异
- 🔄 **流程完整**: 从预处理到最终分析结果的完整处理链

### 8.3 统一分析引擎

```mermaid
classDiagram
    class UnifiedAnalysisEngine {
        -preprocessor: PreprocessorPipeline
        -linterEngine: LinterEngine
        -issueMapper: IssueMapper
        -tempFileManager: TempFileManager
        +analyze(unit: AnalysisUnit) AnalysisResult
        +analyzeMultiple(units: List~AnalysisUnit~) List~AnalysisResult~
        -createTempFile(content: String) File
        -cleanupTempFiles()
    }

    class AnalysisResult {
        -successful: boolean
        -issues: List~IssueObject~
        -sourceMap: SourceMap
        -preprocessResult: PreprocessorResult
        -analysisTime: long
        -fromCache: boolean
        +isSuccessful() boolean
        +getIssues() List~IssueObject~
        +getAnalysisTime() long
        +isFromCache() boolean
    }

    class TempFileManager {
        -tempFiles: Set~File~
        +createTempFile(content: String, suffix: String) File
        +cleanup()
        +getTempFileCount() int
    }

    class AnalysisContext {
        -configuration: AppConfiguration
        -vfs: VirtualFileSystem
        -workspaceRoot: String
        -sessionId: String
        +getConfiguration() AppConfiguration
        +getVirtualFileSystem() VirtualFileSystem
    }

    UnifiedAnalysisEngine --> AnalysisResult
    UnifiedAnalysisEngine --> TempFileManager
    UnifiedAnalysisEngine --> AnalysisContext
```

**统一分析引擎工作流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant UAE as UnifiedAnalysisEngine
    participant PP as PreprocessorPipeline
    participant LE as LinterEngine
    participant IM as IssueMapper
    participant TFM as TempFileManager

    Client->>UAE: analyze(analysisUnit)

    UAE->>PP: process(primaryDocument, context)
    PP-->>UAE: preprocessResult

    alt 预处理成功
        UAE->>TFM: createTempFile(processedContent)
        TFM-->>UAE: tempFile

        UAE->>LE: analyzeFiles([tempFile], configuration)
        LE-->>UAE: rawIssues

        UAE->>IM: mapIssuesBackToOriginal(rawIssues, sourceMap)
        IM-->>UAE: mappedIssues

        UAE->>TFM: cleanup()
        UAE-->>Client: AnalysisResult.success(mappedIssues)

    else 预处理失败
        UAE-->>Client: AnalysisResult.failure(preprocessIssues)
    end
```

**分析结果构建流程**:

```mermaid
flowchart LR
    subgraph PREPROCESS ["预处理阶段"]
        direction TB
        A[开始分析]
        B[预处理文档]
        C{预处理成功?}
        D[返回预处理错误]

        A --> B --> C
        C -->|否| D
    end

    subgraph ANALYSIS ["分析阶段"]
        direction TB
        E[创建临时文件]
        F[调用 LinterEngine]
        G{分析成功?}
        H[返回分析错误]

        E --> F --> G
        G -->|否| H
    end

    subgraph MAPPING ["映射阶段"]
        direction TB
        I[映射错误位置]
        J[构建分析结果]
        K[清理临时文件]
        L[返回成功结果]

        I --> J --> K --> L
    end

    subgraph STATS ["统计记录"]
        direction TB
        M[记录失败统计]
        N[记录成功统计]
    end

    %% 主流程
    C -->|是| E
    G -->|是| I
    MAPPING --> N

    %% 错误流程
    D --> M
    H --> M

    style D fill:#ffebee
    style H fill:#ffebee
    style L fill:#e8f5e8
    style M fill:#fff3e0
    style N fill:#e3f2fd
```

## 9. VFS (虚拟文件系统) 核心设计

### 9.1 设计价值与必要性

VFS 是解决 CLI 和 LSP 文件访问差异的关键架构组件。

#### **核心问题**
- **CLI**: 直接访问文件系统，处理文件路径列表
- **LSP**: 混合访问（内存文档 + 文件系统），实时处理文档变更
- **Include 处理**: LSP 环境下 include 文件可能不在内存中

#### **VFS 解决方案**
通过统一的文件访问接口，抽象掉 CLI 和 LSP 的文件访问差异：

```mermaid
graph TB
    subgraph VFS_SOLUTION ["VFS 统一方案"]
        VFS[VirtualFileSystem<br/>统一接口]
        PFS[PhysicalFileSystem<br/>CLI 使用]
        LSPFS[LspFileSystem<br/>LSP 使用]

        VFS --> PFS
        VFS --> LSPFS
    end

    subgraph BENEFITS ["核心价值"]
        B1[统一的文件访问]
        B2[一致的预处理逻辑]
        B3[简化的错误处理]
        B4[更好的可测试性]
    end

    VFS_SOLUTION --> BENEFITS

    style VFS fill:#e8f5e8
    style BENEFITS fill:#e3f2fd
```

### 9.2 VFS 实现细节

#### **文件系统实现策略**

```mermaid
flowchart LR
    subgraph PHYSICAL ["PhysicalFileSystem"]
        direction TB
        P1[直接文件系统访问]
        P2[路径解析和验证]
        P3[文件读取和缓存]
        P1 --> P2 --> P3
    end

    subgraph LSP_FS ["LspFileSystem"]
        direction TB
        L1[内存文档优先]
        L2[智能回退机制]
        L3[文档同步管理]
        L1 --> L2 --> L3
    end

    subgraph MEMORY ["MemoryFileSystem"]
        direction TB
        M1[纯内存存储]
        M2[快速访问]
        M3[测试友好]
        M1 --> M2 --> M3
    end

    style PHYSICAL fill:#e1f5fe
    style LSP_FS fill:#e8f5e8
    style MEMORY fill:#fff3e0
```

**注**: VFS核心接口定义请参考第2.3.1节，此处重点说明实现细节和扩展机制。

### 9.3 VFS 设计总结

#### **核心优势**

- ✅ **统一抽象**: 为 CLI 和 LSP 提供相同的文件访问接口
- ✅ **透明切换**: 上层代码无需关心文件来源（内存 vs 文件系统）
- ✅ **智能回退**: LSP 环境下优先使用内存文档，自动回退到文件系统
- ✅ **一致错误处理**: 统一的异常处理和错误信息

#### **解决的关键问题**

| 问题 | 传统方案 | VFS 方案 |
|------|----------|----------|
| **文件访问差异** | CLI 用 Files.readString<br/>LSP 用 openDocuments.get | 统一使用 vfs.readFile |
| **Include 文件处理** | 复杂的条件判断逻辑 | VFS 自动处理不同文件源 |
| **错误处理不一致** | 两套不同的错误处理 | 统一的异常处理机制 |
| **代码重复** | CLI 和 LSP 各自实现 | 共享相同的处理逻辑 |

## 10. 总结

### 10.1 设计成果

本文档设计了一个完整的CAPL统一预处理机制，实现了以下核心目标：

#### **统一性成果** ✅
- **架构统一**: CLI和LSP使用完全相同的预处理管道和分析引擎
- **接口统一**: 通过VFS抽象统一了文件访问方式
- **错误处理统一**: 建立了一致的错误分级和处理策略
- **结果格式统一**: 统一的源映射和错误定位机制

#### **完整性成果** ✅
- **预处理指令**: 支持#include、#ifdef、宏展开、#pragma等所有指令
- **智能依赖处理**: 自动去重和循环打断，不阻止处理流程
- **精确源映射**: 支持多层嵌套的位置追踪和错误定位
- **错误恢复**: 智能处理可恢复问题，最大化处理成功率

#### **准确性成果** ✅
- **位置映射**: 精确到行列的源位置映射
- **错误定位**: 错误能准确定位到原始文件的具体位置
- **上下文保持**: 保持完整的错误上下文信息
- **多文件支持**: 支持跨文件的错误分发和显示

### 10.2 技术创新点

#### **智能依赖解析** 🧠
- 传统方案将循环依赖视为错误，本设计将其视为可解决的问题
- 自动选择最优打断点，保持依赖结构稳定性
- 智能去重处理，避免重复包含同一文件

#### **统一VFS架构** 🏗️
- 抽象了CLI和LSP的文件访问差异
- 支持内存文档优先，自动回退到文件系统
- 为测试提供了MemoryFileSystem支持

#### **管道式预处理** ⚙️
- 模块化的处理阶段，易于扩展和维护
- 统一的错误处理策略，区分阻塞性错误和可恢复警告
- 完整的源映射追踪，支持复杂的预处理场景

### 10.3 实施建议

#### **开发优先级** 📋
1. **第一阶段**: 实现VFS和AnalysisUnit基础架构
2. **第二阶段**: 实现预处理管道和各个处理阶段
3. **第三阶段**: 实现源映射和错误定位机制
4. **第四阶段**: 集成到现有CLI和LSP系统
5. **第五阶段**: 实现LSP Workspace分析功能

#### **测试策略** 🧪
- **单元测试**: 每个组件独立测试，确保功能正确性
- **集成测试**: 端到端测试完整的预处理流程
- **性能测试**: 验证大文件和复杂依赖的处理性能
- **兼容性测试**: 确保与现有CAPL代码的兼容性

#### **风险控制** ⚠️
- **渐进式迁移**: 逐步替换现有预处理逻辑，降低风险
- **回退机制**: 保留原有处理方式作为备选方案
- **充分测试**: 在生产环境部署前进行全面测试

### 10.4 预期效果

#### **开发体验提升** 👨‍💻
- **一致性**: CLI和LSP提供完全一致的分析结果
- **准确性**: 错误能精确定位到原始文件位置
- **智能化**: 自动处理常见的依赖问题

#### **维护成本降低** 💰
- **代码复用**: 消除CLI和LSP的重复实现
- **模块化**: 易于添加新功能和修复问题
- **测试友好**: 统一的架构便于自动化测试

#### **用户体验改善** 🎯
- **错误定位**: 准确的错误位置和上下文信息
- **智能提示**: 提供有用的修复建议
- **性能优化**: 更快的分析速度和更低的资源消耗

---

**本设计文档为CAPL Linter的预处理机制提供了完整的技术方案，通过统一架构、智能处理和精确映射，显著提升了工具的可用性和可维护性。**