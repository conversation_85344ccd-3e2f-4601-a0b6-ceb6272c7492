---
marp: true
theme: default
paginate: true
backgroundColor: #ffffff
color: #333333
---

# CAPL Linter (calint) 架构设计与开发指南

**静态代码分析工具的完整技术解析**

---
*为开发团队提供的深度技术文档*
*基于 calint v0.1.0-SNAPSHOT*

---

## 目录

1. **项目概览与设计原则**
2. **CAPL 语言特性与支持**
3. **6层错误检测架构**
4. **系统架构设计**
5. **核心组件详解**
6. **设计模式与原则**
7. **宏处理与预处理**
8. **配置管理体系**
9. **测试策略与质量保证**
10. **扩展开发指南**

---

## 1. 项目概览与设计原则

### 什么是 calint？

**CAPL Linter (calint)** 是一个专为 CAPL (Communication Access Programming Language) 设计的静态代码分析工具。

### 核心设计原则
- 🎯 **准确性优先**: 保证分析结果的准确性，避免误报
- ⚡ **高效性**: 快速处理大规模 CAPL 代码文件
- 🔧 **可配置性**: 允许用户根据项目需求定制检查规则
- 📈 **模块化**: 各功能模块低耦合，易于维护和扩展
- 🛠️ **易用性**: 提供简洁明了的命令行接口和报告

### 项目范围
- ✅ **支持**: 词法/语法分析、语义分析、代码风格检查
- ✅ **输入**: 单个或多个 `.can` / `.cin` 文件
- ✅ **输出**: 多种格式报告 (Console, JSON, HTML)
- ❌ **不包含**: 运行时行为分析、代码自动修复

---

## 2. CAPL 语言特性与支持

### CAPL 语言简介
- **Vector 公司**开发的专用脚本语言
- 广泛应用于**汽车电子 ECU 测试**和**仿真**
- 支持 **CAN、FlexRay、MOST、ARINC 429/664** 等多种通信协议

### 支持的语言特性
```capl
// 包含指令
includes {
  #include "myLibrary.cin"
}

// 变量声明
variables {
  int counter;
  message 0x100 engineData;
  timer myTimer;
  signal CAN1.EngineRPM.RPM rpmSignal;
}

// 事件处理
on timer myTimer {
  counter++;
}

// 网络消息处理
on message engineData {
  write("Engine RPM: %d", rpmSignal.phys);
}

// 函数定义
void myFunction(int param) {
  // 函数体
}
```

### CAPL 特有语法支持

**事件处理器**:
```capl
// 系统变量事件
on sysvar CAN1.MySysVar { ... }

// FlexRay 事件
on frFrame MyFlexRayFrame { ... }

// ARINC 429 事件
on a429Word MyA429Word { ... }

// 键盘事件
on key 'a' { ... }

// 系统生命周期事件
on preStart { ... }
on start { ... }
on stopMeasurement { ... }
```

**宏支持**:
```capl
// 支持的 CAPL 宏
write("Node: %NODE_NAME%, File: %FILE_NAME%");
write("Line: %LINE_NUMBER%, Channel: %CHANNEL%");
```

---

## 3. 6层错误检测架构

### 错误检测层次

calint 采用**6层渐进式错误检测架构**，从基础语法到高级代码质量：

```
┌─────────────────────────────────────────────────────────┐
│                    6. 规则检查阶段                        │
│              代码质量和风格问题 (CALINT-*)               │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    5. 语义分析阶段                        │
│            符号表、类型检查 (SEMANTIC-*)                │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    4. AST构建阶段                        │
│              自定义AST构建 (AST-*)                      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    3. 语法分析阶段                        │
│              语法规则匹配 (PARSER-*)                    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    2. 词法分析阶段                        │
│              Token识别 (LEXER-*)                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    1. 预处理阶段                         │
│          宏展开、文件包含 (CAPL-MACRO-*, CAPL-INCLUDE-*) │
└─────────────────────────────────────────────────────────┘
```

---

## 4. 系统架构设计

### 整体架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CLI/LSP       │───▶│  Configuration   │───▶│  Input          │
│   Gateway       │    │  Manager         │    │  Processor      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Report        │◀───│  Linter Engine   │◀───│  Preprocessor   │
│   Generator     │    │  (Coordinator)   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌────────┼────────┐
                       ▼        ▼        ▼
              ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
              │   Lexer/    │ │    AST      │ │  Semantic   │
              │   Parser    │ │  Builder    │ │  Analyzer   │
              └─────────────┘ └─────────────┘ └─────────────┘
                                              │
                                              ▼
                                    ┌─────────────┐
                                    │    Rule     │
                                    │   Engine    │
                                    └─────────────┘
```

```plantuml

@startuml CAPL Linter (calint) System Architecture

skinparam componentStyle rectangle
skinparam backgroundColor white
skinparam monochrome false
skinparam shadowing false
skinparam defaultFontName Arial
skinparam defaultFontSize 12
skinparam arrowColor #333333
skinparam componentBorderColor #333333

' 定义组件
component "CLI/LSP Gateway" as Gateway {
  [Command Line Interface]
  [Language Server Protocol]
}

component "Configuration Manager" as ConfigManager {
  [Rule Configuration]
  [Analysis Settings]
}

component "Input Processor" as InputProcessor {
  [File Collection]
  [Source Management]
}

component "Preprocessor" as Preprocessor {
  [Macro Expansion]
  [Include Resolution]
}

component "Linter Engine" as LinterEngine {
  [Analysis Coordinator]
}

component "Lexer/Parser" as LexerParser {
  [ANTLR Lexer]
  [ANTLR Parser]
  [Error Recovery]
}

component "AST Builder" as ASTBuilder {
  [Node Construction]
  [Source Mapping]
}

component "Semantic Analyzer" as SemanticAnalyzer {
  [Symbol Table]
  [Type Checker]
  [Scope Analyzer]
}

component "Rule Engine" as RuleEngine {
  [Rule Registry]
  [Rule Executor]
  [Quality Metrics]
}

component "Report Generator" as ReportGenerator {
  [Console Reporter]
  [JSON Reporter]
  [HTML Reporter]
}

' 定义关系
Gateway --> ConfigManager
Gateway --> InputProcessor
ConfigManager --> LinterEngine
InputProcessor --> Preprocessor
Preprocessor --> LinterEngine
LinterEngine --> LexerParser
LinterEngine --> ASTBuilder
LinterEngine --> SemanticAnalyzer
SemanticAnalyzer --> RuleEngine
LinterEngine --> ReportGenerator

' 添加说明
note bottom of Gateway
  系统入口点
  处理命令行参数或LSP请求
end note

note bottom of Preprocessor
  处理宏展开和文件包含
  生成统一代码流
end note

note bottom of ASTBuilder
  构建自定义抽象语法树
  170+ 节点类型支持
end note

note bottom of SemanticAnalyzer
  符号表构建
  类型检查
  作用域分析
end note

note bottom of RuleEngine
  执行代码质量和风格检查规则
  可配置的规则集
end note

@enduml
```

### 核心模块说明

- **CLI/LSP Gateway**: 系统入口点，解析命令行参数或LSP请求
- **Configuration Manager**: 加载和验证配置文件，管理规则参数
- **Input Processor**: 收集CAPL源文件，处理文件系统交互
- **Preprocessor**: 处理宏展开和文件包含，生成统一代码流
- **Lexer/Parser**: ANTLR生成的词法和语法分析器
- **AST Builder**: 构建自定义抽象语法树
- **Semantic Analyzer**: 符号表构建、类型检查、作用域分析
- **Rule Engine**: 执行代码质量和风格检查规则
- **Report Generator**: 生成多格式分析报告

---

## 5. 核心组件详解

### 5.1 预处理层 (Preprocessing Layer)

**PreprocessingInputProcessor**
- 🔄 **宏展开**: 严格验证和替换 CAPL 宏
- 📁 **文件包含**: 递归处理 `#include` 指令
- ✅ **错误检测**: 未知宏和循环包含检测

**支持的 CAPL 宏**:
```java
// 预定义的 CAPL 宏列表
Set<String> PREDEFINED_MACROS = Set.of(
    "BASE_FILE_NAME", "BASE_FILE_NAME_NO_EXT",
    "FILE_NAME", "FILE_NAME_NO_EXT",
    "NODE_NAME", "NETWORK_NAME",
    "BUS_TYPE", "CHANNEL", "LINE_NUMBER"
);

// 宏验证和替换
Pattern MACRO_PATTERN = Pattern.compile("%([A-Za-z_][A-Za-z0-9_]*)%");
```

**错误示例**:
```capl
// CAPL-MACRO-001: 未知宏使用
write("%UNKNOWN_MACRO%");

// CAPL-INCLUDE-001: 包含文件不存在
#include "nonexistent.cin"
```

---

### 5.2 词法语法分析层 (Lexical & Syntax Analysis)

**ANTLR4 集成架构**
- 📝 **语法定义**: 基于完整的 `CAPL.g4` 文件
- 🔍 **词法分析**: 自动生成的 `CAPLLexer`
- 🌳 **语法分析**: 自动生成的 `CAPLParser`
- 🚨 **错误恢复**: 增强的错误恢复机制

**错误监听器**:
```java
public class CaplAntlrErrorListener extends BaseErrorListener {
    @Override
    public void syntaxError(Recognizer<?, ?> recognizer,
                           Object offendingSymbol,
                           int line, int charPositionInLine,
                           String msg, RecognitionException e) {
        // 转换为统一的 IssueObject
        IssueObject issue = new IssueObject(
            sourceFile.getAbsolutePath().toString(),
            line, charPositionInLine,
            SeverityLevel.ERROR,
            "PARSER-SYNTAX-ERROR",
            msg
        );
    }
}
```

---

### 5.3 AST 构建层 (AST Construction)

**自定义 AST 设计原则**
- 🎯 **语义丰富**: 比 ANTLR Parse Tree 更适合分析
- 🏗️ **节点层次**: 完整的节点类型体系 (170+ 节点类型)
- 📍 **位置信息**: 精确的源码定位
- 🔗 **符号链接**: 支持符号表引用

**核心节点类型**:
```java
public enum NodeType {
    // 程序结构
    PROGRAM_UNIT, INCLUDES_SECTION, VARIABLES_SECTION,

    // 声明类型
    VARIABLE_DECLARATION, FUNCTION_DEFINITION, EVENT_HANDLER,

    // 表达式类型
    BINARY_EXPRESSION, UNARY_EXPRESSION, FUNCTION_CALL_EXPRESSION,

    // CAPL 特有
    MESSAGE_DEFINITION, TIMER_DECLARATION, SIGNAL_REFERENCE,

    // 语句类型
    IF_STATEMENT, WHILE_STATEMENT, FOR_STATEMENT,
    // ... 170+ 节点类型
}
```

**Helper 类架构**:
```java
// 模块化的 AST 构建 Helper
ExpressionHelper.buildBinaryExpression(ctx);
StatementHelper.buildIfStatement(ctx);
DeclarationHelper.buildVariableDeclaration(ctx);
EventHandlerHelper.buildEventHandler(ctx);
```

---

### 5.4 语义分析层 (Semantic Analysis)

**DefaultSemanticAnalysisEngine**
- 🔍 **符号表管理**: 分层作用域的符号表
- 🎯 **类型检查**: CAPL 类型系统验证
- 📊 **作用域分析**: 标识符可见性检查
- 🔗 **引用解析**: 符号引用链接

**核心分析能力**:
```java
// 符号表相关问题
SEMANTIC-UNDECLARED-001: "未声明变量使用"
SEMANTIC-REDEFINITION-001: "符号重定义"
SEMANTIC-SCOPE-VIOLATION-001: "作用域违规"

// 类型检查问题
SEMANTIC-TYPE-MISMATCH-001: "类型不匹配"
SEMANTIC-FUNC-ARG-MISMATCH-001: "函数参数不匹配"
SEMANTIC-INVALID-ASSIGNMENT-001: "无效赋值操作"

// CAPL 特定问题
SEMANTIC-CAPL-MESSAGE-001: "Message类型声明错误"
SEMANTIC-CAPL-TIMER-001: "Timer类型声明错误"
```

---

### 5.5 规则引擎层 (Rule Engine)

**SimpleRuleEngine 架构**
- 🔧 **规则注册**: 动态规则发现和注册
- ⚙️ **配置驱动**: 基于配置文件的规则启用/禁用
- 📊 **上下文感知**: 丰富的分析上下文信息
- 🎯 **类型化检查**: 针对特定节点类型的规则

**规则分类**:
```java
// 代码质量规则
CALINT-NAMING-001: "变量命名不规范"
CALINT-MAGIC-NUMBER-001: "魔术数字使用"
CALINT-UNUSED-VARIABLE-001: "未使用的变量"

// CAPL 最佳实践规则
CALINT-EVENT-BLOCKING-001: "事件函数中的耗时操作"
CALINT-RESOURCE-MANAGEMENT-001: "资源管理问题"

// 代码复杂度规则
CALINT-FUNC-COMPLEXITY-001: "函数复杂度过高"
CALINT-NESTING-DEPTH-001: "嵌套层级过深"
```

**规则执行流程**:
```java
// 1. 加载启用的规则
List<Rule> enabledRules = ruleEngine.loadEnabledRules(configuration);

// 2. 遍历 AST 并应用规则
for (Map.Entry<SourceFile, AstNode> entry : asts.entrySet()) {
    AnalysisContext context = new AnalysisContext(sourceFile, astRoot, ruleConfig);
    for (Rule rule : enabledRules) {
        List<IssueObject> issues = rule.check(astRoot, context);
        allIssues.addAll(issues);
    }
}
```

---

## 6. 设计模式与原则

### 6.1 访问者模式 (Visitor Pattern)

**AstVisitor 接口设计**:
```java
public interface AstVisitor<R, C> {
    // 通用访问方法
    R visit(AstNode node, C context);

    // 特定节点类型访问方法 (170+ 方法)
    R visitProgramUnit(ProgramUnitNode node, C context);
    R visitVariableDeclaration(VariableDeclarationNode node, C context);
    R visitFunctionDefinition(FunctionDefinitionNode node, C context);
    R visitEventHandler(EventHandlerNode node, C context);
    R visitBinaryExpression(BinaryExpressionNode node, C context);
    // ... 更多访问方法
}
```

**RuleBaseAstVisitor 基类**:
```java
public abstract class RuleBaseAstVisitor<R, C> implements AstVisitor<R, C> {
    // 提供默认实现，子类只需重写感兴趣的方法
    @Override
    public R visit(AstNode node, C context) {
        // 默认遍历子节点
        for (AstNode child : node.getChildren()) {
            visit(child, context);
        }
        return null;
    }
}
```

**优势**:
- 🔄 **操作分离**: 将算法与数据结构分离
- 📈 **易于扩展**: 添加新操作无需修改节点类
- 🎯 **类型安全**: 编译时检查访问方法
- 🏗️ **模块化**: 不同分析逻辑独立实现

---

### 6.2 策略模式 (Strategy Pattern)

**Rule 接口设计**:
```java
public interface Rule {
    String getRuleId();           // 规则唯一标识
    String getName();             // 规则名称
    String getDescription();      // 规则描述
    SeverityLevel getDefaultSeverity();  // 默认严重级别

    // 核心检查方法
    List<IssueObject> check(AstNode node, AnalysisContext context);
}
```

**规则实现示例**:
```java
@RuleIdentifier("CAPL-VAR-001")
public class UnusedVariableRule extends RuleBaseAstVisitor<Void, AnalysisContext>
                                implements Rule {
    @Override
    public List<IssueObject> check(AstNode node, AnalysisContext context) {
        this.issues = new ArrayList<>();
        this.context = context;
        visit(node, context);  // 遍历 AST
        return issues;
    }

    @Override
    public Void visitVariableDeclaration(VariableDeclarationNode node,
                                        AnalysisContext context) {
        // 检查变量是否被使用
        if (!isVariableUsed(node, context)) {
            issues.add(createIssue(node, "Variable never used"));
        }
        return null;
    }
}
```

---

### 6.3 Helper 模式 (Helper Pattern)

**模块化 Helper 设计**:
```java
// AST 构建 Helper 类
public class ExpressionHelper {
    public static ExpressionNode buildBinaryExpression(
        CAPLParser.BinaryExpressionContext ctx) {
        // 构建二元表达式节点
    }
}

public class StatementHelper {
    public static StatementNode buildIfStatement(
        CAPLParser.IfStatementContext ctx) {
        // 构建 if 语句节点
    }
}

// 语义分析 Helper 类
public class TypeCheckingHelper {
    public static boolean isTypeCompatible(TypeInfo source, TypeInfo target) {
        // 类型兼容性检查
    }
}
```

**Helper 使用原则**:
- 🎯 **单一职责**: 每个 Helper 专注特定功能
- 🔧 **静态方法**: 无状态的工具方法
- 📦 **模块化**: 按功能领域组织
- 🔄 **可重用**: 多个组件可共享使用

---

## 7. 宏处理与预处理

### 7.1 CAPL 宏验证设计

**严格的宏验证策略**:
- ✅ **预定义宏**: 只允许 CAPL 规范中定义的宏
- ❌ **未知宏**: 视为错误，停止分析
- 🔄 **强制替换**: 所有有效宏必须被替换
- 📍 **精确定位**: 提供详细的错误位置信息

**支持的 CAPL 宏列表**:
```java
public static final Map<String, String> PREDEFINED_MACROS = Map.of(
    "BASE_FILE_NAME", "unknown.can",
    "BASE_FILE_NAME_NO_EXT", "unknown",
    "FILE_NAME", "unknown.cin",
    "FILE_NAME_NO_EXT", "unknown",
    "NODE_NAME", "UnknownNode",
    "NETWORK_NAME", "UnknownNetwork",
    "BUS_TYPE", "CAN",
    "CHANNEL", "1",
    "LINE_NUMBER", "实际行号"  // 特殊处理
);
```

### 7.2 宏处理流程

**两阶段处理**:
```java
// 阶段1: 宏验证
List<IssueObject> issues = preprocessor.validateMacros(sourceCode, filePath);
if (!issues.isEmpty()) {
    return failureResult;  // 发现未知宏，停止分析
}

// 阶段2: 宏替换
PreprocessorResult result = preprocessor.preprocess(sourceCode, context);
```

**错误示例**:
```capl
// ✅ 正确的宏使用
write("Node: %NODE_NAME%, File: %FILE_NAME%");
// 输出: write("Node: TestNode, File: test.cin");

// ❌ 错误的宏使用
write("Invalid: %UNKNOWN_MACRO%");
// 错误: CAPL-MACRO-001: Unknown CAPL macro '%UNKNOWN_MACRO%'
```

### 7.3 MacroContext 设计

**上下文驱动的宏值**:
```java
// 测试友好的上下文
MacroContext context = MacroContext.forTesting("test.cin");

// 完整的上下文构建
MacroContext context = MacroContext.builder()
    .sourceFileName("engine_test.cin")
    .nodeName("EngineECU")
    .networkName("PowertrainCAN")
    .busType("CAN")
    .channel(1)
    .build();
```

---

## 8. 扩展性机制

### 8.1 规则扩展机制

**添加新规则的完整流程**:

1. **实现 Rule 接口**:
```java
@RuleIdentifier("CAPL-CUSTOM-001")
public class CustomNamingRule extends RuleBaseAstVisitor<Void, AnalysisContext>
                              implements Rule {
    @Override
    public String getRuleId() { return "CAPL-CUSTOM-001"; }

    @Override
    public String getName() { return "Custom Naming Convention"; }

    @Override
    public SeverityLevel getDefaultSeverity() { return SeverityLevel.WARNING; }

    @Override
    public List<IssueObject> check(AstNode node, AnalysisContext context) {
        this.issues = new ArrayList<>();
        visit(node, context);
        return issues;
    }

    @Override
    public Void visitVariableDeclaration(VariableDeclarationNode node,
                                        AnalysisContext context) {
        String varName = node.getVariableName();
        if (!varName.matches("^[a-z][a-zA-Z0-9]*$")) {
            issues.add(createIssue(node, "Variable should use camelCase"));
        }
        return super.visitVariableDeclaration(node, context);
    }
}
```

2. **配置规则元数据**:
```yaml
# rules-config.yaml
rules:
  CAPL-CUSTOM-001:
    category: STYLE
    description: "Custom variable naming convention"
    defaultSeverity: WARNING
    enabled: true
    parameters:
      pattern: "^[a-z][a-zA-Z0-9]*$"
```

3. **规则自动发现**: 通过反射或注解扫描自动注册

### 8.2 Helper 类扩展

**新增 Helper 类的步骤**:
```java
// 1. 创建新的 Helper 类
public class CustomAnalysisHelper {
    public static boolean isComplexExpression(ExpressionNode expr) {
        // 自定义复杂度分析逻辑
        return calculateComplexity(expr) > THRESHOLD;
    }

    public static List<IssueObject> validateCustomPattern(AstNode node) {
        // 自定义验证逻辑
    }
}

// 2. 在现有组件中使用
public class CustomSemanticAnalyzer {
    public void analyzeComplexity(ExpressionNode expr) {
        if (CustomAnalysisHelper.isComplexExpression(expr)) {
            // 处理复杂表达式
        }
    }
}
```

---

## 9. 配置管理体系

### 9.1 配置文件结构

**应用配置 (calint.json)**:
```json
{
  "inputPaths": ["src/test/resources/test_samples"],
  "excludedPaths": ["src/main/capl/legacy/**"],
  "defaultInputCharset": "UTF-8",
  "defaultSeverityThreshold": "INFO",
  "projectIncludePaths": ["include/common"],
  "reportFormat": "JSON",
  "reportOutputPath": "reports/calint-report.json",
  "failOnSeverity": true
}
```

**规则配置 (rules-config.yaml)**:
```yaml
rules:
  # 语义错误规则
  CAPL-VAR-001:
    enabled: true
    severity: ERROR
    description: "Undeclared variable usage"

  # 代码风格规则
  CAPL-STYLE-001:
    enabled: true
    severity: WARNING
    description: "Variable naming convention"
    parameters:
      pattern: "^[a-z][a-zA-Z0-9]*$"

  # CAPL 最佳实践规则
  CAPL-EVENT-001:
    enabled: true
    severity: WARNING
    description: "Avoid blocking operations in event handlers"
    parameters:
      maxExecutionTime: 100  # ms
```

### 9.2 配置管理架构

**配置优先级层次**:
```
1. 命令行参数 (最高优先级)
   ├── --config <file>
   ├── --severity <level>
   └── --rules <rule1,rule2>

2. 项目配置文件
   ├── ./calint.json
   └── ./rules-config.yaml

3. 用户配置文件
   ├── ~/.calint/config.json
   └── ~/.calint/rules.yaml

4. 默认配置 (最低优先级)
   ├── 内置默认值
   └── 系统预设规则
```

**配置加载流程**:
```java
public class AppConfigManager {
    public AppConfiguration loadConfiguration(Optional<Path> configFile) {
        // 1. 加载默认配置
        AppConfiguration config = getDefaultConfiguration();

        // 2. 合并用户全局配置
        config = mergeWithUserConfig(config);

        // 3. 合并项目配置
        config = mergeWithProjectConfig(config, configFile);

        // 4. 应用命令行覆盖
        config = applyCommandLineOverrides(config);

        return config;
    }
}
```

### 9.3 规则配置管理

**RulesConfigManager 设计**:
```java
public class RulesConfigManager {
    // 规则定义 (元数据)
    private Map<String, RuleDefinition> ruleDefinitions;

    // 规则配置 (用户设置)
    private Map<String, RuleConfig> ruleConfigs;

    public Optional<RuleConfig> getEffectiveRuleConfig(String ruleId) {
        RuleDefinition definition = ruleDefinitions.get(ruleId);
        RuleConfig userConfig = ruleConfigs.get(ruleId);

        // 合并默认设置和用户配置
        return mergeRuleConfig(definition, userConfig);
    }
}
```

**规则配置示例**:
```java
// 规则定义 (rule-metadata.yaml)
RuleDefinition definition = new RuleDefinition(
    "CAPL-NAMING-001",
    RuleCategory.STYLE,
    "Variable naming convention",
    SeverityLevel.WARNING
);

// 用户配置 (rules-config.yaml)
RuleConfig userConfig = new RuleConfig(
    "CAPL-NAMING-001",
    true,  // enabled
    SeverityLevel.ERROR,  // 覆盖默认严重级别
    Map.of("pattern", "^g[A-Z][a-zA-Z0-9]*$")  // 全局变量以g开头
);
```

---

## 10. 测试策略与质量保证

### 10.1 分层测试架构

基于项目的6层错误检测架构，采用对应的分层测试策略：

```
┌─────────────────────────────────────────────────────────┐
│                   系统测试层                              │
│              端到端功能验证 (5个测试)                      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   集成测试层                              │
│            组件间交互验证 (12个测试)                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   单元测试层                              │
│           Helper类和核心组件 (53个测试)                    │
└─────────────────────────────────────────────────────────┘
```

### 10.2 已完成的测试覆盖

**✅ 单元测试层 (53个测试)**:
```java
// AST Helper 类测试
CompilationUnitHelperTest        (5个测试)
ParameterDeclarationHelperTest   (6个测试)
CompoundStatementHelperTest      (2个测试)
ProgramUnitHelperTest           (2个测试)
ExpressionHelperTest            (13个测试)
StatementHelperTest             (11个测试)
DeclarationHelperTest           (8个测试)
EventHandlerHelperTest          (6个测试)
```

**✅ 集成测试层 (12个测试)**:
```java
// 组件集成测试
ParserToAstIntegrationTest      (6个测试)
CaplLinterIntegrationTest       (6个测试)

// 覆盖范围:
- 解析器到AST构建的完整流程
- AST到语义分析的集成
- 规则引擎与AST的集成
- 错误处理机制验证
- 边界情况处理
```

**✅ 系统测试层 (5个测试)**:
```java
// 端到端系统测试
CaplLinterSystemTest            (5个测试)

// 测试场景:
- 完整CAPL程序的端到端处理
- 复杂语法结构的正确解析
- 语义错误的准确检测
- 语法错误的优雅处理
- 空文件等边界情况
```

### 10.3 测试策略特点

**🎯 重点突出**:
- 优先测试核心功能和错误处理
- 重点验证Helper类的健壮性
- 全面覆盖组件间的数据流

**🔧 实用导向**:
- 简化ANTLR Mock策略，重点测试业务逻辑
- 为所有Helper类添加null检查
- 使用真实CAPL代码样本进行测试

**📈 可扩展**:
- 模块化的测试结构，易于添加新测试
- 分层清晰，便于定位问题
- 支持回归测试和性能测试扩展

### 10.4 质量保证措施

**代码覆盖率**:
- 单元测试: 80%+ 代码覆盖率目标
- 集成测试: 覆盖主要组件交互
- 系统测试: 覆盖端到端场景

**错误处理验证**:
- 6层错误检测的完整验证
- 异常情况的优雅处理
- 错误消息的准确性和有用性

**性能考虑**:
- 大文件处理能力验证
- 内存使用效率监控
- 复杂AST构建的性能测试

---

## 11. 扩展开发指南

### 11.1 添加新的 AST 节点

**完整开发流程**:

1. **定义节点类型**:
```java
// 在 AstNodeType.java 中添加
public enum NodeType {
    // ... 现有节点类型
    NEW_CUSTOM_NODE,  // 新增节点类型
}
```

2. **创建节点类**:
```java
public class NewCustomNode extends AstNode {
    private final String customProperty;

    public NewCustomNode(SourceLocation location, String customProperty) {
        super(NodeType.NEW_CUSTOM_NODE, location);
        this.customProperty = customProperty;
    }

    public String getCustomProperty() {
        return customProperty;
    }

    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitNewCustomNode(this, context);
    }
}
```

3. **更新访问者接口**:
```java
// 在 AstVisitor.java 中添加
public interface AstVisitor<R, C> {
    // ... 现有方法
    R visitNewCustomNode(NewCustomNode node, C context);
}
```

4. **更新基础访问者**:
```java
// 在 RuleBaseAstVisitor.java 中添加默认实现
@Override
public R visitNewCustomNode(NewCustomNode node, C context) {
    return visitChildren(node, context);
}
```

5. **添加 Helper 方法**:
```java
// 在相应的 Helper 类中添加构建方法
public class CustomHelper {
    public static NewCustomNode buildNewCustomNode(
        CAPLParser.NewCustomContext ctx) {
        SourceLocation location = SourceLocationHelper.getLocation(ctx);
        String property = ctx.IDENTIFIER().getText();
        return new NewCustomNode(location, property);
    }
}
```

### 11.2 添加新的语义检查规则

**示例 - 循环复杂度检查规则**:

```java
@RuleIdentifier("CAPL-COMPLEXITY-001")
public class CyclomaticComplexityRule extends RuleBaseAstVisitor<Void, AnalysisContext>
                                      implements Rule {
    private static final int DEFAULT_MAX_COMPLEXITY = 10;
    private List<IssueObject> issues;
    private int maxComplexity;

    @Override
    public String getRuleId() { return "CAPL-COMPLEXITY-001"; }

    @Override
    public String getName() { return "Cyclomatic Complexity Check"; }

    @Override
    public SeverityLevel getDefaultSeverity() { return SeverityLevel.WARNING; }

    @Override
    public List<IssueObject> check(AstNode node, AnalysisContext context) {
        this.issues = new ArrayList<>();
        this.maxComplexity = getMaxComplexityFromConfig(context);
        visit(node, context);
        return issues;
    }

    @Override
    public Void visitFunctionDefinition(FunctionDefinitionNode node, AnalysisContext context) {
        int complexity = calculateComplexity(node);
        if (complexity > maxComplexity) {
            issues.add(new IssueObject(
                context.getSourceFile().getAbsolutePath().toString(),
                node.getSourceLocation().getStartLine(),
                node.getSourceLocation().getStartColumn(),
                SeverityLevel.WARNING,
                getRuleId(),
                String.format("Function '%s' has complexity %d, exceeds limit %d",
                             node.getFunctionName(), complexity, maxComplexity)
            ));
        }
        return super.visitFunctionDefinition(node, context);
    }

    private int calculateComplexity(FunctionDefinitionNode function) {
        ComplexityCalculator calculator = new ComplexityCalculator();
        return calculator.visit(function, null);
    }

    private static class ComplexityCalculator extends RuleBaseAstVisitor<Integer, Void> {
        @Override
        public Integer visitIfStatement(IfStatementNode node, Void context) {
            return 1 + super.visitIfStatement(node, context);
        }

        @Override
        public Integer visitWhileStatement(WhileStatementNode node, Void context) {
            return 1 + super.visitWhileStatement(node, context);
        }

        @Override
        public Integer visitForStatement(ForStatementNode node, Void context) {
            return 1 + super.visitForStatement(node, context);
        }

        @Override
        public Integer visitSwitchStatement(SwitchStatementNode node, Void context) {
            return node.getCaseClauses().size() + super.visitSwitchStatement(node, context);
        }
    }
}
```

### 11.3 性能优化最佳实践

**1. AST 遍历优化**:
```java
// 使用缓存避免重复计算
public class OptimizedSemanticAnalyzer {
    private final Map<AstNode, TypeInfo> typeCache = new ConcurrentHashMap<>();
    private final Map<String, SymbolInfo> symbolCache = new ConcurrentHashMap<>();

    public TypeInfo getNodeType(AstNode node) {
        return typeCache.computeIfAbsent(node, this::computeTypeInfo);
    }

    // 批量处理减少遍历次数
    public void analyzeInBatches(List<AstNode> nodes) {
        nodes.parallelStream()
             .collect(Collectors.groupingBy(AstNode::getNodeType))
             .forEach(this::processSameTypeNodes);
    }
}
```

**2. 内存优化**:
```java
// 使用弱引用避免内存泄漏
public class MemoryEfficientSymbolTable {
    private final Map<String, WeakReference<SymbolInfo>> symbols =
        new ConcurrentHashMap<>();

    public void addSymbol(String name, SymbolInfo symbol) {
        symbols.put(name, new WeakReference<>(symbol));
    }

    public Optional<SymbolInfo> getSymbol(String name) {
        WeakReference<SymbolInfo> ref = symbols.get(name);
        if (ref != null) {
            SymbolInfo symbol = ref.get();
            if (symbol != null) {
                return Optional.of(symbol);
            } else {
                symbols.remove(name);  // 清理失效引用
            }
        }
        return Optional.empty();
    }
}
```

**3. 并发处理优化**:
```java
// 并行处理多个文件
public class ParallelLinterEngine {
    private final ForkJoinPool customThreadPool =
        new ForkJoinPool(Runtime.getRuntime().availableProcessors());

    public List<IssueObject> analyzeFilesInParallel(List<SourceFile> files) {
        return files.parallelStream()
                   .map(this::analyzeSingleFile)
                   .flatMap(List::stream)
                   .collect(Collectors.toList());
    }
}
```

---

## 总结与展望

### 当前成就 ✅

**🏗️ 完整的架构体系**:
- 6层渐进式错误检测架构
- 模块化的组件设计
- 170+ AST 节点类型支持
- 完整的 CAPL 语法覆盖

**🔧 强大的分析能力**:
- 严格的宏验证和处理
- 精确的语义分析和类型检查
- 可配置的代码质量规则
- 多格式的错误报告

**📊 质量保证体系**:
- 70个测试用例 (单元+集成+系统)
- 分层测试策略
- 全面的错误处理验证
- 性能优化考虑

**🎯 开发友好特性**:
- 清晰的扩展指南
- 丰富的设计模式应用
- 详细的错误信息和位置
- 灵活的配置管理

### 技术亮点 🌟

**创新的设计**:
- Helper 模式简化 AST 构建复杂度
- 访问者模式实现灵活的规则扩展
- 上下文感知的宏处理机制
- 分层配置优先级系统

**工程实践**:
- ANTLR4 与自定义 AST 的完美结合
- 错误恢复和优雅降级机制
- 内存优化和并发处理支持
- 国际化和日志管理

### 未来发展方向 🚀

**短期目标 (3-6个月)**:
- � **LSP 服务器支持** - 实现 IDE 集成
- � **增强报告功能** - HTML 报告和代码高亮
- 🎯 **规则库扩展** - 更多 CAPL 最佳实践规则
- ⚡ **性能优化** - 大文件处理能力提升

**中期目标 (6-12个月)**:
- 🤖 **自动修复建议** - 基于规则的代码修复
- � **代码度量分析** - 复杂度、耦合度等质量指标
- 🔄 **增量分析** - 只分析变更的代码部分
- 🌐 **插件生态** - 支持第三方规则插件

**长期愿景 (1-2年)**:
- ☁️ **云端分析服务** - 大规模代码分析平台
- 🧠 **AI 辅助分析** - 机器学习驱动的代码质量检测
- 🌍 **多语言支持** - 扩展到其他汽车领域 DSL
- 📱 **移动端支持** - 轻量级的代码检查工具

### 贡献指南 🤝

**如何参与开发**:
1. **阅读设计文档** - 理解架构和设计原则
2. **运行测试套件** - 确保环境配置正确
3. **选择合适任务** - 从简单的规则扩展开始
4. **遵循编码规范** - 保持代码质量和一致性
5. **编写测试用例** - 确保新功能的质量

**开发资源**:
- 📚 **设计文档**: `document/design/` 目录
- 🧪 **测试样例**: `src/test/resources/test_samples/`
- 🔧 **配置示例**: `calint.json`, `rules-config.yaml`
- 📖 **API 文档**: JavaDoc 注释

---

## Q&A

**感谢聆听！**

欢迎讨论以下话题：
- 🏗️ **架构设计问题** - 组件职责、接口设计
- 🔧 **实现细节疑问** - 具体算法、数据结构
- 📈 **扩展开发建议** - 新功能、新规则
- 🐛 **问题排查方法** - 调试技巧、性能优化
- 🎯 **最佳实践分享** - 代码质量、测试策略

**联系方式**:
- 📧 **项目仓库**: GitHub Issues
- 💬 **技术讨论**: 开发团队内部群组
- 📝 **文档贡献**: Pull Request 欢迎

---

*本文档基于 calint v0.1.0-SNAPSHOT*
*最后更新: 2024年12月*
*文档版本: 2.0*
