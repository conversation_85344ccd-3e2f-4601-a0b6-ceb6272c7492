# CAPL Linter (calint) - 项目总览

**版本**: v0.1.0-SNAPSHOT  
**最后更新**: 2024年12月  
**文档版本**: 3.0  

## 项目简介

**CAPL Linter (calint)** 是一个专为 CAPL (Communication Access Programming Language) 设计的静态代码分析工具。CAPL 是 Vector 公司开发的专用脚本语言，广泛应用于汽车电子 ECU 测试和仿真领域。

### 核心价值

- 🎯 **准确性优先**: 保证分析结果的准确性，避免误报
- ⚡ **高效性**: 快速处理大规模 CAPL 代码文件
- 🔧 **可配置性**: 允许用户根据项目需求定制检查规则
- 📈 **模块化**: 各功能模块低耦合，易于维护和扩展
- 🛠️ **易用性**: 提供简洁明了的命令行接口和 VS Code 集成

### 支持的功能

✅ **词法/语法分析**: 基于 ANTLR4 的完整 CAPL 语法支持  
✅ **语义分析**: 符号表构建、类型检查、作用域分析  
✅ **代码质量检查**: 170+ 种代码质量和风格规则  
✅ **多格式报告**: Console、JSON、HTML 格式输出  
✅ **VS Code 集成**: 通过 LSP 协议提供实时代码检查  
✅ **预处理支持**: 宏展开、#include 文件合并、智能依赖解析  

### 支持的 CAPL 特性

- **文件类型**: `.capl`, `.can`, `.cin` 三种扩展名
- **事件处理器**: `on timer`, `on message`, `on key`, `on sysvar` 等
- **网络协议**: CAN、FlexRay、MOST、ARINC 429/664
- **预定义宏**: `%FILE_NAME%`, `%NODE_NAME%`, `%LINE_NUMBER%` 等
- **数据类型**: 基础类型、消息类型、定时器类型、信号类型

## 快速开始

### 安装

```bash
# 从源码构建
git clone <repository-url>
cd calint
mvn clean package

# 或下载预编译版本
# [下载链接]
```

### 基本使用

```bash
# 分析单个文件
java -jar calint.jar analyze myfile.can

# 分析整个目录
java -jar calint.jar analyze src/capl/

# 指定配置文件
java -jar calint.jar analyze --config calint.json src/

# 生成 JSON 报告
java -jar calint.jar analyze --format json --output report.json src/
```

### VS Code 集成

1. 安装 CAPL Linter 扩展
2. 配置 LSP 服务器连接
3. 在 VS Code 中打开 CAPL 文件即可看到实时检查结果

## 架构概览

calint 采用 **6层渐进式错误检测架构**：

```
┌─────────────────────────────────────────────────────────┐
│                    6. 规则检查阶段                        │
│              代码质量和风格问题 (CALINT-*)               │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    5. 语义分析阶段                        │
│            符号表、类型检查 (SEMANTIC-*)                │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    4. AST构建阶段                        │
│              自定义AST构建 (AST-*)                      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    3. 语法分析阶段                        │
│              语法规则匹配 (PARSER-*)                    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    2. 词法分析阶段                        │
│              Token识别 (LEXER-*)                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    1. 预处理阶段                         │
│          宏展开、文件包含 (CAPL-MACRO-*, CAPL-INCLUDE-*) │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

- **预处理器**: 宏展开、#include 解析、智能依赖处理
- **词法/语法分析器**: 基于 ANTLR4 的 CAPL 解析器
- **AST 构建器**: 170+ 节点类型的自定义抽象语法树
- **语义分析器**: 符号表、类型检查、作用域分析
- **规则引擎**: 可配置的代码质量检查规则
- **报告生成器**: 多格式输出支持

## 项目状态

### 当前成就 ✅

- **完整的架构体系**: 6层渐进式错误检测架构
- **强大的分析能力**: 170+ AST 节点类型支持
- **质量保证体系**: 70个测试用例覆盖
- **开发友好特性**: 清晰的扩展指南和设计模式

### 开发中功能 🚧

- **LSP 服务器**: VS Code 实时集成 (90% 完成)
- **增强报告**: HTML 报告和代码高亮
- **性能优化**: 大文件处理能力提升

### 计划功能 📋

- **自动修复建议**: 基于规则的代码修复
- **代码度量分析**: 复杂度、耦合度等质量指标
- **增量分析**: 只分析变更的代码部分

## 文档导航

### 🏗️ 设计文档
- [系统架构设计](../02-design/system-architecture.md) - 完整的系统架构和组件设计
- [预处理机制设计](../02-design/preprocessing-design.md) - 宏处理和文件合并机制
- [AST 设计](../02-design/ast-design.md) - 抽象语法树设计和节点类型
- [语义分析设计](../02-design/semantic-analysis-design.md) - 符号表和类型检查
- [规则引擎设计](../02-design/rule-engine-design.md) - 代码质量检查规则
- [LSP 集成设计](../02-design/lsp-integration-design.md) - VS Code 集成方案

### 🔧 开发文档
- [开发指南](../04-development/development-guide.md) - 开发环境搭建和贡献指南
- [测试策略](../04-development/testing-strategy.md) - 测试框架和策略
- [构建部署](../04-development/build-deployment.md) - 构建和部署流程

### 📚 参考文档
- [CAPL 语言参考](../06-reference/capl-language-ref/) - CAPL 语法和特性参考
- [API 参考](../06-reference/api-reference.md) - 核心 API 文档
- [配置参考](../06-reference/configuration-ref.md) - 配置选项详解

### 👥 用户指南
- [安装指南](../07-user-guide/installation.md) - 详细安装步骤
- [使用示例](../07-user-guide/usage-examples.md) - 实际使用案例
- [VS Code 扩展](../07-user-guide/vscode-extension.md) - VS Code 集成使用
- [命令行参考](../07-user-guide/cli-reference.md) - 命令行选项详解

## 技术栈

- **编程语言**: Java 11+
- **解析器**: ANTLR v4
- **构建工具**: Maven
- **测试框架**: JUnit 5
- **LSP 实现**: Eclipse LSP4J
- **VS Code 扩展**: TypeScript + Node.js

## 贡献指南

我们欢迎社区贡献！请参阅：

- [贡献指南](../04-development/contribution-guide.md) - 如何参与项目开发
- [开发指南](../04-development/development-guide.md) - 开发环境和编码规范
- [重构路线图](../05-refactoring/refactoring-roadmap.md) - 当前重构计划

## 许可证

[许可证信息]

## 联系方式

- **项目仓库**: [GitHub/SVN 链接]
- **问题反馈**: [Issue 链接]
- **技术讨论**: [讨论区链接]

---

*本文档是 CAPL Linter 项目的入口文档，提供项目的整体概览。详细的技术信息请参阅相应的专门文档。*
