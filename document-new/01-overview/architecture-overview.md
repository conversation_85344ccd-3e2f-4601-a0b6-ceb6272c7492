# CAPL Linter 架构概览

**版本**: v0.1.0-SNAPSHOT  
**最后更新**: 2024年12月  
**文档版本**: 3.0  

## 架构设计原则

### 核心设计理念

CAPL Linter 采用**分层架构**和**管道式处理**的设计理念，确保代码分析的准确性、高效性和可扩展性。

#### 设计原则

- 🎯 **准确性优先**: 保证分析结果的准确性，避免误报
- ⚡ **高效性**: 快速处理大规模 CAPL 代码文件  
- 🔧 **可配置性**: 允许用户根据项目需求定制检查规则
- 📈 **模块化**: 各功能模块低耦合，易于维护和扩展
- 🛠️ **易用性**: 提供简洁明了的命令行接口和报告

## 6层错误检测架构

calint 采用**6层渐进式错误检测架构**，从基础语法到高级代码质量逐层检测：

```mermaid
graph TD
    subgraph "6层错误检测架构"
        L6[6. 规则检查阶段<br/>代码质量和风格问题<br/>CALINT-*]
        L5[5. 语义分析阶段<br/>符号表、类型检查<br/>SEMANTIC-*]
        L4[4. AST构建阶段<br/>自定义AST构建<br/>AST-*]
        L3[3. 语法分析阶段<br/>语法规则匹配<br/>PARSER-*]
        L2[2. 词法分析阶段<br/>Token识别<br/>LEXER-*]
        L1[1. 预处理阶段<br/>宏展开、文件包含<br/>CAPL-MACRO-*, CAPL-INCLUDE-*]
        
        L1 --> L2 --> L3 --> L4 --> L5 --> L6
    end
    
    style L1 fill:#e1f5fe
    style L2 fill:#e8f5e8
    style L3 fill:#fff3e0
    style L4 fill:#f3e5f5
    style L5 fill:#fce4ec
    style L6 fill:#e0f2f1
```

### 各层职责说明

| 层级 | 名称 | 主要职责 | 错误类型 | 示例 |
|------|------|----------|----------|------|
| **1** | 预处理阶段 | 宏展开、#include解析 | CAPL-MACRO-*, CAPL-INCLUDE-* | 未知宏、文件未找到 |
| **2** | 词法分析阶段 | Token识别和分类 | LEXER-* | 非法字符、未闭合字符串 |
| **3** | 语法分析阶段 | 语法规则匹配 | PARSER-* | 缺少分号、括号不匹配 |
| **4** | AST构建阶段 | 构建自定义AST | AST-* | 节点构建失败 |
| **5** | 语义分析阶段 | 符号表、类型检查 | SEMANTIC-* | 变量未声明、类型不匹配 |
| **6** | 规则检查阶段 | 代码质量和风格 | CALINT-* | 命名规范、复杂度过高 |

## 系统整体架构

```mermaid
graph TB
    subgraph "输入层"
        CLI[CLI 命令行]
        LSP[LSP 服务器]
        TEST[测试输入]
    end
    
    subgraph "配置层"
        CM[配置管理器<br/>ConfigManager]
        RC[规则配置<br/>RulesConfig]
    end
    
    subgraph "预处理层"
        VFS[虚拟文件系统<br/>VirtualFileSystem]
        PP[预处理管道<br/>PreprocessorPipeline]
        ME[宏展开<br/>MacroExpansion]
        IR[Include解析<br/>IncludeResolution]
    end
    
    subgraph "分析层"
        LP[词法语法分析<br/>LexerParser]
        AB[AST构建<br/>ASTBuilder]
        SA[语义分析<br/>SemanticAnalyzer]
        RE[规则引擎<br/>RuleEngine]
    end
    
    subgraph "输出层"
        RG[报告生成<br/>ReportGenerator]
        CONSOLE[控制台输出]
        JSON[JSON报告]
        HTML[HTML报告]
        LSPOUT[LSP诊断]
    end
    
    CLI --> CM
    LSP --> CM
    TEST --> CM
    
    CM --> VFS
    CM --> PP
    
    VFS --> PP
    PP --> ME --> IR
    
    IR --> LP
    LP --> AB
    AB --> SA
    SA --> RE
    
    RE --> RG
    RG --> CONSOLE
    RG --> JSON
    RG --> HTML
    RG --> LSPOUT
    
    RC --> RE
    
    style CLI fill:#e1f5fe
    style LSP fill:#e8f5e8
    style PP fill:#fff3e0
    style SA fill:#f3e5f5
    style RE fill:#fce4ec
    style RG fill:#e0f2f1
```

## 核心组件详解

### 1. 预处理系统

**设计特点**: 专为静态分析优化，采用管道式架构

```mermaid
flowchart LR
    INPUT[输入文档] --> MACRO[宏展开阶段]
    MACRO --> INCLUDE[Include解析阶段]
    INCLUDE --> OUTPUT[预处理结果]
    
    MACRO -.-> WARNINGS[警告处理]
    INCLUDE -.-> WARNINGS
    WARNINGS -.-> OUTPUT
```

**核心功能**:
- ✅ **宏验证和展开**: 严格验证 CAPL 预定义宏
- ✅ **智能依赖解析**: 自动处理循环依赖和重复引用
- ✅ **精确源映射**: 支持多层 include 嵌套的位置追踪

### 2. 词法语法分析

**技术栈**: 基于 ANTLR4 的自动生成解析器

```mermaid
graph LR
    CAPL_G4[CAPL.g4<br/>语法定义] --> ANTLR[ANTLR4<br/>生成器]
    ANTLR --> LEXER[CAPLLexer<br/>词法分析器]
    ANTLR --> PARSER[CAPLParser<br/>语法分析器]
    
    SOURCE[源代码] --> LEXER
    LEXER --> TOKENS[Token流]
    TOKENS --> PARSER
    PARSER --> PARSE_TREE[解析树]
```

**支持特性**:
- 完整的 CAPL 语法支持 (.capl, .can, .cin)
- 增强的错误恢复机制
- 精确的位置信息追踪

### 3. AST 构建系统

**设计理念**: 自定义 AST 节点，语义丰富，适合分析

```mermaid
classDiagram
    class AstNode {
        <<abstract>>
        +NodeType nodeType
        +SourceLocation location
        +List~AstNode~ children
        +accept(visitor)
    }
    
    class ProgramUnitNode {
        +IncludesSection includes
        +VariablesSection variables
        +List~FunctionDefinition~ functions
    }
    
    class VariableDeclarationNode {
        +String variableName
        +TypeInfo variableType
        +ExpressionNode initialValue
    }
    
    class EventHandlerNode {
        +EventType eventType
        +String eventTarget
        +CompoundStatementNode body
    }
    
    AstNode <|-- ProgramUnitNode
    AstNode <|-- VariableDeclarationNode
    AstNode <|-- EventHandlerNode
```

**核心特性**:
- **170+ 节点类型**: 覆盖完整的 CAPL 语法结构
- **访问者模式**: 支持灵活的遍历和分析
- **Helper 模式**: 模块化的节点构建逻辑

### 4. 语义分析引擎

**核心功能**: 符号表管理、类型检查、作用域分析

```mermaid
graph TB
    subgraph "语义分析组件"
        ST[符号表<br/>SymbolTable]
        TC[类型检查器<br/>TypeChecker]
        SA[作用域分析器<br/>ScopeAnalyzer]
        RR[引用解析器<br/>ReferenceResolver]
    end
    
    AST[AST] --> ST
    ST --> TC
    TC --> SA
    SA --> RR
    RR --> SEMANTIC_INFO[语义信息]
```

**分析能力**:
- **符号表构建**: 分层作用域的符号管理
- **类型检查**: CAPL 类型系统验证
- **引用解析**: 符号引用链接和验证

### 5. 规则引擎

**架构特点**: 可配置、可扩展的规则执行框架

```mermaid
graph LR
    CONFIG[规则配置] --> REGISTRY[规则注册表]
    REGISTRY --> EXECUTOR[规则执行器]
    
    AST[AST] --> EXECUTOR
    SEMANTIC[语义信息] --> EXECUTOR
    
    EXECUTOR --> ISSUES[问题列表]
```

**规则分类**:
- **语义错误**: 变量未声明、类型不匹配等
- **代码质量**: 命名规范、复杂度检查等
- **CAPL 最佳实践**: 事件处理优化建议等

## 技术亮点

### 1. 智能预处理机制

**创新点**: 循环依赖智能打断，而非简单报错

```mermaid
flowchart TD
    A[检测循环依赖] --> B[分析循环结构]
    B --> C[选择最佳打断点]
    C --> D[移除循环边]
    D --> E[生成拓扑排序]
    E --> F[继续处理 + 警告]
    
    style A fill:#fff3e0
    style F fill:#e8f5e8
```

### 2. 精确源映射系统

**特点**: 支持多层 include 嵌套的精确错误定位

```mermaid
sequenceDiagram
    participant Original as 原始文件
    participant Preprocessed as 预处理后
    participant Analysis as 分析引擎
    participant Mapped as 映射后错误
    
    Original->>Preprocessed: 宏展开 + Include合并
    Preprocessed->>Analysis: 语法语义分析
    Analysis->>Mapped: 源映射回原始位置
    
    Note over Mapped: 错误精确定位到<br/>用户编辑的原始文件
```

### 3. 模块化设计模式

**设计模式应用**:
- **访问者模式**: AST 遍历和分析
- **策略模式**: 可配置的规则检查
- **Helper 模式**: 模块化的组件构建
- **管道模式**: 预处理阶段的流式处理

## 性能特性

### 并发处理能力

- **文件级并行**: 使用 ForkJoinPool 按文件划分任务
- **内存优化**: 弱引用和缓存机制
- **增量分析**: 支持只分析变更文件（计划中）

### 扩展性设计

- **规则插件化**: 支持动态加载自定义规则
- **多格式输出**: Console、JSON、HTML 等格式
- **LSP 集成**: 实时 IDE 集成支持

## 质量保证

### 测试体系

```
测试金字塔:
┌─────────────────────────────────────┐
│        系统测试 (5个测试)             │  ← 端到端功能验证
├─────────────────────────────────────┤
│       集成测试 (12个测试)             │  ← 组件间交互验证  
├─────────────────────────────────────┤
│       单元测试 (53个测试)             │  ← Helper类和核心组件
└─────────────────────────────────────┘
```

### 代码质量

- **测试覆盖率**: 80%+ 代码覆盖率目标
- **错误处理**: 6层错误检测的完整验证
- **性能监控**: 大文件处理能力验证

## 下一步发展

### 短期目标 (3-6个月)
- 🚧 **LSP 服务器完善**: 实现完整的 IDE 集成
- 📊 **增强报告功能**: HTML 报告和代码高亮
- ⚡ **性能优化**: 大文件处理能力提升

### 中期目标 (6-12个月)
- 🤖 **自动修复建议**: 基于规则的代码修复
- 📈 **代码度量分析**: 复杂度、耦合度等质量指标
- 🔄 **增量分析**: 只分析变更的代码部分

### 长期愿景 (1-2年)
- ☁️ **云端分析服务**: 大规模代码分析平台
- 🧠 **AI 辅助分析**: 机器学习驱动的代码质量检测
- 🌍 **多语言支持**: 扩展到其他汽车领域 DSL

---

*本文档提供 CAPL Linter 的架构概览。详细的技术设计请参阅 [02-design](../02-design/) 目录下的专门文档。*
