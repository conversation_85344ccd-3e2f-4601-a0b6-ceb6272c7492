<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MacroExpander.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_source">MacroExpander.java</span></div><h1>MacroExpander.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.util.SourceLocation;
import com.polelink.calint.issue.IssueObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Handles expansion of CAPL macros in source code.
 * 
 * This class is responsible for finding and replacing CAPL macros (e.g., %NODE_NAME%)
 * with their corresponding values based on the provided MacroContext.
 * 
 * Supported CAPL macros:
 * - %BASE_FILE_NAME% - Name of the compiled file
 * - %BASE_FILE_NAME_NO_EXT% - Name without extension
 * - %BUS_TYPE% - Bus system of the channel
 * - %CHANNEL% - Channel number
 * - %FILE_NAME% - Source code file name
 * - %FILE_NAME_NO_EXT% - Source file name without extension
 * - %LINE_NUMBER% - Line number containing the macro
 * - %NETWORK_NAME% - Network name
 * - %NODE_NAME% - Node name
 */
public class MacroExpander {

<span class="nc" id="L37">    private static final Logger LOGGER = LoggerFactory.getLogger(MacroExpander.class);</span>

    // Pattern to match CAPL macros: %MACRO_NAME%
    // Matches: '%' [A-Za-z_][A-Za-z0-9_]* '%'
<span class="nc" id="L41">    private static final Pattern MACRO_PATTERN = Pattern.compile(&quot;%([A-Za-z_][A-Za-z0-9_]*)%&quot;);</span>

    // Built-in macro resolvers
    private final Map&lt;String, Function&lt;MacroContext, String&gt;&gt; builtInMacros;

    /**
     * Creates a new MacroExpander with built-in CAPL macros.
     */
<span class="nc" id="L49">    public MacroExpander() {</span>
<span class="nc" id="L50">        this.builtInMacros = createBuiltInMacros();</span>
<span class="nc" id="L51">    }</span>

    /**
     * Expands all macros in the given source code using the provided context.
     * 
     * @param sourceCode The source code containing macros to expand
     * @param context The macro context providing values for expansion
     * @return The source code with all macros expanded
     * @throws MacroExpansionException if macro expansion fails
     */
    public String expandMacros(String sourceCode, MacroContext context) throws MacroExpansionException {
<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (sourceCode == null) {</span>
<span class="nc" id="L63">            return null;</span>
        }
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (context == null) {</span>
<span class="nc" id="L66">            throw new MacroExpansionException(&quot;Macro context cannot be null&quot;);</span>
        }

<span class="nc" id="L69">        StringBuilder result = new StringBuilder();</span>
<span class="nc" id="L70">        String[] lines = sourceCode.split(&quot;\n&quot;, -1); // Keep empty lines</span>
        
<span class="nc bnc" id="L72" title="All 2 branches missed.">        for (int lineNumber = 0; lineNumber &lt; lines.length; lineNumber++) {</span>
<span class="nc" id="L73">            String expandedLine = expandMacrosInLine(lines[lineNumber], context, lineNumber + 1);</span>
<span class="nc" id="L74">            result.append(expandedLine);</span>
            
            // Add newline except for the last line (to preserve original line endings)
<span class="nc bnc" id="L77" title="All 2 branches missed.">            if (lineNumber &lt; lines.length - 1) {</span>
<span class="nc" id="L78">                result.append(&quot;\n&quot;);</span>
            }
        }

<span class="nc" id="L82">        return result.toString();</span>
    }

    /**
     * Expands macros in a single line of code.
     * 
     * @param line The line of code to process
     * @param context The macro context
     * @param lineNumber The current line number (1-based)
     * @return The line with macros expanded
     * @throws MacroExpansionException if macro expansion fails
     */
    private String expandMacrosInLine(String line, MacroContext context, int lineNumber) throws MacroExpansionException {
<span class="nc" id="L95">        Matcher matcher = MACRO_PATTERN.matcher(line);</span>
<span class="nc" id="L96">        StringBuffer result = new StringBuffer();</span>

<span class="nc bnc" id="L98" title="All 2 branches missed.">        while (matcher.find()) {</span>
<span class="nc" id="L99">            String macroName = matcher.group(1);</span>
            // resolveMacro now throws exception for unknown macros
<span class="nc" id="L101">            String macroValue = resolveMacro(macroName, context, lineNumber);</span>

            // Escape special regex characters in the replacement
<span class="nc" id="L104">            String escapedValue = Matcher.quoteReplacement(macroValue);</span>
<span class="nc" id="L105">            matcher.appendReplacement(result, escapedValue);</span>
<span class="nc" id="L106">            LOGGER.debug(&quot;Expanded macro %{}% to '{}' at line {}&quot;, macroName, macroValue, lineNumber);</span>
<span class="nc" id="L107">        }</span>

<span class="nc" id="L109">        matcher.appendTail(result);</span>
<span class="nc" id="L110">        return result.toString();</span>
    }

    /**
     * Resolves a macro name to its value.
     *
     * @param macroName The name of the macro (without % symbols)
     * @param context The macro context
     * @param lineNumber The current line number
     * @return The macro value
     * @throws MacroExpansionException if the macro is unknown or cannot be resolved
     */
    private String resolveMacro(String macroName, MacroContext context, int lineNumber) throws MacroExpansionException {
        // Handle LINE_NUMBER specially since it's context-dependent
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (&quot;LINE_NUMBER&quot;.equals(macroName)) {</span>
<span class="nc" id="L125">            return String.valueOf(lineNumber);</span>
        }

        // Try built-in macros first
<span class="nc" id="L129">        Function&lt;MacroContext, String&gt; resolver = builtInMacros.get(macroName);</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">        if (resolver != null) {</span>
<span class="nc" id="L131">            String value = resolver.apply(context);</span>
            // For static analysis, we must provide a value even if context doesn't have it
<span class="nc bnc" id="L133" title="All 2 branches missed.">            if (value == null) {</span>
<span class="nc" id="L134">                value = getDefaultValueForMacro(macroName);</span>
            }
<span class="nc" id="L136">            return value;</span>
        }

        // Try user-defined macros
<span class="nc" id="L140">        String userValue = context.getUserDefinedMacro(macroName);</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">        if (userValue != null) {</span>
<span class="nc" id="L142">            return userValue;</span>
        }

        // Unknown macro - this is an error for CAPL static analysis
<span class="nc" id="L146">        throw new MacroExpansionException(String.format(&quot;Unknown CAPL macro '%%%s%%' at line %d. Only predefined CAPL macros are allowed.&quot;, macroName, lineNumber));</span>
    }

    /**
     * Creates the map of built-in CAPL macros.
     */
    private Map&lt;String, Function&lt;MacroContext, String&gt;&gt; createBuiltInMacros() {
<span class="nc" id="L153">        Map&lt;String, Function&lt;MacroContext, String&gt;&gt; macros = new HashMap&lt;&gt;();</span>

        // File-related macros
<span class="nc" id="L156">        macros.put(&quot;BASE_FILE_NAME&quot;, MacroContext::getBaseFileName);</span>
<span class="nc" id="L157">        macros.put(&quot;BASE_FILE_NAME_NO_EXT&quot;, MacroContext::getBaseFileNameNoExt);</span>
<span class="nc" id="L158">        macros.put(&quot;FILE_NAME&quot;, MacroContext::getSourceFileName);</span>
<span class="nc" id="L159">        macros.put(&quot;FILE_NAME_NO_EXT&quot;, MacroContext::getSourceFileNameNoExt);</span>

        // Node and network macros
<span class="nc" id="L162">        macros.put(&quot;NODE_NAME&quot;, MacroContext::getNodeName);</span>
<span class="nc" id="L163">        macros.put(&quot;NETWORK_NAME&quot;, MacroContext::getNetworkName);</span>
<span class="nc" id="L164">        macros.put(&quot;BUS_TYPE&quot;, MacroContext::getBusType);</span>
<span class="nc" id="L165">        macros.put(&quot;CHANNEL&quot;, context -&gt; {</span>
<span class="nc" id="L166">            int channel = context.getChannel();</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">            return channel &gt;= 0 ? String.valueOf(channel) : null;</span>
        });

<span class="nc" id="L170">        return macros;</span>
    }

    /**
     * Gets default value for a macro when context doesn't provide one.
     * This is used for static analysis to ensure all macros are replaced.
     *
     * @param macroName The macro name
     * @return Default value for the macro
     */
    private String getDefaultValueForMacro(String macroName) {
<span class="nc bnc" id="L181" title="All 9 branches missed.">        switch (macroName) {</span>
            case &quot;BASE_FILE_NAME&quot;:
<span class="nc" id="L183">                return &quot;unknown.can&quot;;</span>
            case &quot;BASE_FILE_NAME_NO_EXT&quot;:
<span class="nc" id="L185">                return &quot;unknown&quot;;</span>
            case &quot;FILE_NAME&quot;:
<span class="nc" id="L187">                return &quot;unknown.cin&quot;;</span>
            case &quot;FILE_NAME_NO_EXT&quot;:
<span class="nc" id="L189">                return &quot;unknown&quot;;</span>
            case &quot;NODE_NAME&quot;:
<span class="nc" id="L191">                return &quot;UnknownNode&quot;;</span>
            case &quot;NETWORK_NAME&quot;:
<span class="nc" id="L193">                return &quot;UnknownNetwork&quot;;</span>
            case &quot;BUS_TYPE&quot;:
<span class="nc" id="L195">                return &quot;CAN&quot;;</span>
            case &quot;CHANNEL&quot;:
<span class="nc" id="L197">                return &quot;1&quot;;</span>
            default:
<span class="nc" id="L199">                return &quot;UNKNOWN_VALUE&quot;;</span>
        }
    }

    /**
     * Validates all macros in the source code and collects any errors.
     * This method should be called before expandMacros to check for unknown macros.
     *
     * @param sourceCode The source code to validate
     * @param filePath The file path for error reporting
     * @return List of IssueObject for any unknown macros found
     */
    public List&lt;IssueObject&gt; validateMacros(String sourceCode, String filePath) {
<span class="nc" id="L212">        List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L214" title="All 2 branches missed.">        if (sourceCode == null) {</span>
<span class="nc" id="L215">            return issues;</span>
        }

<span class="nc" id="L218">        String[] lines = sourceCode.split(&quot;\n&quot;, -1);</span>

<span class="nc bnc" id="L220" title="All 2 branches missed.">        for (int lineNumber = 0; lineNumber &lt; lines.length; lineNumber++) {</span>
<span class="nc" id="L221">            String line = lines[lineNumber];</span>
<span class="nc" id="L222">            Matcher matcher = MACRO_PATTERN.matcher(line);</span>

<span class="nc bnc" id="L224" title="All 2 branches missed.">            while (matcher.find()) {</span>
<span class="nc" id="L225">                String macroName = matcher.group(1);</span>

                // Check if this is a known macro
<span class="nc bnc" id="L228" title="All 2 branches missed.">                if (!isKnownMacro(macroName)) {</span>
                    // Calculate column position
<span class="nc" id="L230">                    int startColumn = matcher.start() + 1; // 1-based</span>
<span class="nc" id="L231">                    int endColumn = matcher.end(); // 1-based</span>

<span class="nc" id="L233">                    SourceLocation location = new SourceLocation(</span>
                        filePath,
                        lineNumber + 1, // 1-based line number
                        startColumn,
                        lineNumber + 1,
                        endColumn
                    );

<span class="nc" id="L241">                    String suggestion = &quot;Remove the unknown macro or check if it's a typo. &quot; +</span>
<span class="nc" id="L242">                                      &quot;Valid CAPL macros are: &quot; + String.join(&quot;, &quot;, getBuiltInMacroNames());</span>

<span class="nc" id="L244">                    IssueObject issue = new IssueObject(</span>
                        &quot;CAPL-MACRO-001&quot;,
<span class="nc" id="L246">                        String.format(&quot;Unknown CAPL macro '%%%s%%'. Only predefined CAPL macros are allowed.&quot;, macroName),</span>
                        SeverityLevel.ERROR,
                        location,
                        suggestion,
<span class="nc" id="L250">                        line.trim() // Code context</span>
                    );

<span class="nc" id="L253">                    issues.add(issue);</span>
                }
<span class="nc" id="L255">            }</span>
        }

<span class="nc" id="L258">        return issues;</span>
    }

    /**
     * Checks if the given string contains any CAPL macros.
     *
     * @param text The text to check
     * @return true if the text contains macros, false otherwise
     */
    public boolean containsMacros(String text) {
<span class="nc bnc" id="L268" title="All 2 branches missed.">        if (text == null) {</span>
<span class="nc" id="L269">            return false;</span>
        }
<span class="nc" id="L271">        return MACRO_PATTERN.matcher(text).find();</span>
    }

    /**
     * Gets all macro names found in the given text.
     * 
     * @param text The text to scan for macros
     * @return A set of macro names (without % symbols)
     */
    public java.util.Set&lt;String&gt; findMacroNames(String text) {
<span class="nc" id="L281">        java.util.Set&lt;String&gt; macroNames = new java.util.HashSet&lt;&gt;();</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">        if (text != null) {</span>
<span class="nc" id="L283">            Matcher matcher = MACRO_PATTERN.matcher(text);</span>
<span class="nc bnc" id="L284" title="All 2 branches missed.">            while (matcher.find()) {</span>
<span class="nc" id="L285">                macroNames.add(matcher.group(1));</span>
            }
        }
<span class="nc" id="L288">        return macroNames;</span>
    }

    /**
     * Checks if a macro name is known (either built-in or user-defined).
     *
     * @param macroName The macro name to check
     * @return true if the macro is known, false otherwise
     */
    private boolean isKnownMacro(String macroName) {
        // Check built-in macros
<span class="nc bnc" id="L299" title="All 2 branches missed.">        if (builtInMacros.containsKey(macroName)) {</span>
<span class="nc" id="L300">            return true;</span>
        }

        // Check special macros
<span class="nc bnc" id="L304" title="All 2 branches missed.">        if (&quot;LINE_NUMBER&quot;.equals(macroName)) {</span>
<span class="nc" id="L305">            return true;</span>
        }

        // Note: User-defined macros are context-dependent and checked during expansion
        // For validation purposes, we only check built-in macros
<span class="nc" id="L310">        return false;</span>
    }

    /**
     * Gets the names of all supported built-in macros.
     *
     * @return A set of built-in macro names
     */
    public java.util.Set&lt;String&gt; getBuiltInMacroNames() {
<span class="nc" id="L319">        java.util.Set&lt;String&gt; names = new java.util.HashSet&lt;&gt;(builtInMacros.keySet());</span>
<span class="nc" id="L320">        names.add(&quot;LINE_NUMBER&quot;); // Add LINE_NUMBER which is handled specially</span>
<span class="nc" id="L321">        return names;</span>
    }

    /**
     * Exception thrown when macro expansion fails.
     */
    public static class MacroExpansionException extends Exception {
        public MacroExpansionException(String message) {
<span class="nc" id="L329">            super(message);</span>
<span class="nc" id="L330">        }</span>

        public MacroExpansionException(String message, Throwable cause) {
<span class="nc" id="L333">            super(message, cause);</span>
<span class="nc" id="L334">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>