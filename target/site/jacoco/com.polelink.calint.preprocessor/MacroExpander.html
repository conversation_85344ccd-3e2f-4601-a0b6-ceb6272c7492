<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MacroExpander</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_class">MacroExpander</span></div><h1>MacroExpander</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">410 of 410</td><td class="ctr2">0%</td><td class="bar">47 of 47</td><td class="ctr2">0%</td><td class="ctr1">40</td><td class="ctr2">40</td><td class="ctr1">100</td><td class="ctr2">100</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a12"><a href="MacroExpander.java.html#L212" class="el_method">validateMacros(String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="92" alt="92"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="MacroExpander.java.html#L62" class="el_method">expandMacros(String, MacroContext)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="55" alt="55"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="MacroExpander.java.html#L95" class="el_method">expandMacrosInLine(String, MacroContext, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="55" alt="55"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="MacroExpander.java.html#L124" class="el_method">resolveMacro(String, MacroContext, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="69" height="10" title="53" alt="53"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="MacroExpander.java.html#L153" class="el_method">createBuiltInMacros()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="46" alt="46"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="MacroExpander.java.html#L181" class="el_method">getDefaultValueForMacro(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="26" alt="26"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="MacroExpander.java.html#L281" class="el_method">findMacroNames(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="22" alt="22"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><a href="MacroExpander.java.html#L299" class="el_method">isKnownMacro(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="15" alt="15"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a5"><a href="MacroExpander.java.html#L319" class="el_method">getBuiltInMacroNames()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="MacroExpander.java.html#L166" class="el_method">lambda$createBuiltInMacros$0(MacroContext)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="10" alt="10"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a0"><a href="MacroExpander.java.html#L268" class="el_method">containsMacros(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h9">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="MacroExpander.java.html#L49" class="el_method">MacroExpander()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="7" alt="7"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a11"><a href="MacroExpander.java.html#L37" class="el_method">static {...}</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="7" alt="7"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>