<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.preprocessor</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.preprocessor</span></div><h1>com.polelink.calint.preprocessor</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,707 of 1,707</td><td class="ctr2">0%</td><td class="bar">165 of 165</td><td class="ctr2">0%</td><td class="ctr1">195</td><td class="ctr2">195</td><td class="ctr1">399</td><td class="ctr2">399</td><td class="ctr1">109</td><td class="ctr2">109</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a3"><a href="MacroExpander.html" class="el_class">MacroExpander</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="410" alt="410"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="47" alt="47"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">40</td><td class="ctr2" id="g0">40</td><td class="ctr1" id="h0">100</td><td class="ctr2" id="i0">100</td><td class="ctr1" id="j3">13</td><td class="ctr2" id="k3">13</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="MacroContext.html" class="el_class">MacroContext</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="284" alt="284"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="28" alt="28"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">33</td><td class="ctr2" id="g2">33</td><td class="ctr1" id="h2">54</td><td class="ctr2" id="i2">54</td><td class="ctr1" id="j0">19</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a5"><a href="PreprocessedSourceFile.html" class="el_class">PreprocessedSourceFile</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="274" alt="274"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="34" alt="34"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">36</td><td class="ctr2" id="g1">36</td><td class="ctr1" id="h3">50</td><td class="ctr2" id="i3">50</td><td class="ctr1" id="j1">19</td><td class="ctr2" id="k1">19</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="CaplPreprocessor.html" class="el_class">CaplPreprocessor</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="264" alt="264"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="24" alt="24"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">24</td><td class="ctr2" id="g4">24</td><td class="ctr1" id="h1">82</td><td class="ctr2" id="i1">82</td><td class="ctr1" id="j4">12</td><td class="ctr2" id="k4">12</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a6"><a href="PreprocessorResult.html" class="el_class">PreprocessorResult</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="230" alt="230"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="22" alt="22"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">27</td><td class="ctr2" id="g3">27</td><td class="ctr1" id="h4">43</td><td class="ctr2" id="i4">43</td><td class="ctr1" id="j2">16</td><td class="ctr2" id="k2">16</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a2"><a href="MacroContext$Builder.html" class="el_class">MacroContext.Builder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="105" alt="105"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">17</td><td class="ctr2" id="g5">17</td><td class="ctr1" id="h5">34</td><td class="ctr2" id="i5">34</td><td class="ctr1" id="j5">12</td><td class="ctr2" id="k5">12</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a7"><a href="PreprocessorResult$Builder.html" class="el_class">PreprocessorResult.Builder</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="66" alt="66"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">9</td><td class="ctr2" id="g6">9</td><td class="ctr1" id="h6">18</td><td class="ctr2" id="i6">18</td><td class="ctr1" id="j6">9</td><td class="ctr2" id="k6">9</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a8"><a href="PreprocessorResult$MacroExpansion.html" class="el_class">PreprocessorResult.MacroExpansion</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="65" alt="65"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">7</td><td class="ctr2" id="g7">7</td><td class="ctr1" id="h7">14</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j7">7</td><td class="ctr2" id="k7">7</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a4"><a href="MacroExpander$MacroExpansionException.html" class="el_class">MacroExpander.MacroExpansionException</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j8">2</td><td class="ctr2" id="k8">2</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>