<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MacroContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_source">MacroContext.java</span></div><h1>MacroContext.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Represents the context information needed for CAPL macro expansion.
 * 
 * This class contains all the contextual information that CAPL macros
 * can access during preprocessing, such as file names, node information,
 * network configuration, etc.
 * 
 * Based on Vector CANoe CAPL documentation, the following macros are supported:
 * - %BASE_FILE_NAME% - Name of the compiled file (e.g., SomeFile.can)
 * - %BASE_FILE_NAME_NO_EXT% - Name without extension (e.g., SomeFile)
 * - %BUS_TYPE% - Bus system of the channel to which the node is assigned
 * - %CHANNEL% - Number of the channel to which the node is assigned
 * - %FILE_NAME% - Name of the source code file (e.g., SomeIncludeFile.cin)
 * - %FILE_NAME_NO_EXT% - Source file name without extension
 * - %LINE_NUMBER% - Number of the line containing the macro
 * - %NETWORK_NAME% - Name of the network to which the node is assigned
 * - %NODE_NAME% - Name of the node
 */
public class MacroContext {

    // File-related context
    private final String sourceFileName;
    private final String baseFileName;
    private final Path sourceFilePath;
    private final Path baseFilePath;

    // Node and network context
    private final String nodeName;
    private final String networkName;
    private final String busType;
    private final int channel;

    // Additional user-defined macros
    private final Map&lt;String, String&gt; userDefinedMacros;

    /**
     * Private constructor for builder pattern.
     */
<span class="nc" id="L47">    private MacroContext(Builder builder) {</span>
<span class="nc" id="L48">        this.sourceFileName = builder.sourceFileName;</span>
<span class="nc" id="L49">        this.baseFileName = builder.baseFileName;</span>
<span class="nc" id="L50">        this.sourceFilePath = builder.sourceFilePath;</span>
<span class="nc" id="L51">        this.baseFilePath = builder.baseFilePath;</span>
<span class="nc" id="L52">        this.nodeName = builder.nodeName;</span>
<span class="nc" id="L53">        this.networkName = builder.networkName;</span>
<span class="nc" id="L54">        this.busType = builder.busType;</span>
<span class="nc" id="L55">        this.channel = builder.channel;</span>
<span class="nc" id="L56">        this.userDefinedMacros = new HashMap&lt;&gt;(builder.userDefinedMacros);</span>
<span class="nc" id="L57">    }</span>

    /**
     * Gets the source file name (e.g., &quot;SomeIncludeFile.cin&quot;).
     */
    public String getSourceFileName() {
<span class="nc" id="L63">        return sourceFileName;</span>
    }

    /**
     * Gets the source file name without extension (e.g., &quot;SomeIncludeFile&quot;).
     */
    public String getSourceFileNameNoExt() {
<span class="nc bnc" id="L70" title="All 2 branches missed.">        if (sourceFileName == null) return null;</span>
<span class="nc" id="L71">        int lastDot = sourceFileName.lastIndexOf('.');</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">        return lastDot &gt; 0 ? sourceFileName.substring(0, lastDot) : sourceFileName;</span>
    }

    /**
     * Gets the base file name (e.g., &quot;SomeFile.can&quot;).
     */
    public String getBaseFileName() {
<span class="nc" id="L79">        return baseFileName;</span>
    }

    /**
     * Gets the base file name without extension (e.g., &quot;SomeFile&quot;).
     */
    public String getBaseFileNameNoExt() {
<span class="nc bnc" id="L86" title="All 2 branches missed.">        if (baseFileName == null) return null;</span>
<span class="nc" id="L87">        int lastDot = baseFileName.lastIndexOf('.');</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">        return lastDot &gt; 0 ? baseFileName.substring(0, lastDot) : baseFileName;</span>
    }

    /**
     * Gets the source file path.
     */
    public Path getSourceFilePath() {
<span class="nc" id="L95">        return sourceFilePath;</span>
    }

    /**
     * Gets the base file path.
     */
    public Path getBaseFilePath() {
<span class="nc" id="L102">        return baseFilePath;</span>
    }

    /**
     * Gets the node name.
     */
    public String getNodeName() {
<span class="nc" id="L109">        return nodeName;</span>
    }

    /**
     * Gets the network name.
     */
    public String getNetworkName() {
<span class="nc" id="L116">        return networkName;</span>
    }

    /**
     * Gets the bus type.
     */
    public String getBusType() {
<span class="nc" id="L123">        return busType;</span>
    }

    /**
     * Gets the channel number.
     */
    public int getChannel() {
<span class="nc" id="L130">        return channel;</span>
    }

    /**
     * Gets a user-defined macro value.
     */
    public String getUserDefinedMacro(String macroName) {
<span class="nc" id="L137">        return userDefinedMacros.get(macroName);</span>
    }

    /**
     * Gets all user-defined macros.
     */
    public Map&lt;String, String&gt; getUserDefinedMacros() {
<span class="nc" id="L144">        return new HashMap&lt;&gt;(userDefinedMacros);</span>
    }

    /**
     * Creates a new builder for MacroContext.
     */
    public static Builder builder() {
<span class="nc" id="L151">        return new Builder();</span>
    }

    /**
     * Creates a simple MacroContext for testing with just file information.
     */
    public static MacroContext forFile(String fileName) {
<span class="nc" id="L158">        return builder()</span>
<span class="nc" id="L159">                .sourceFileName(fileName)</span>
<span class="nc" id="L160">                .baseFileName(fileName)</span>
<span class="nc" id="L161">                .build();</span>
    }

    /**
     * Creates a MacroContext for testing with default values to avoid warnings.
     */
    public static MacroContext forTesting(String fileName) {
<span class="nc" id="L168">        return builder()</span>
<span class="nc" id="L169">                .sourceFileName(fileName)</span>
<span class="nc" id="L170">                .baseFileName(fileName)</span>
<span class="nc" id="L171">                .nodeName(&quot;TestNode&quot;)</span>
<span class="nc" id="L172">                .networkName(&quot;TestNetwork&quot;)</span>
<span class="nc" id="L173">                .busType(&quot;CAN&quot;)</span>
<span class="nc" id="L174">                .channel(1)</span>
<span class="nc" id="L175">                .build();</span>
    }

    /**
     * Builder class for MacroContext.
     */
<span class="nc" id="L181">    public static class Builder {</span>
        private String sourceFileName;
        private String baseFileName;
        private Path sourceFilePath;
        private Path baseFilePath;
        private String nodeName;
        private String networkName;
        private String busType;
<span class="nc" id="L189">        private int channel = -1;</span>
<span class="nc" id="L190">        private Map&lt;String, String&gt; userDefinedMacros = new HashMap&lt;&gt;();</span>

        public Builder sourceFileName(String sourceFileName) {
<span class="nc" id="L193">            this.sourceFileName = sourceFileName;</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">            if (sourceFileName != null) {</span>
<span class="nc" id="L195">                this.sourceFilePath = Paths.get(sourceFileName);</span>
            }
<span class="nc" id="L197">            return this;</span>
        }

        public Builder baseFileName(String baseFileName) {
<span class="nc" id="L201">            this.baseFileName = baseFileName;</span>
<span class="nc bnc" id="L202" title="All 2 branches missed.">            if (baseFileName != null) {</span>
<span class="nc" id="L203">                this.baseFilePath = Paths.get(baseFileName);</span>
            }
<span class="nc" id="L205">            return this;</span>
        }

        public Builder sourceFilePath(Path sourceFilePath) {
<span class="nc" id="L209">            this.sourceFilePath = sourceFilePath;</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">            if (sourceFilePath != null) {</span>
<span class="nc" id="L211">                this.sourceFileName = sourceFilePath.getFileName().toString();</span>
            }
<span class="nc" id="L213">            return this;</span>
        }

        public Builder baseFilePath(Path baseFilePath) {
<span class="nc" id="L217">            this.baseFilePath = baseFilePath;</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">            if (baseFilePath != null) {</span>
<span class="nc" id="L219">                this.baseFileName = baseFilePath.getFileName().toString();</span>
            }
<span class="nc" id="L221">            return this;</span>
        }

        public Builder nodeName(String nodeName) {
<span class="nc" id="L225">            this.nodeName = nodeName;</span>
<span class="nc" id="L226">            return this;</span>
        }

        public Builder networkName(String networkName) {
<span class="nc" id="L230">            this.networkName = networkName;</span>
<span class="nc" id="L231">            return this;</span>
        }

        public Builder busType(String busType) {
<span class="nc" id="L235">            this.busType = busType;</span>
<span class="nc" id="L236">            return this;</span>
        }

        public Builder channel(int channel) {
<span class="nc" id="L240">            this.channel = channel;</span>
<span class="nc" id="L241">            return this;</span>
        }

        public Builder addUserDefinedMacro(String name, String value) {
<span class="nc" id="L245">            this.userDefinedMacros.put(name, value);</span>
<span class="nc" id="L246">            return this;</span>
        }

        public Builder userDefinedMacros(Map&lt;String, String&gt; macros) {
<span class="nc" id="L250">            this.userDefinedMacros.clear();</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">            if (macros != null) {</span>
<span class="nc" id="L252">                this.userDefinedMacros.putAll(macros);</span>
            }
<span class="nc" id="L254">            return this;</span>
        }

        public MacroContext build() {
<span class="nc" id="L258">            return new MacroContext(this);</span>
        }
    }

    @Override
    public boolean equals(Object obj) {
<span class="nc bnc" id="L264" title="All 2 branches missed.">        if (this == obj) return true;</span>
<span class="nc bnc" id="L265" title="All 4 branches missed.">        if (obj == null || getClass() != obj.getClass()) return false;</span>
        
<span class="nc" id="L267">        MacroContext that = (MacroContext) obj;</span>
<span class="nc bnc" id="L268" title="All 2 branches missed.">        return channel == that.channel &amp;&amp;</span>
<span class="nc bnc" id="L269" title="All 2 branches missed.">               Objects.equals(sourceFileName, that.sourceFileName) &amp;&amp;</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">               Objects.equals(baseFileName, that.baseFileName) &amp;&amp;</span>
<span class="nc bnc" id="L271" title="All 2 branches missed.">               Objects.equals(nodeName, that.nodeName) &amp;&amp;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">               Objects.equals(networkName, that.networkName) &amp;&amp;</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">               Objects.equals(busType, that.busType) &amp;&amp;</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">               Objects.equals(userDefinedMacros, that.userDefinedMacros);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L279">        return Objects.hash(sourceFileName, baseFileName, nodeName, </span>
<span class="nc" id="L280">                          networkName, busType, channel, userDefinedMacros);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L285">        return String.format(&quot;MacroContext{sourceFile='%s', baseFile='%s', node='%s', network='%s', bus='%s', channel=%d}&quot;,</span>
<span class="nc" id="L286">                           sourceFileName, baseFileName, nodeName, networkName, busType, channel);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>