<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplPreprocessor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_source">CaplPreprocessor.java</span></div><h1>CaplPreprocessor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor;

import com.polelink.calint.issue.IssueObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

/**
 * Main CAPL preprocessor that handles macro expansion and other preprocessing tasks.
 * 
 * This class serves as the entry point for CAPL preprocessing operations.
 * It coordinates macro expansion, include file processing, and other preprocessing
 * tasks that need to be performed before the source code is passed to the
 * ANTLR parser.
 * 
 * The preprocessor follows the traditional compiler architecture:
 * Source Code → Preprocessor → Lexer → Parser → AST
 * 
 * Current capabilities:
 * - CAPL macro expansion (%NODE_NAME%, %FILE_NAME%, etc.)
 * - Context-aware preprocessing based on simulation setup
 * 
 * Future capabilities:
 * - #include directive processing
 * - #define directive processing
 * - Conditional compilation (#if, #ifdef, etc.)
 */
public class CaplPreprocessor {

<span class="nc" id="L35">    private static final Logger LOGGER = LoggerFactory.getLogger(CaplPreprocessor.class);</span>

    private final MacroExpander macroExpander;

    /**
     * Creates a new CAPL preprocessor with default configuration.
     */
<span class="nc" id="L42">    public CaplPreprocessor() {</span>
<span class="nc" id="L43">        this.macroExpander = new MacroExpander();</span>
<span class="nc" id="L44">    }</span>

    /**
     * Creates a new CAPL preprocessor with custom macro expander.
     * 
     * @param macroExpander The macro expander to use
     */
<span class="nc" id="L51">    public CaplPreprocessor(MacroExpander macroExpander) {</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">        this.macroExpander = macroExpander != null ? macroExpander : new MacroExpander();</span>
<span class="nc" id="L53">    }</span>

    /**
     * Preprocesses CAPL source code from a string.
     * 
     * @param sourceCode The CAPL source code to preprocess
     * @param context The macro context for preprocessing
     * @return The preprocessing result
     */
    public PreprocessorResult preprocess(String sourceCode, MacroContext context) {
<span class="nc bnc" id="L63" title="All 2 branches missed.">        if (sourceCode == null) {</span>
<span class="nc" id="L64">            return PreprocessorResult.failure(null, context, &quot;Source code cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (context == null) {</span>
<span class="nc" id="L67">            return PreprocessorResult.failure(sourceCode, null, &quot;Macro context cannot be null&quot;);</span>
        }

        try {
<span class="nc" id="L71">            LOGGER.debug(&quot;Starting CAPL preprocessing&quot;);</span>

            // First, validate all macros in the source code
<span class="nc bnc" id="L74" title="All 2 branches missed.">            String filePath = (context.getSourceFileName() != null)</span>
<span class="nc" id="L75">                ? context.getSourceFileName()</span>
<span class="nc" id="L76">                : &quot;&lt;unknown&gt;&quot;;</span>

<span class="nc" id="L78">            List&lt;IssueObject&gt; macroIssues = macroExpander.validateMacros(sourceCode, filePath);</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">            if (!macroIssues.isEmpty()) {</span>
                // Create a failure result with the macro issues
<span class="nc" id="L81">                PreprocessorResult.Builder resultBuilder = PreprocessorResult.builder()</span>
<span class="nc" id="L82">                        .originalSource(sourceCode)</span>
<span class="nc" id="L83">                        .preprocessedSource(sourceCode) // Keep original since we can't process it</span>
<span class="nc" id="L84">                        .context(context)</span>
<span class="nc" id="L85">                        .successful(false);</span>

                // Add all macro issues directly
<span class="nc bnc" id="L88" title="All 2 branches missed.">                for (IssueObject issue : macroIssues) {</span>
<span class="nc" id="L89">                    resultBuilder.addIssue(issue);</span>
<span class="nc" id="L90">                }</span>

<span class="nc" id="L92">                return resultBuilder.build();</span>
            }

            // Check if source contains any macros
<span class="nc bnc" id="L96" title="All 2 branches missed.">            if (!macroExpander.containsMacros(sourceCode)) {</span>
<span class="nc" id="L97">                LOGGER.debug(&quot;No macros found in source code, skipping expansion&quot;);</span>
<span class="nc" id="L98">                return PreprocessorResult.success(sourceCode, sourceCode, context);</span>
            }

            // All macros are valid, proceed with expansion
<span class="nc" id="L102">            String preprocessedSource = macroExpander.expandMacros(sourceCode, context);</span>
            
            // Build result with expansion information
<span class="nc" id="L105">            PreprocessorResult.Builder resultBuilder = PreprocessorResult.builder()</span>
<span class="nc" id="L106">                    .originalSource(sourceCode)</span>
<span class="nc" id="L107">                    .preprocessedSource(preprocessedSource)</span>
<span class="nc" id="L108">                    .context(context)</span>
<span class="nc" id="L109">                    .successful(true);</span>

            // Log macro expansions
<span class="nc bnc" id="L112" title="All 2 branches missed.">            if (!sourceCode.equals(preprocessedSource)) {</span>
<span class="nc" id="L113">                LOGGER.debug(&quot;Macro expansion completed successfully&quot;);</span>
                // TODO: Add detailed expansion tracking in future versions
            }

<span class="nc" id="L117">            return resultBuilder.build();</span>

<span class="nc" id="L119">        } catch (MacroExpander.MacroExpansionException e) {</span>
<span class="nc" id="L120">            LOGGER.error(&quot;Macro expansion failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L121">            return PreprocessorResult.failure(sourceCode, context, &quot;Macro expansion failed: &quot; + e.getMessage());</span>
<span class="nc" id="L122">        } catch (Exception e) {</span>
<span class="nc" id="L123">            LOGGER.error(&quot;Unexpected error during preprocessing: {}&quot;, e.getMessage());</span>
<span class="nc" id="L124">            return PreprocessorResult.failure(sourceCode, context, &quot;Preprocessing failed: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Preprocesses CAPL source code from a file.
     * 
     * @param filePath The path to the CAPL source file
     * @param context The macro context for preprocessing
     * @return The preprocessing result
     * @throws IOException if the file cannot be read
     */
    public PreprocessorResult preprocessFile(Path filePath, MacroContext context) throws IOException {
<span class="nc bnc" id="L137" title="All 2 branches missed.">        if (filePath == null) {</span>
<span class="nc" id="L138">            return PreprocessorResult.failure(null, context, &quot;File path cannot be null&quot;);</span>
        }

<span class="nc" id="L141">        LOGGER.info(&quot;Preprocessing CAPL file: &quot; + filePath);</span>

        // Read source code from file
        String sourceCode;
        try {
<span class="nc" id="L146">            sourceCode = Files.readString(filePath);</span>
<span class="nc" id="L147">        } catch (IOException e) {</span>
<span class="nc" id="L148">            LOGGER.error(&quot;Failed to read file: {} - {}&quot;, filePath, e.getMessage());</span>
<span class="nc" id="L149">            throw e;</span>
<span class="nc" id="L150">        }</span>

        // Update context with file information if not already set
<span class="nc" id="L153">        MacroContext fileContext = context;</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">        if (context.getSourceFileName() == null) {</span>
<span class="nc" id="L155">            fileContext = MacroContext.builder()</span>
<span class="nc" id="L156">                    .sourceFilePath(filePath)</span>
<span class="nc" id="L157">                    .baseFileName(context.getBaseFileName())</span>
<span class="nc" id="L158">                    .nodeName(context.getNodeName())</span>
<span class="nc" id="L159">                    .networkName(context.getNetworkName())</span>
<span class="nc" id="L160">                    .busType(context.getBusType())</span>
<span class="nc" id="L161">                    .channel(context.getChannel())</span>
<span class="nc" id="L162">                    .userDefinedMacros(context.getUserDefinedMacros())</span>
<span class="nc" id="L163">                    .build();</span>
        }

<span class="nc" id="L166">        return preprocess(sourceCode, fileContext);</span>
    }

    /**
     * Preprocesses CAPL source code with minimal context (for testing).
     *
     * @param sourceCode The CAPL source code to preprocess
     * @param fileName The source file name (can be null)
     * @return The preprocessing result
     */
    public PreprocessorResult preprocessSimple(String sourceCode, String fileName) {
<span class="nc bnc" id="L177" title="All 2 branches missed.">        MacroContext context = fileName != null</span>
<span class="nc" id="L178">                ? MacroContext.forFile(fileName)</span>
<span class="nc" id="L179">                : MacroContext.builder().build();</span>
<span class="nc" id="L180">        return preprocess(sourceCode, context);</span>
    }

    /**
     * Preprocesses CAPL source code with testing context (avoids warnings).
     *
     * @param sourceCode The CAPL source code to preprocess
     * @param fileName The source file name (can be null)
     * @return The preprocessing result
     */
    public PreprocessorResult preprocessForTesting(String sourceCode, String fileName) {
<span class="nc bnc" id="L191" title="All 2 branches missed.">        MacroContext context = fileName != null</span>
<span class="nc" id="L192">                ? MacroContext.forTesting(fileName)</span>
<span class="nc" id="L193">                : MacroContext.builder()</span>
<span class="nc" id="L194">                    .nodeName(&quot;TestNode&quot;)</span>
<span class="nc" id="L195">                    .networkName(&quot;TestNetwork&quot;)</span>
<span class="nc" id="L196">                    .busType(&quot;CAN&quot;)</span>
<span class="nc" id="L197">                    .channel(1)</span>
<span class="nc" id="L198">                    .build();</span>
<span class="nc" id="L199">        return preprocess(sourceCode, context);</span>
    }

    /**
     * Checks if the given source code contains any CAPL macros.
     * 
     * @param sourceCode The source code to check
     * @return true if macros are found, false otherwise
     */
    public boolean containsMacros(String sourceCode) {
<span class="nc" id="L209">        return macroExpander.containsMacros(sourceCode);</span>
    }

    /**
     * Validates all macros in the source code and returns any issues found.
     * This method can be used to check for unknown macros before preprocessing.
     *
     * @param sourceCode The source code to validate
     * @param filePath The file path for error reporting
     * @return List of IssueObject for any unknown macros found
     */
    public List&lt;IssueObject&gt; validateMacros(String sourceCode, String filePath) {
<span class="nc" id="L221">        return macroExpander.validateMacros(sourceCode, filePath);</span>
    }

    /**
     * Gets the macro expander used by this preprocessor.
     *
     * @return The macro expander
     */
    public MacroExpander getMacroExpander() {
<span class="nc" id="L230">        return macroExpander;</span>
    }

    /**
     * Creates a preprocessor with a specific macro context for a simulation setup.
     * 
     * @param nodeName The name of the node
     * @param networkName The name of the network
     * @param busType The bus type (e.g., &quot;CAN&quot;, &quot;LIN&quot;, &quot;FlexRay&quot;)
     * @param channel The channel number
     * @return A configured preprocessor
     */
    public static CaplPreprocessor forSimulation(String nodeName, String networkName, String busType, int channel) {
        // This method could be used to create a preprocessor with simulation-specific configuration
        // For now, it returns a standard preprocessor
<span class="nc" id="L245">        return new CaplPreprocessor();</span>
    }

    /**
     * Creates a preprocessor for testing purposes with minimal configuration.
     * 
     * @return A preprocessor suitable for testing
     */
    public static CaplPreprocessor forTesting() {
<span class="nc" id="L254">        return new CaplPreprocessor();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>