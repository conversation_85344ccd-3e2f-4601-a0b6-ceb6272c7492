<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.preprocessor</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.preprocessor</span></div><h1>com.polelink.calint.preprocessor</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,707 of 1,707</td><td class="ctr2">0%</td><td class="bar">165 of 165</td><td class="ctr2">0%</td><td class="ctr1">195</td><td class="ctr2">195</td><td class="ctr1">399</td><td class="ctr2">399</td><td class="ctr1">109</td><td class="ctr2">109</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="MacroExpander.java.html" class="el_source">MacroExpander.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="419" alt="419"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="47" alt="47"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f2">42</td><td class="ctr2" id="g2">42</td><td class="ctr1" id="h0">104</td><td class="ctr2" id="i0">104</td><td class="ctr1" id="j3">15</td><td class="ctr2" id="k3">15</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a1"><a href="MacroContext.java.html" class="el_source">MacroContext.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="111" height="10" title="389" alt="389"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="38" alt="38"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">50</td><td class="ctr2" id="g0">50</td><td class="ctr1" id="h1">88</td><td class="ctr2" id="i1">88</td><td class="ctr1" id="j1">31</td><td class="ctr2" id="k1">31</td><td class="ctr1" id="l2">2</td><td class="ctr2" id="m2">2</td></tr><tr><td id="a4"><a href="PreprocessorResult.java.html" class="el_source">PreprocessorResult.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="103" height="10" title="361" alt="361"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">43</td><td class="ctr2" id="g1">43</td><td class="ctr1" id="h3">75</td><td class="ctr2" id="i3">75</td><td class="ctr1" id="j0">32</td><td class="ctr2" id="k0">32</td><td class="ctr1" id="l0">3</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a3"><a href="PreprocessedSourceFile.java.html" class="el_source">PreprocessedSourceFile.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="274" alt="274"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="34" alt="34"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">36</td><td class="ctr2" id="g3">36</td><td class="ctr1" id="h4">50</td><td class="ctr2" id="i4">50</td><td class="ctr1" id="j2">19</td><td class="ctr2" id="k2">19</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="CaplPreprocessor.java.html" class="el_source">CaplPreprocessor.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="264" alt="264"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="24" alt="24"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">24</td><td class="ctr2" id="g4">24</td><td class="ctr1" id="h2">82</td><td class="ctr2" id="i2">82</td><td class="ctr1" id="j4">12</td><td class="ctr2" id="k4">12</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>