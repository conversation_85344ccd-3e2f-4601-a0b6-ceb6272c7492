<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PreprocessorResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_source">PreprocessorResult.java</span></div><h1>PreprocessorResult.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor;

import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.util.SourceLocation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Represents the result of CAPL preprocessing.
 * 
 * This class contains the preprocessed source code along with metadata
 * about the preprocessing operation, such as expanded macros, warnings,
 * and any issues encountered during preprocessing.
 */
public class PreprocessorResult {

    private final String originalSource;
    private final String preprocessedSource;
    private final MacroContext context;
    private final List&lt;MacroExpansion&gt; expansions;
    private final List&lt;IssueObject&gt; issues;
    private final boolean successful;

    /**
     * Private constructor for builder pattern.
     */
<span class="nc" id="L31">    private PreprocessorResult(Builder builder) {</span>
<span class="nc" id="L32">        this.originalSource = builder.originalSource;</span>
<span class="nc" id="L33">        this.preprocessedSource = builder.preprocessedSource;</span>
<span class="nc" id="L34">        this.context = builder.context;</span>
<span class="nc" id="L35">        this.expansions = Collections.unmodifiableList(new ArrayList&lt;&gt;(builder.expansions));</span>
<span class="nc" id="L36">        this.issues = Collections.unmodifiableList(new ArrayList&lt;&gt;(builder.issues));</span>
<span class="nc" id="L37">        this.successful = builder.successful;</span>
<span class="nc" id="L38">    }</span>

    /**
     * Gets the original source code before preprocessing.
     */
    public String getOriginalSource() {
<span class="nc" id="L44">        return originalSource;</span>
    }

    /**
     * Gets the preprocessed source code with macros expanded.
     */
    public String getPreprocessedSource() {
<span class="nc" id="L51">        return preprocessedSource;</span>
    }

    /**
     * Gets the macro context used for preprocessing.
     */
    public MacroContext getContext() {
<span class="nc" id="L58">        return context;</span>
    }

    /**
     * Gets the list of macro expansions that were performed.
     */
    public List&lt;MacroExpansion&gt; getExpansions() {
<span class="nc" id="L65">        return expansions;</span>
    }

    /**
     * Gets the list of issues generated during preprocessing.
     */
    public List&lt;IssueObject&gt; getIssues() {
<span class="nc" id="L72">        return issues;</span>
    }

    /**
     * Returns true if preprocessing was successful, false otherwise.
     */
    public boolean isSuccessful() {
<span class="nc" id="L79">        return successful;</span>
    }

    /**
     * Returns true if any macros were expanded during preprocessing.
     */
    public boolean hasMacroExpansions() {
<span class="nc bnc" id="L86" title="All 2 branches missed.">        return !expansions.isEmpty();</span>
    }

    /**
     * Returns true if any issues were generated during preprocessing.
     */
    public boolean hasIssues() {
<span class="nc bnc" id="L93" title="All 2 branches missed.">        return !issues.isEmpty();</span>
    }

    /**
     * Gets the number of macros that were expanded.
     */
    public int getExpansionCount() {
<span class="nc" id="L100">        return expansions.size();</span>
    }

    /**
     * Creates a new builder for PreprocessorResult.
     */
    public static Builder builder() {
<span class="nc" id="L107">        return new Builder();</span>
    }

    /**
     * Creates a successful result with no expansions.
     */
    public static PreprocessorResult success(String originalSource, String preprocessedSource, MacroContext context) {
<span class="nc" id="L114">        return builder()</span>
<span class="nc" id="L115">                .originalSource(originalSource)</span>
<span class="nc" id="L116">                .preprocessedSource(preprocessedSource)</span>
<span class="nc" id="L117">                .context(context)</span>
<span class="nc" id="L118">                .successful(true)</span>
<span class="nc" id="L119">                .build();</span>
    }

    /**
     * Creates a failed result.
     */
    public static PreprocessorResult failure(String originalSource, MacroContext context, String errorMessage) {
<span class="nc" id="L126">        return builder()</span>
<span class="nc" id="L127">                .originalSource(originalSource)</span>
<span class="nc" id="L128">                .preprocessedSource(originalSource) // Keep original on failure</span>
<span class="nc" id="L129">                .context(context)</span>
<span class="nc" id="L130">                .successful(false)</span>
<span class="nc" id="L131">                .addIssue(new IssueObject(&quot;CAPL-PREPROCESSOR-001&quot;, errorMessage,</span>
                    SeverityLevel.ERROR,
                    new SourceLocation(&quot;unknown&quot;, 1, 1, 1, 1)))
<span class="nc" id="L134">                .build();</span>
    }

    /**
     * Represents a single macro expansion.
     */
    public static class MacroExpansion {
        private final String macroName;
        private final String originalValue;
        private final String expandedValue;
        private final int lineNumber;
        private final int columnNumber;

<span class="nc" id="L147">        public MacroExpansion(String macroName, String originalValue, String expandedValue, int lineNumber, int columnNumber) {</span>
<span class="nc" id="L148">            this.macroName = macroName;</span>
<span class="nc" id="L149">            this.originalValue = originalValue;</span>
<span class="nc" id="L150">            this.expandedValue = expandedValue;</span>
<span class="nc" id="L151">            this.lineNumber = lineNumber;</span>
<span class="nc" id="L152">            this.columnNumber = columnNumber;</span>
<span class="nc" id="L153">        }</span>

<span class="nc" id="L155">        public String getMacroName() { return macroName; }</span>
<span class="nc" id="L156">        public String getOriginalValue() { return originalValue; }</span>
<span class="nc" id="L157">        public String getExpandedValue() { return expandedValue; }</span>
<span class="nc" id="L158">        public int getLineNumber() { return lineNumber; }</span>
<span class="nc" id="L159">        public int getColumnNumber() { return columnNumber; }</span>

        @Override
        public String toString() {
<span class="nc" id="L163">            return String.format(&quot;MacroExpansion{%s: '%s' -&gt; '%s' at %d:%d}&quot;, </span>
<span class="nc" id="L164">                               macroName, originalValue, expandedValue, lineNumber, columnNumber);</span>
        }
    }



    /**
     * Builder class for PreprocessorResult.
     */
<span class="nc" id="L173">    public static class Builder {</span>
        private String originalSource;
        private String preprocessedSource;
        private MacroContext context;
<span class="nc" id="L177">        private List&lt;MacroExpansion&gt; expansions = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L178">        private List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L179">        private boolean successful = true;</span>

        public Builder originalSource(String originalSource) {
<span class="nc" id="L182">            this.originalSource = originalSource;</span>
<span class="nc" id="L183">            return this;</span>
        }

        public Builder preprocessedSource(String preprocessedSource) {
<span class="nc" id="L187">            this.preprocessedSource = preprocessedSource;</span>
<span class="nc" id="L188">            return this;</span>
        }

        public Builder context(MacroContext context) {
<span class="nc" id="L192">            this.context = context;</span>
<span class="nc" id="L193">            return this;</span>
        }

        public Builder addExpansion(MacroExpansion expansion) {
<span class="nc" id="L197">            this.expansions.add(expansion);</span>
<span class="nc" id="L198">            return this;</span>
        }

        public Builder addExpansion(String macroName, String originalValue, String expandedValue, int lineNumber, int columnNumber) {
<span class="nc" id="L202">            return addExpansion(new MacroExpansion(macroName, originalValue, expandedValue, lineNumber, columnNumber));</span>
        }

        public Builder addIssue(IssueObject issue) {
<span class="nc" id="L206">            this.issues.add(issue);</span>
<span class="nc" id="L207">            return this;</span>
        }

        public Builder successful(boolean successful) {
<span class="nc" id="L211">            this.successful = successful;</span>
<span class="nc" id="L212">            return this;</span>
        }

        public PreprocessorResult build() {
<span class="nc" id="L216">            return new PreprocessorResult(this);</span>
        }
    }

    @Override
    public boolean equals(Object obj) {
<span class="nc bnc" id="L222" title="All 2 branches missed.">        if (this == obj) return true;</span>
<span class="nc bnc" id="L223" title="All 4 branches missed.">        if (obj == null || getClass() != obj.getClass()) return false;</span>
        
<span class="nc" id="L225">        PreprocessorResult that = (PreprocessorResult) obj;</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">        return successful == that.successful &amp;&amp;</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">               Objects.equals(originalSource, that.originalSource) &amp;&amp;</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">               Objects.equals(preprocessedSource, that.preprocessedSource) &amp;&amp;</span>
<span class="nc bnc" id="L229" title="All 2 branches missed.">               Objects.equals(context, that.context) &amp;&amp;</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">               Objects.equals(expansions, that.expansions) &amp;&amp;</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">               Objects.equals(issues, that.issues);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L236">        return Objects.hash(originalSource, preprocessedSource, context, expansions, issues, successful);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L241">        return String.format(&quot;PreprocessorResult{successful=%s, expansions=%d, issues=%d}&quot;,</span>
<span class="nc" id="L242">                           successful, expansions.size(), issues.size());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>