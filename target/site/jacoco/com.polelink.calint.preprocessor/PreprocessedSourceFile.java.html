<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PreprocessedSourceFile.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor</a> &gt; <span class="el_source">PreprocessedSourceFile.java</span></div><h1>PreprocessedSourceFile.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor;

import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.Objects;

import com.polelink.calint.util.SourceFile;

/**
 * Represents a CAPL source file that has been preprocessed.
 *
 * This class wraps a SourceFile to include preprocessing information
 * such as macro expansions, preprocessing warnings, and the original
 * source code before preprocessing.
 *
 * The preprocessed content is what gets passed to the ANTLR parser,
 * while the original content and preprocessing metadata are preserved
 * for error reporting and debugging purposes.
 */
public class PreprocessedSourceFile {

    private final SourceFile originalSourceFile;
    private final SourceFile preprocessedSourceFile;
    private final PreprocessorResult preprocessorResult;
    private final MacroContext macroContext;

    /**
     * Creates a new PreprocessedSourceFile.
     *
     * @param originalSourceFile The original source file before preprocessing
     * @param preprocessorResult The result of preprocessing operations
     */
<span class="nc" id="L33">    public PreprocessedSourceFile(SourceFile originalSourceFile, PreprocessorResult preprocessorResult) {</span>
<span class="nc" id="L34">        this.originalSourceFile = originalSourceFile;</span>
<span class="nc" id="L35">        this.preprocessedSourceFile = new SourceFile(</span>
<span class="nc" id="L36">                originalSourceFile.getAbsolutePath(),</span>
<span class="nc" id="L37">                preprocessorResult.getPreprocessedSource(),</span>
<span class="nc" id="L38">                originalSourceFile.getDetectedCharset()</span>
        );
<span class="nc" id="L40">        this.preprocessorResult = preprocessorResult;</span>
<span class="nc" id="L41">        this.macroContext = preprocessorResult.getContext();</span>
<span class="nc" id="L42">    }</span>

    /**
     * Creates a new PreprocessedSourceFile with explicit parameters.
     *
     * @param filePath The path to the source file
     * @param originalContent The original content before preprocessing
     * @param preprocessedContent The content after preprocessing
     * @param charset The character encoding of the file
     * @param preprocessorResult The preprocessing result
     */
    public PreprocessedSourceFile(Path filePath, String originalContent, String preprocessedContent,
<span class="nc" id="L54">                                Charset charset, PreprocessorResult preprocessorResult) {</span>
<span class="nc" id="L55">        this.originalSourceFile = new SourceFile(filePath, originalContent, charset);</span>
<span class="nc" id="L56">        this.preprocessedSourceFile = new SourceFile(filePath, preprocessedContent, charset);</span>
<span class="nc" id="L57">        this.preprocessorResult = preprocessorResult;</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">        this.macroContext = preprocessorResult != null ? preprocessorResult.getContext() : null;</span>
<span class="nc" id="L59">    }</span>

    /**
     * Gets the absolute path to the file.
     *
     * @return The absolute path
     */
    public Path getAbsolutePath() {
<span class="nc" id="L67">        return preprocessedSourceFile.getAbsolutePath();</span>
    }

    /**
     * Gets the preprocessed source content.
     * This is what should be passed to the parser.
     *
     * @return The preprocessed source content
     */
    public String getContent() {
<span class="nc" id="L77">        return preprocessedSourceFile.getContent();</span>
    }

    /**
     * Gets the character encoding of the file.
     *
     * @return The detected charset
     */
    public Charset getDetectedCharset() {
<span class="nc" id="L86">        return preprocessedSourceFile.getDetectedCharset();</span>
    }

    /**
     * Gets the original source content before preprocessing.
     *
     * @return The original source content
     */
    public String getOriginalContent() {
<span class="nc" id="L95">        return originalSourceFile.getContent();</span>
    }

    /**
     * Gets the preprocessed source content.
     * This is the same as getContent() but provided for clarity.
     *
     * @return The preprocessed source content
     */
    public String getPreprocessedContent() {
<span class="nc" id="L105">        return getContent();</span>
    }

    /**
     * Gets the preprocessing result containing detailed information
     * about macro expansions, warnings, etc.
     *
     * @return The preprocessor result
     */
    public PreprocessorResult getPreprocessorResult() {
<span class="nc" id="L115">        return preprocessorResult;</span>
    }

    /**
     * Gets the SourceFile object that should be used for parsing.
     * This contains the preprocessed content.
     *
     * @return The SourceFile for parsing
     */
    public SourceFile getSourceFileForParsing() {
<span class="nc" id="L125">        return preprocessedSourceFile;</span>
    }

    /**
     * Gets the macro context used during preprocessing.
     * 
     * @return The macro context
     */
    public MacroContext getMacroContext() {
<span class="nc" id="L134">        return macroContext;</span>
    }

    /**
     * Returns true if preprocessing was successful.
     * 
     * @return true if preprocessing succeeded, false otherwise
     */
    public boolean isPreprocessingSuccessful() {
<span class="nc bnc" id="L143" title="All 4 branches missed.">        return preprocessorResult != null &amp;&amp; preprocessorResult.isSuccessful();</span>
    }

    /**
     * Returns true if any macros were expanded during preprocessing.
     * 
     * @return true if macros were expanded, false otherwise
     */
    public boolean hasMacroExpansions() {
<span class="nc bnc" id="L152" title="All 4 branches missed.">        return preprocessorResult != null &amp;&amp; preprocessorResult.hasMacroExpansions();</span>
    }

    /**
     * Returns true if any warnings were generated during preprocessing.
     * 
     * @return true if warnings exist, false otherwise
     */
    public boolean hasPreprocessingWarnings() {
<span class="nc bnc" id="L161" title="All 4 branches missed.">        return preprocessorResult != null &amp;&amp; preprocessorResult.hasIssues();</span>
    }

    /**
     * Gets the number of macro expansions performed.
     * 
     * @return The number of macro expansions
     */
    public int getMacroExpansionCount() {
<span class="nc bnc" id="L170" title="All 2 branches missed.">        return preprocessorResult != null ? preprocessorResult.getExpansionCount() : 0;</span>
    }

    /**
     * Creates a PreprocessedSourceFile from a regular SourceFile with no preprocessing.
     * This is useful when preprocessing is skipped (e.g., no macros found).
     * 
     * @param sourceFile The original source file
     * @return A PreprocessedSourceFile with no preprocessing changes
     */
    public static PreprocessedSourceFile withoutPreprocessing(SourceFile sourceFile) {
<span class="nc" id="L181">        PreprocessorResult noOpResult = PreprocessorResult.success(</span>
<span class="nc" id="L182">                sourceFile.getContent(), </span>
<span class="nc" id="L183">                sourceFile.getContent(), </span>
<span class="nc" id="L184">                MacroContext.forFile(sourceFile.getAbsolutePath().getFileName().toString())</span>
        );
<span class="nc" id="L186">        return new PreprocessedSourceFile(sourceFile, noOpResult);</span>
    }

    /**
     * Creates a PreprocessedSourceFile from a regular SourceFile with failed preprocessing.
     * 
     * @param sourceFile The original source file
     * @param errorMessage The preprocessing error message
     * @return A PreprocessedSourceFile representing failed preprocessing
     */
    public static PreprocessedSourceFile withPreprocessingFailure(SourceFile sourceFile, String errorMessage) {
<span class="nc" id="L197">        MacroContext context = MacroContext.forFile(sourceFile.getAbsolutePath().getFileName().toString());</span>
<span class="nc" id="L198">        PreprocessorResult failureResult = PreprocessorResult.failure(</span>
<span class="nc" id="L199">                sourceFile.getContent(), </span>
                context, 
                errorMessage
        );
<span class="nc" id="L203">        return new PreprocessedSourceFile(sourceFile, failureResult);</span>
    }

    @Override
    public boolean equals(Object obj) {
<span class="nc bnc" id="L208" title="All 2 branches missed.">        if (this == obj) return true;</span>
<span class="nc bnc" id="L209" title="All 4 branches missed.">        if (obj == null || getClass() != obj.getClass()) return false;</span>

<span class="nc" id="L211">        PreprocessedSourceFile that = (PreprocessedSourceFile) obj;</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">        return Objects.equals(originalSourceFile, that.originalSourceFile) &amp;&amp;</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">               Objects.equals(preprocessedSourceFile, that.preprocessedSourceFile) &amp;&amp;</span>
<span class="nc bnc" id="L214" title="All 2 branches missed.">               Objects.equals(preprocessorResult, that.preprocessorResult) &amp;&amp;</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">               Objects.equals(macroContext, that.macroContext);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L220">        return Objects.hash(originalSourceFile, preprocessedSourceFile, preprocessorResult, macroContext);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L225">        return String.format(&quot;PreprocessedSourceFile{path='%s', originalSize=%d, preprocessedSize=%d, expansions=%d, successful=%s}&quot;,</span>
<span class="nc" id="L226">                           getAbsolutePath(),</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">                           getOriginalContent() != null ? getOriginalContent().length() : 0,</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">                           getContent() != null ? getContent().length() : 0,</span>
<span class="nc" id="L229">                           getMacroExpansionCount(),</span>
<span class="nc" id="L230">                           isPreprocessingSuccessful());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>