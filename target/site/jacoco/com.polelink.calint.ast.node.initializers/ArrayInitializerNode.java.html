<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ArrayInitializerNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">ArrayInitializerNode.java</span></div><h1>ArrayInitializerNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.base.ExpressionNode;
import com.polelink.calint.util.SourceLocation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Represents an array initializer in CAPL.
 * 
 * This is a specialized form of initializer specifically for arrays.
 * It can contain either simple values or designated initializers.
 * 
 * Examples:
 * - `{1, 2, 3, 4}` (simple array initializer)
 * - `{[0] = 10, [2] = 20}` (sparse array with designated initializers)
 * - `{1, [5] = 50, 6}` (mixed simple and designated)
 * - `{{1, 2}, {3, 4}}` (multi-dimensional array)
 */
public class ArrayInitializerNode extends AbstractAstNode {
    
    private final InitializerListNode initializerList;
    private final boolean isMultiDimensional;
    
    /**
     * Creates an array initializer with the given initializer list.
     * 
     * @param sourceLocation The source location of this array initializer
     * @param initializerList The list of initializers for array elements
     * @throws IllegalArgumentException if sourceLocation or initializerList is null
     */
    public ArrayInitializerNode(SourceLocation sourceLocation, InitializerListNode initializerList) {
<span class="nc" id="L40">        super(AstNodeType.ARRAY_INITIALIZER, sourceLocation);</span>
        
<span class="nc bnc" id="L42" title="All 2 branches missed.">        if (initializerList == null) {</span>
<span class="nc" id="L43">            throw new IllegalArgumentException(&quot;Initializer list cannot be null&quot;);</span>
        }
        
<span class="nc" id="L46">        this.initializerList = initializerList;</span>
<span class="nc" id="L47">        this.isMultiDimensional = checkIfMultiDimensional();</span>
<span class="nc" id="L48">    }</span>
    
    /**
     * Gets the initializer list for this array.
     * 
     * @return The initializer list
     */
    public InitializerListNode getInitializerList() {
<span class="nc" id="L56">        return initializerList;</span>
    }
    
    /**
     * Gets the number of elements in this array initializer.
     * 
     * @return The number of elements
     */
    public int getElementCount() {
<span class="nc" id="L65">        return initializerList.getElementCount();</span>
    }
    
    /**
     * Gets the element at the specified index.
     * 
     * @param index The index of the element to retrieve
     * @return The element at the specified index
     * @throws IndexOutOfBoundsException if the index is out of range
     */
    public InitializerElementNode getElement(int index) {
<span class="nc" id="L76">        return initializerList.getElement(index);</span>
    }
    
    /**
     * Checks if this is a simple array initializer (no designators).
     * 
     * @return true if this is a simple array initializer
     */
    public boolean isSimpleArrayInitializer() {
<span class="nc" id="L85">        return initializerList.isSimpleArrayInitializer();</span>
    }
    
    /**
     * Checks if this array initializer uses designated initializers.
     * 
     * @return true if any elements use designators
     */
    public boolean hasDesignatedElements() {
<span class="nc" id="L94">        return initializerList.hasDesignatedElements();</span>
    }
    
    /**
     * Checks if this is a multi-dimensional array initializer.
     * 
     * @return true if this contains nested array initializers
     */
    public boolean isMultiDimensional() {
<span class="nc" id="L103">        return isMultiDimensional;</span>
    }
    
    /**
     * Checks if this array initializer is constant (all elements are constant).
     * 
     * @return true if all elements are constant
     */
    public boolean isConstant() {
<span class="nc" id="L112">        return initializerList.isConstant();</span>
    }
    
    /**
     * Checks if this array initializer contains only literal values.
     * 
     * @return true if all elements are literals
     */
    public boolean isLiteral() {
<span class="nc" id="L121">        return initializerList.isLiteral();</span>
    }
    
    /**
     * Gets all simple (non-designated) expressions from this array initializer.
     * This is useful for simple array cases like {1, 2, 3}.
     * 
     * @return A list of expressions from simple elements
     */
    public List&lt;ExpressionNode&gt; getSimpleExpressions() {
<span class="nc" id="L131">        List&lt;ExpressionNode&gt; expressions = new ArrayList&lt;&gt;();</span>
        
<span class="nc bnc" id="L133" title="All 2 branches missed.">        for (InitializerElementNode element : initializerList.getElements()) {</span>
<span class="nc bnc" id="L134" title="All 4 branches missed.">            if (element.isSimple() &amp;&amp; element.getInitializer().isExpressionInitializer()) {</span>
<span class="nc" id="L135">                expressions.add(element.getInitializer().getExpression());</span>
            }
<span class="nc" id="L137">        }</span>
        
<span class="nc" id="L139">        return Collections.unmodifiableList(expressions);</span>
    }
    
    /**
     * Checks if all elements are simple expressions (no designators, no nested lists).
     * 
     * @return true if all elements are simple expressions
     */
    public boolean isAllSimpleExpressions() {
<span class="nc" id="L148">        return initializerList.getElements().stream().allMatch(element -&gt;</span>
<span class="nc bnc" id="L149" title="All 4 branches missed.">            element.isSimple() &amp;&amp; element.getInitializer().isExpressionInitializer());</span>
    }
    
    /**
     * Gets the maximum designated index in this array (if any).
     * This is useful for determining the minimum array size needed.
     * 
     * @return The maximum designated index, or -1 if no designated elements
     */
    public int getMaxDesignatedIndex() {
<span class="nc" id="L159">        int maxIndex = -1;</span>
        
<span class="nc bnc" id="L161" title="All 2 branches missed.">        for (InitializerElementNode element : initializerList.getElements()) {</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">            if (element.isArrayDesignated()) {</span>
<span class="nc" id="L163">                DesignationNode designation = element.getDesignation();</span>
<span class="nc" id="L164">                DesignatorNode firstDesignator = designation.getFirstDesignator();</span>
                
<span class="nc bnc" id="L166" title="All 4 branches missed.">                if (firstDesignator.isArrayDesignator() &amp;&amp; firstDesignator.hasConstantIndex()) {</span>
                    // This is a simplification - in a real implementation, you'd evaluate the constant expression
                    // For now, we'll return -1 to indicate we can't determine it
<span class="nc" id="L169">                    return -1;</span>
                }
            }
<span class="nc" id="L172">        }</span>
        
<span class="nc" id="L174">        return maxIndex;</span>
    }
    
    /**
     * Checks if this array initializer is multi-dimensional by examining its elements.
     */
    private boolean checkIfMultiDimensional() {
<span class="nc" id="L181">        return initializerList.getElements().stream().anyMatch(element -&gt;</span>
<span class="nc" id="L182">            element.getInitializer().isListInitializer());</span>
    }
    
    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc" id="L187">        return Collections.singletonList(initializerList);</span>
    }
    
    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L192">        return visitor.visitArrayInitializer(this, context);</span>
    }
    
    @Override
    public String toString() {
<span class="nc" id="L197">        return String.format(&quot;ArrayInitializerNode{elementCount=%d, multiDimensional=%s, location=%s}&quot;, </span>
<span class="nc" id="L198">                           getElementCount(), isMultiDimensional, getSourceLocation());</span>
    }
    
    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L203" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L204" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>
        
<span class="nc" id="L207">        ArrayInitializerNode that = (ArrayInitializerNode) o;</span>
<span class="nc" id="L208">        return Objects.equals(initializerList, that.initializerList);</span>
    }
    
    @Override
    public int hashCode() {
<span class="nc" id="L213">        return Objects.hash(super.hashCode(), initializerList);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>