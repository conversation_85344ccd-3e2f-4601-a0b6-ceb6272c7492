<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InitializerNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">InitializerNode.java</span></div><h1>InitializerNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.base.ExpressionNode;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents an initializer in CAPL.
 *
 * An initializer can be either:
 * 1. A simple assignment expression: `x = 10`
 * 2. A brace-enclosed initializer list: `arr = {1, 2, 3}`
 *
 * Based on CAPL grammar rule:
 * &lt;pre&gt;
 * initializer
 *     : assignmentExpression
 *     | LeftBrace initializerList Comma? RightBrace
 * &lt;/pre&gt;
 *
 * Examples:
 * - `42` (simple expression initializer)
 * - `x + y` (expression initializer)
 * - `{1, 2, 3}` (list initializer)
 * - `{.x = 10, .y = 20}` (designated initializer list)
 */
public class InitializerNode extends AbstractAstNode {

    /**
     * The type of initializer.
     */
<span class="nc" id="L39">    public enum InitializerType {</span>
        /** Simple expression initializer (e.g., `42`, `x + y`) */
<span class="nc" id="L41">        EXPRESSION,</span>
        /** List initializer (e.g., `{1, 2, 3}`) */
<span class="nc" id="L43">        LIST</span>
    }

    private final InitializerType initializerType;
    private final ExpressionNode expression;
    private final InitializerListNode initializerList;

    /**
     * Creates an expression initializer.
     *
     * @param sourceLocation The source location of this initializer
     * @param expression The expression that provides the initial value
     * @throws IllegalArgumentException if sourceLocation or expression is null
     */
    public InitializerNode(SourceLocation sourceLocation, ExpressionNode expression) {
<span class="nc" id="L58">        super(AstNodeType.INITIALIZER, sourceLocation);</span>

<span class="nc bnc" id="L60" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L61">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L63" title="All 2 branches missed.">        if (expression == null) {</span>
<span class="nc" id="L64">            throw new IllegalArgumentException(&quot;Expression cannot be null&quot;);</span>
        }

<span class="nc" id="L67">        this.initializerType = InitializerType.EXPRESSION;</span>
<span class="nc" id="L68">        this.expression = expression;</span>
<span class="nc" id="L69">        this.initializerList = null;</span>
<span class="nc" id="L70">    }</span>

    /**
     * Creates a list initializer.
     *
     * @param sourceLocation The source location of this initializer
     * @param initializerList The list of initializers
     * @throws IllegalArgumentException if sourceLocation or initializerList is null
     */
    public InitializerNode(SourceLocation sourceLocation, InitializerListNode initializerList) {
<span class="nc" id="L80">        super(AstNodeType.INITIALIZER, sourceLocation);</span>

<span class="nc bnc" id="L82" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L83">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L85" title="All 2 branches missed.">        if (initializerList == null) {</span>
<span class="nc" id="L86">            throw new IllegalArgumentException(&quot;Initializer list cannot be null&quot;);</span>
        }

<span class="nc" id="L89">        this.initializerType = InitializerType.LIST;</span>
<span class="nc" id="L90">        this.expression = null;</span>
<span class="nc" id="L91">        this.initializerList = initializerList;</span>
<span class="nc" id="L92">    }</span>

    /**
     * Gets the type of this initializer.
     *
     * @return The initializer type
     */
    public InitializerType getInitializerType() {
<span class="nc" id="L100">        return initializerType;</span>
    }

    /**
     * Gets the expression for expression initializers.
     *
     * @return The expression, or null if this is a list initializer
     */
    public ExpressionNode getExpression() {
<span class="nc" id="L109">        return expression;</span>
    }

    /**
     * Gets the initializer list for list initializers.
     *
     * @return The initializer list, or null if this is an expression initializer
     */
    public InitializerListNode getInitializerList() {
<span class="nc" id="L118">        return initializerList;</span>
    }

    /**
     * Checks if this is an expression initializer.
     *
     * @return true if this is an expression initializer
     */
    public boolean isExpressionInitializer() {
<span class="nc bnc" id="L127" title="All 2 branches missed.">        return initializerType == InitializerType.EXPRESSION;</span>
    }

    /**
     * Checks if this is a list initializer.
     *
     * @return true if this is a list initializer
     */
    public boolean isListInitializer() {
<span class="nc bnc" id="L136" title="All 2 branches missed.">        return initializerType == InitializerType.LIST;</span>
    }

    /**
     * Checks if this initializer is constant (can be evaluated at compile time).
     *
     * @return true if the initializer is constant
     */
    public boolean isConstant() {
<span class="nc bnc" id="L145" title="All 2 branches missed.">        if (isExpressionInitializer()) {</span>
<span class="nc" id="L146">            return expression.isConstant();</span>
        } else {
<span class="nc" id="L148">            return initializerList.isConstant();</span>
        }
    }

    /**
     * Checks if this initializer contains only literal values.
     *
     * @return true if the initializer contains only literals
     */
    public boolean isLiteral() {
<span class="nc bnc" id="L158" title="All 2 branches missed.">        if (isExpressionInitializer()) {</span>
<span class="nc" id="L159">            return expression.isLiteral();</span>
        } else {
<span class="nc" id="L161">            return initializerList.isLiteral();</span>
        }
    }

    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc bnc" id="L167" title="All 2 branches missed.">        if (isExpressionInitializer()) {</span>
<span class="nc" id="L168">            return Collections.singletonList(expression);</span>
        } else {
<span class="nc" id="L170">            return Collections.singletonList(initializerList);</span>
        }
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L176">        return visitor.visitInitializer(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L181">        return String.format(&quot;InitializerNode{type=%s, location=%s}&quot;,</span>
<span class="nc" id="L182">                           initializerType, getSourceLocation());</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L188" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>

<span class="nc" id="L191">        InitializerNode that = (InitializerNode) o;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">        return initializerType == that.initializerType &amp;&amp;</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">               Objects.equals(expression, that.expression) &amp;&amp;</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">               Objects.equals(initializerList, that.initializerList);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L199">        return Objects.hash(super.hashCode(), initializerType, expression, initializerList);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>