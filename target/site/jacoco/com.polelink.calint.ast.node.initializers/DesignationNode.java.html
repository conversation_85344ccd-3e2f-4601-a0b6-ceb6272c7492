<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DesignationNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">DesignationNode.java</span></div><h1>DesignationNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents a designation for an initializer (C99-style).
 *
 * A designation consists of one or more designators followed by an assignment operator.
 * It's used in designated initializers to specify which array element or struct member
 * is being initialized.
 *
 * Based on CAPL grammar rule:
 * &lt;pre&gt;
 * designation
 *     : designatorList Assign
 * &lt;/pre&gt;
 *
 * Examples:
 * - `[0] =` (single array index designator)
 * - `.member =` (single struct member designator)
 * - `[0].member =` (chained designators)
 * - `[1][2] =` (multiple array designators)
 */
public class DesignationNode extends AbstractAstNode {

    private final DesignatorListNode designatorList;

    /**
     * Creates a designation with the given designator list.
     *
     * @param sourceLocation The source location of this designation
     * @param designatorList The list of designators
     * @throws IllegalArgumentException if sourceLocation or designatorList is null
     */
    public DesignationNode(SourceLocation sourceLocation, DesignatorListNode designatorList) {
<span class="nc" id="L44">        super(AstNodeType.DESIGNATED_INITIALIZER, sourceLocation);</span>

<span class="nc bnc" id="L46" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L47">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L49" title="All 2 branches missed.">        if (designatorList == null) {</span>
<span class="nc" id="L50">            throw new IllegalArgumentException(&quot;Designator list cannot be null&quot;);</span>
        }

<span class="nc" id="L53">        this.designatorList = designatorList;</span>
<span class="nc" id="L54">    }</span>

    /**
     * Convenience constructor for a single designator.
     *
     * @param sourceLocation The source location of this designation
     * @param designator The single designator
     * @throws IllegalArgumentException if sourceLocation or designator is null
     */
    public DesignationNode(SourceLocation sourceLocation, DesignatorNode designator) {
<span class="nc" id="L64">        super(AstNodeType.DESIGNATED_INITIALIZER, sourceLocation);</span>

<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L67">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (designator == null) {</span>
<span class="nc" id="L70">            throw new IllegalArgumentException(&quot;Designator cannot be null&quot;);</span>
        }

<span class="nc" id="L73">        this.designatorList = new DesignatorListNode(sourceLocation, designator);</span>
<span class="nc" id="L74">    }</span>

    /**
     * Gets the designator list.
     *
     * @return The designator list
     */
    public DesignatorListNode getDesignatorList() {
<span class="nc" id="L82">        return designatorList;</span>
    }

    /**
     * Gets the list of designators.
     *
     * @return An unmodifiable list of designators
     */
    public List&lt;DesignatorNode&gt; getDesignators() {
<span class="nc" id="L91">        return designatorList.getDesignators();</span>
    }

    /**
     * Gets the number of designators in this designation.
     *
     * @return The number of designators
     */
    public int getDesignatorCount() {
<span class="nc" id="L100">        return designatorList.getDesignatorCount();</span>
    }

    /**
     * Gets the designator at the specified index.
     *
     * @param index The index of the designator to retrieve
     * @return The designator at the specified index
     * @throws IndexOutOfBoundsException if the index is out of range
     */
    public DesignatorNode getDesignator(int index) {
<span class="nc" id="L111">        return designatorList.getDesignator(index);</span>
    }

    /**
     * Gets the first designator.
     *
     * @return The first designator
     */
    public DesignatorNode getFirstDesignator() {
<span class="nc" id="L120">        return designatorList.getFirstDesignator();</span>
    }

    /**
     * Gets the last designator.
     *
     * @return The last designator
     */
    public DesignatorNode getLastDesignator() {
<span class="nc" id="L129">        return designatorList.getLastDesignator();</span>
    }

    /**
     * Checks if this designation has only one designator.
     *
     * @return true if there is only one designator
     */
    public boolean isSingleDesignator() {
<span class="nc" id="L138">        return designatorList.isSingleDesignator();</span>
    }

    /**
     * Checks if this designation has multiple designators (chained).
     *
     * @return true if there are multiple designators
     */
    public boolean isChainedDesignators() {
<span class="nc" id="L147">        return designatorList.isChainedDesignators();</span>
    }

    /**
     * Checks if this designation contains any array designators.
     *
     * @return true if at least one designator is an array designator
     */
    public boolean hasArrayDesignator() {
<span class="nc" id="L156">        return designatorList.hasArrayDesignator();</span>
    }

    /**
     * Checks if this designation contains any member designators.
     *
     * @return true if at least one designator is a member designator
     */
    public boolean hasMemberDesignator() {
<span class="nc" id="L165">        return designatorList.hasMemberDesignator();</span>
    }

    /**
     * Checks if all designators are array designators.
     *
     * @return true if all designators are array designators
     */
    public boolean isAllArrayDesignators() {
<span class="nc" id="L174">        return designatorList.isAllArrayDesignators();</span>
    }

    /**
     * Checks if all designators are member designators.
     *
     * @return true if all designators are member designators
     */
    public boolean isAllMemberDesignators() {
<span class="nc" id="L183">        return designatorList.isAllMemberDesignators();</span>
    }

    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc" id="L188">        return Collections.singletonList(designatorList);</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L193">        return visitor.visitDesignation(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L198">        return String.format(&quot;DesignationNode{designatorCount=%d, location=%s}&quot;,</span>
<span class="nc" id="L199">                           getDesignatorCount(), getSourceLocation());</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L205" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>

<span class="nc" id="L208">        DesignationNode that = (DesignationNode) o;</span>
<span class="nc" id="L209">        return Objects.equals(designatorList, that.designatorList);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L214">        return Objects.hash(super.hashCode(), designatorList);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>