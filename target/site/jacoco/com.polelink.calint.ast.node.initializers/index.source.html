<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.ast.node.initializers</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.ast.node.initializers</span></div><h1>com.polelink.calint.ast.node.initializers</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,448 of 1,448</td><td class="ctr2">0%</td><td class="bar">194 of 194</td><td class="ctr2">0%</td><td class="ctr1">223</td><td class="ctr2">223</td><td class="ctr1">280</td><td class="ctr2">280</td><td class="ctr1">126</td><td class="ctr2">126</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="DesignatorListNode.java.html" class="el_source">DesignatorListNode.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="233" alt="233"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="26" alt="26"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">36</td><td class="ctr2" id="g0">36</td><td class="ctr1" id="h3">42</td><td class="ctr2" id="i3">42</td><td class="ctr1" id="j0">23</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="DesignatorNode.java.html" class="el_source">DesignatorNode.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="229" alt="229"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">33</td><td class="ctr2" id="g2">33</td><td class="ctr1" id="h1">44</td><td class="ctr2" id="i1">44</td><td class="ctr1" id="j4">15</td><td class="ctr2" id="k4">15</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a0"><a href="ArrayInitializerNode.java.html" class="el_source">ArrayInitializerNode.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="113" height="10" title="221" alt="221"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="28" alt="28"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">34</td><td class="ctr2" id="g1">34</td><td class="ctr1" id="h2">43</td><td class="ctr2" id="i2">43</td><td class="ctr1" id="j1">20</td><td class="ctr2" id="k1">20</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a6"><a href="InitializerNode.java.html" class="el_source">InitializerNode.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="218" alt="218"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="32" alt="32"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">31</td><td class="ctr2" id="g3">31</td><td class="ctr1" id="h0">46</td><td class="ctr2" id="i0">46</td><td class="ctr1" id="j5">15</td><td class="ctr2" id="k5">15</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a4"><a href="InitializerElementNode.java.html" class="el_source">InitializerElementNode.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="99" height="10" title="193" alt="193"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="32" alt="32"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">31</td><td class="ctr2" id="g4">31</td><td class="ctr1" id="h4">37</td><td class="ctr2" id="i4">37</td><td class="ctr1" id="j6">15</td><td class="ctr2" id="k6">15</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a5"><a href="InitializerListNode.java.html" class="el_source">InitializerListNode.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="189" alt="189"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="24" alt="24"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">31</td><td class="ctr2" id="g5">31</td><td class="ctr1" id="h6">32</td><td class="ctr2" id="i6">32</td><td class="ctr1" id="j2">19</td><td class="ctr2" id="k2">19</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a1"><a href="DesignationNode.java.html" class="el_source">DesignationNode.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="165" alt="165"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="16" alt="16"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">27</td><td class="ctr2" id="g6">27</td><td class="ctr1" id="h5">36</td><td class="ctr2" id="i5">36</td><td class="ctr1" id="j3">19</td><td class="ctr2" id="k3">19</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>