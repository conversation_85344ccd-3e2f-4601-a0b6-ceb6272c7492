<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InitializerListNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">InitializerListNode.java</span></div><h1>InitializerListNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents an initializer list in CAPL.
 *
 * An initializer list is a comma-separated list of initializers, optionally with designators.
 * It's used for initializing aggregates like arrays or structs.
 *
 * Based on CAPL grammar rule:
 * &lt;pre&gt;
 * initializerList
 *     : designation? initializer (Comma designation? initializer)*
 * &lt;/pre&gt;
 *
 * Examples:
 * - `{1, 2, 3}` (simple list)
 * - `{[0] = 1, [1] = 5}` (with array designators)
 * - `{.x = 10, .y = 20}` (with struct member designators)
 * - `{1, .y = 20, 3}` (mixed designated and non-designated)
 */
public class InitializerListNode extends AbstractAstNode {

    private final List&lt;InitializerElementNode&gt; elements;

    /**
     * Creates an initializer list with the given elements.
     *
     * @param sourceLocation The source location of this initializer list
     * @param elements The list of initializer elements
     * @throws IllegalArgumentException if sourceLocation is null or elements is null/empty
     */
    public InitializerListNode(SourceLocation sourceLocation, List&lt;InitializerElementNode&gt; elements) {
<span class="nc" id="L44">        super(AstNodeType.INITIALIZER_LIST, sourceLocation);</span>

<span class="nc bnc" id="L46" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L47">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L49" title="All 2 branches missed.">        if (elements == null) {</span>
<span class="nc" id="L50">            throw new IllegalArgumentException(&quot;Elements list cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L52" title="All 2 branches missed.">        if (elements.isEmpty()) {</span>
<span class="nc" id="L53">            throw new IllegalArgumentException(&quot;Elements list cannot be empty&quot;);</span>
        }

<span class="nc" id="L56">        this.elements = Collections.unmodifiableList(new ArrayList&lt;&gt;(elements));</span>
<span class="nc" id="L57">    }</span>

    /**
     * Gets the list of initializer elements.
     *
     * @return An unmodifiable list of initializer elements
     */
    public List&lt;InitializerElementNode&gt; getElements() {
<span class="nc" id="L65">        return elements;</span>
    }

    /**
     * Gets the number of elements in this initializer list.
     *
     * @return The number of elements
     */
    public int getElementCount() {
<span class="nc" id="L74">        return elements.size();</span>
    }

    /**
     * Gets the element at the specified index.
     *
     * @param index The index of the element to retrieve
     * @return The element at the specified index
     * @throws IndexOutOfBoundsException if the index is out of range
     */
    public InitializerElementNode getElement(int index) {
<span class="nc" id="L85">        return elements.get(index);</span>
    }

    /**
     * Checks if this initializer list is empty.
     *
     * @return true if the list has no elements (should not happen due to constructor validation)
     */
    public boolean isEmpty() {
<span class="nc" id="L94">        return elements.isEmpty();</span>
    }

    /**
     * Checks if all elements in this list are designated (have designators).
     *
     * @return true if all elements are designated
     */
    public boolean areAllElementsDesignated() {
<span class="nc" id="L103">        return elements.stream().allMatch(InitializerElementNode::isDesignated);</span>
    }

    /**
     * Checks if any elements in this list are designated.
     *
     * @return true if at least one element is designated
     */
    public boolean hasDesignatedElements() {
<span class="nc" id="L112">        return elements.stream().anyMatch(InitializerElementNode::isDesignated);</span>
    }

    /**
     * Checks if this initializer list is constant (all elements are constant).
     *
     * @return true if all elements are constant
     */
    public boolean isConstant() {
<span class="nc" id="L121">        return elements.stream().allMatch(InitializerElementNode::isConstant);</span>
    }

    /**
     * Checks if this initializer list contains only literal values.
     *
     * @return true if all elements are literals
     */
    public boolean isLiteral() {
<span class="nc" id="L130">        return elements.stream().allMatch(InitializerElementNode::isLiteral);</span>
    }

    /**
     * Checks if this is a simple array initializer (no designators).
     *
     * @return true if this is a simple array initializer
     */
    public boolean isSimpleArrayInitializer() {
<span class="nc bnc" id="L139" title="All 2 branches missed.">        return !hasDesignatedElements();</span>
    }

    /**
     * Checks if this is a struct initializer (has member designators).
     *
     * @return true if this appears to be a struct initializer
     */
    public boolean isStructInitializer() {
<span class="nc" id="L148">        return elements.stream().anyMatch(element -&gt;</span>
<span class="nc bnc" id="L149" title="All 4 branches missed.">            element.isDesignated() &amp;&amp; element.getDesignation().hasMemberDesignator());</span>
    }

    /**
     * Checks if this is an array initializer (has array index designators).
     *
     * @return true if this appears to be an array initializer
     */
    public boolean isArrayInitializer() {
<span class="nc" id="L158">        return elements.stream().anyMatch(element -&gt;</span>
<span class="nc bnc" id="L159" title="All 4 branches missed.">            element.isDesignated() &amp;&amp; element.getDesignation().hasArrayDesignator());</span>
    }

    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc" id="L164">        return Collections.unmodifiableList(new ArrayList&lt;&gt;(elements));</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L169">        return visitor.visitInitializerList(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L174">        return String.format(&quot;InitializerListNode{elementCount=%d, location=%s}&quot;,</span>
<span class="nc" id="L175">                           elements.size(), getSourceLocation());</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L180" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L181" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L182" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>

<span class="nc" id="L184">        InitializerListNode that = (InitializerListNode) o;</span>
<span class="nc" id="L185">        return Objects.equals(elements, that.elements);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L190">        return Objects.hash(super.hashCode(), elements);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>