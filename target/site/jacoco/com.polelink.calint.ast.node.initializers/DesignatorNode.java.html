<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DesignatorNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">DesignatorNode.java</span></div><h1>DesignatorNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.base.ExpressionNode;
import com.polelink.calint.ast.node.expressions.literals.IdentifierNode;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents a designator, used in C99-style designated initializers.
 *
 * A designator specifies an array index or a struct/union member.
 *
 * Based on CAPL grammar rule:
 * &lt;pre&gt;
 * designator
 *     : LeftBracket constantExpression RightBracket
 *     | Dot Identifier
 * &lt;/pre&gt;
 *
 * Examples:
 * - `[0]` (array index designator)
 * - `[MAX_SIZE - 1]` (array index with expression)
 * - `.member` (struct/union member designator)
 * - `.x` (struct member designator)
 */
public class DesignatorNode extends AbstractAstNode {

    /**
     * The type of designator.
     */
<span class="nc" id="L38">    public enum DesignatorType {</span>
        /** Array index designator (e.g., [0], [i+1]) */
<span class="nc" id="L40">        ARRAY_INDEX,</span>
        /** Struct/union member designator (e.g., .member, .x) */
<span class="nc" id="L42">        MEMBER</span>
    }

    private final DesignatorType designatorType;
    private final ExpressionNode arrayIndex;
    private final IdentifierNode memberName;

    /**
     * Creates an array index designator.
     *
     * @param sourceLocation The source location of this designator
     * @param arrayIndex The expression for the array index
     * @throws IllegalArgumentException if sourceLocation or arrayIndex is null
     */
    public DesignatorNode(SourceLocation sourceLocation, ExpressionNode arrayIndex) {
<span class="nc" id="L57">        super(AstNodeType.DESIGNATOR, sourceLocation);</span>

<span class="nc bnc" id="L59" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L60">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (arrayIndex == null) {</span>
<span class="nc" id="L63">            throw new IllegalArgumentException(&quot;Array index expression cannot be null&quot;);</span>
        }

<span class="nc" id="L66">        this.designatorType = DesignatorType.ARRAY_INDEX;</span>
<span class="nc" id="L67">        this.arrayIndex = arrayIndex;</span>
<span class="nc" id="L68">        this.memberName = null;</span>
<span class="nc" id="L69">    }</span>

    /**
     * Creates a member designator.
     *
     * @param sourceLocation The source location of this designator
     * @param memberName The identifier for the member name
     * @throws IllegalArgumentException if sourceLocation or memberName is null
     */
    public DesignatorNode(SourceLocation sourceLocation, IdentifierNode memberName) {
<span class="nc" id="L79">        super(AstNodeType.DESIGNATOR, sourceLocation);</span>

<span class="nc bnc" id="L81" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L82">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L84" title="All 2 branches missed.">        if (memberName == null) {</span>
<span class="nc" id="L85">            throw new IllegalArgumentException(&quot;Member name cannot be null&quot;);</span>
        }

<span class="nc" id="L88">        this.designatorType = DesignatorType.MEMBER;</span>
<span class="nc" id="L89">        this.arrayIndex = null;</span>
<span class="nc" id="L90">        this.memberName = memberName;</span>
<span class="nc" id="L91">    }</span>

    /**
     * Gets the type of this designator.
     *
     * @return The designator type
     */
    public DesignatorType getDesignatorType() {
<span class="nc" id="L99">        return designatorType;</span>
    }

    /**
     * Gets the array index expression for array designators.
     *
     * @return The array index expression, or null if this is a member designator
     */
    public ExpressionNode getArrayIndex() {
<span class="nc" id="L108">        return arrayIndex;</span>
    }

    /**
     * Gets the member name for member designators.
     *
     * @return The member name, or null if this is an array designator
     */
    public IdentifierNode getMemberName() {
<span class="nc" id="L117">        return memberName;</span>
    }

    /**
     * Checks if this is an array index designator.
     *
     * @return true if this is an array index designator
     */
    public boolean isArrayDesignator() {
<span class="nc bnc" id="L126" title="All 2 branches missed.">        return designatorType == DesignatorType.ARRAY_INDEX;</span>
    }

    /**
     * Checks if this is a member designator.
     *
     * @return true if this is a member designator
     */
    public boolean isMemberDesignator() {
<span class="nc bnc" id="L135" title="All 2 branches missed.">        return designatorType == DesignatorType.MEMBER;</span>
    }

    /**
     * Checks if this designator uses a constant index (for array designators).
     *
     * @return true if this is an array designator with a constant index
     */
    public boolean hasConstantIndex() {
<span class="nc bnc" id="L144" title="All 4 branches missed.">        return isArrayDesignator() &amp;&amp; arrayIndex.isConstant();</span>
    }

    /**
     * Gets the member name as a string (for member designators).
     *
     * @return The member name as a string, or null if this is an array designator
     */
    public String getMemberNameString() {
<span class="nc bnc" id="L153" title="All 2 branches missed.">        return isMemberDesignator() ? memberName.getName() : null;</span>
    }

    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc bnc" id="L158" title="All 2 branches missed.">        if (isArrayDesignator()) {</span>
<span class="nc" id="L159">            return Collections.singletonList(arrayIndex);</span>
        } else {
<span class="nc" id="L161">            return Collections.singletonList(memberName);</span>
        }
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L167">        return visitor.visitDesignator(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc bnc" id="L172" title="All 2 branches missed.">        if (isArrayDesignator()) {</span>
<span class="nc" id="L173">            return String.format(&quot;DesignatorNode{type=ARRAY_INDEX, location=%s}&quot;, getSourceLocation());</span>
        } else {
<span class="nc" id="L175">            return String.format(&quot;DesignatorNode{type=MEMBER, member=%s, location=%s}&quot;,</span>
<span class="nc" id="L176">                               getMemberNameString(), getSourceLocation());</span>
        }
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L182" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L183" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L184" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>

<span class="nc" id="L186">        DesignatorNode that = (DesignatorNode) o;</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">        return designatorType == that.designatorType &amp;&amp;</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">               Objects.equals(arrayIndex, that.arrayIndex) &amp;&amp;</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">               Objects.equals(memberName, that.memberName);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L194">        return Objects.hash(super.hashCode(), designatorType, arrayIndex, memberName);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>