<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InitializerElementNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">InitializerElementNode.java</span></div><h1>InitializerElementNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.util.SourceLocation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Represents a single element within an initializer list.
 * 
 * An initializer element can be either:
 * 1. A simple initializer: `42` or `{1, 2}`
 * 2. A designated initializer: `[0] = 42` or `.member = value`
 * 
 * Based on CAPL grammar pattern:
 * &lt;pre&gt;
 * designation? initializer
 * &lt;/pre&gt;
 * 
 * Examples:
 * - `42` (simple element)
 * - `[0] = 42` (array designated element)
 * - `.x = 10` (struct member designated element)
 * - `{1, 2}` (nested initializer list element)
 */
public class InitializerElementNode extends AbstractAstNode {
    
    private final DesignationNode designation;
    private final InitializerNode initializer;
    
    /**
     * Creates a simple (non-designated) initializer element.
     * 
     * @param sourceLocation The source location of this element
     * @param initializer The initializer for this element
     * @throws IllegalArgumentException if sourceLocation or initializer is null
     */
    public InitializerElementNode(SourceLocation sourceLocation, InitializerNode initializer) {
<span class="nc" id="L45">        super(AstNodeType.INITIALIZER_ELEMENT, sourceLocation);</span>
        
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (initializer == null) {</span>
<span class="nc" id="L48">            throw new IllegalArgumentException(&quot;Initializer cannot be null&quot;);</span>
        }
        
<span class="nc" id="L51">        this.designation = null;</span>
<span class="nc" id="L52">        this.initializer = initializer;</span>
<span class="nc" id="L53">    }</span>
    
    /**
     * Creates a designated initializer element.
     * 
     * @param sourceLocation The source location of this element
     * @param designation The designation (e.g., [0] or .member)
     * @param initializer The initializer for this element
     * @throws IllegalArgumentException if sourceLocation, designation, or initializer is null
     */
    public InitializerElementNode(SourceLocation sourceLocation, DesignationNode designation, InitializerNode initializer) {
<span class="nc" id="L64">        super(AstNodeType.INITIALIZER_ELEMENT, sourceLocation);</span>
        
<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (designation == null) {</span>
<span class="nc" id="L67">            throw new IllegalArgumentException(&quot;Designation cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (initializer == null) {</span>
<span class="nc" id="L70">            throw new IllegalArgumentException(&quot;Initializer cannot be null&quot;);</span>
        }
        
<span class="nc" id="L73">        this.designation = designation;</span>
<span class="nc" id="L74">        this.initializer = initializer;</span>
<span class="nc" id="L75">    }</span>
    
    /**
     * Gets the designation for this element.
     * 
     * @return The designation, or null if this is a simple element
     */
    public DesignationNode getDesignation() {
<span class="nc" id="L83">        return designation;</span>
    }
    
    /**
     * Gets the initializer for this element.
     * 
     * @return The initializer
     */
    public InitializerNode getInitializer() {
<span class="nc" id="L92">        return initializer;</span>
    }
    
    /**
     * Checks if this element is designated (has a designator).
     * 
     * @return true if this element has a designation
     */
    public boolean isDesignated() {
<span class="nc bnc" id="L101" title="All 2 branches missed.">        return designation != null;</span>
    }
    
    /**
     * Checks if this element is a simple (non-designated) element.
     * 
     * @return true if this element has no designation
     */
    public boolean isSimple() {
<span class="nc bnc" id="L110" title="All 2 branches missed.">        return designation == null;</span>
    }
    
    /**
     * Checks if this element is constant (can be evaluated at compile time).
     * 
     * @return true if the element is constant
     */
    public boolean isConstant() {
<span class="nc" id="L119">        return initializer.isConstant();</span>
    }
    
    /**
     * Checks if this element contains only literal values.
     * 
     * @return true if the element contains only literals
     */
    public boolean isLiteral() {
<span class="nc" id="L128">        return initializer.isLiteral();</span>
    }
    
    /**
     * Checks if this is an array designated element (e.g., [0] = value).
     * 
     * @return true if this is an array designated element
     */
    public boolean isArrayDesignated() {
<span class="nc bnc" id="L137" title="All 4 branches missed.">        return isDesignated() &amp;&amp; designation.hasArrayDesignator();</span>
    }
    
    /**
     * Checks if this is a struct member designated element (e.g., .member = value).
     * 
     * @return true if this is a struct member designated element
     */
    public boolean isMemberDesignated() {
<span class="nc bnc" id="L146" title="All 4 branches missed.">        return isDesignated() &amp;&amp; designation.hasMemberDesignator();</span>
    }
    
    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc" id="L151">        List&lt;AstNode&gt; children = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">        if (designation != null) {</span>
<span class="nc" id="L153">            children.add(designation);</span>
        }
<span class="nc" id="L155">        children.add(initializer);</span>
<span class="nc" id="L156">        return Collections.unmodifiableList(children);</span>
    }
    
    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L161">        return visitor.visitInitializerElement(this, context);</span>
    }
    
    @Override
    public String toString() {
<span class="nc" id="L166">        return String.format(&quot;InitializerElementNode{designated=%s, location=%s}&quot;, </span>
<span class="nc" id="L167">                           isDesignated(), getSourceLocation());</span>
    }
    
    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L172" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L173" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>
        
<span class="nc" id="L176">        InitializerElementNode that = (InitializerElementNode) o;</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">        return Objects.equals(designation, that.designation) &amp;&amp;</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">               Objects.equals(initializer, that.initializer);</span>
    }
    
    @Override
    public int hashCode() {
<span class="nc" id="L183">        return Objects.hash(super.hashCode(), designation, initializer);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>