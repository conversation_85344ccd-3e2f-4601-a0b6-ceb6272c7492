<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DesignatorListNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.initializers</a> &gt; <span class="el_source">DesignatorListNode.java</span></div><h1>DesignatorListNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.initializers;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.util.SourceLocation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Represents a list of designators in CAPL.
 * 
 * A designator list is a sequence of designators that specify a path to a particular
 * element in a nested structure or array. This is used in C99-style designated initializers.
 * 
 * Based on CAPL grammar rule:
 * &lt;pre&gt;
 * designatorList
 *     : designator+
 * &lt;/pre&gt;
 * 
 * Examples:
 * - `[0]` (single array designator)
 * - `.member` (single struct member designator)
 * - `[0].member` (chained: array element's member)
 * - `[1][2]` (multi-dimensional array access)
 * - `.outer.inner` (nested struct members)
 */
public class DesignatorListNode extends AbstractAstNode {
    
    private final List&lt;DesignatorNode&gt; designators;
    
    /**
     * Creates a designator list with the given designators.
     * 
     * @param sourceLocation The source location of this designator list
     * @param designators The list of designators
     * @throws IllegalArgumentException if sourceLocation is null or designators is null/empty
     */
    public DesignatorListNode(SourceLocation sourceLocation, List&lt;DesignatorNode&gt; designators) {
<span class="nc" id="L45">        super(AstNodeType.DESIGNATOR_LIST, sourceLocation);</span>
        
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L48">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (designators == null) {</span>
<span class="nc" id="L51">            throw new IllegalArgumentException(&quot;Designators list cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (designators.isEmpty()) {</span>
<span class="nc" id="L54">            throw new IllegalArgumentException(&quot;Designators list cannot be empty&quot;);</span>
        }
        
<span class="nc" id="L57">        this.designators = Collections.unmodifiableList(new ArrayList&lt;&gt;(designators));</span>
<span class="nc" id="L58">    }</span>
    
    /**
     * Convenience constructor for a single designator.
     * 
     * @param sourceLocation The source location of this designator list
     * @param designator The single designator
     * @throws IllegalArgumentException if sourceLocation or designator is null
     */
    public DesignatorListNode(SourceLocation sourceLocation, DesignatorNode designator) {
<span class="nc" id="L68">        super(AstNodeType.DESIGNATOR_LIST, sourceLocation);</span>
        
<span class="nc bnc" id="L70" title="All 2 branches missed.">        if (sourceLocation == null) {</span>
<span class="nc" id="L71">            throw new IllegalArgumentException(&quot;Source location cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L73" title="All 2 branches missed.">        if (designator == null) {</span>
<span class="nc" id="L74">            throw new IllegalArgumentException(&quot;Designator cannot be null&quot;);</span>
        }
        
<span class="nc" id="L77">        this.designators = Collections.singletonList(designator);</span>
<span class="nc" id="L78">    }</span>
    
    /**
     * Gets the list of designators.
     * 
     * @return An unmodifiable list of designators
     */
    public List&lt;DesignatorNode&gt; getDesignators() {
<span class="nc" id="L86">        return designators;</span>
    }
    
    /**
     * Gets the number of designators in this list.
     * 
     * @return The number of designators
     */
    public int getDesignatorCount() {
<span class="nc" id="L95">        return designators.size();</span>
    }
    
    /**
     * Gets the designator at the specified index.
     * 
     * @param index The index of the designator to retrieve
     * @return The designator at the specified index
     * @throws IndexOutOfBoundsException if the index is out of range
     */
    public DesignatorNode getDesignator(int index) {
<span class="nc" id="L106">        return designators.get(index);</span>
    }
    
    /**
     * Gets the first designator.
     * 
     * @return The first designator
     */
    public DesignatorNode getFirstDesignator() {
<span class="nc" id="L115">        return designators.get(0);</span>
    }
    
    /**
     * Gets the last designator.
     * 
     * @return The last designator
     */
    public DesignatorNode getLastDesignator() {
<span class="nc" id="L124">        return designators.get(designators.size() - 1);</span>
    }
    
    /**
     * Checks if this list has only one designator.
     * 
     * @return true if there is only one designator
     */
    public boolean isSingleDesignator() {
<span class="nc bnc" id="L133" title="All 2 branches missed.">        return designators.size() == 1;</span>
    }
    
    /**
     * Checks if this list has multiple designators (chained).
     * 
     * @return true if there are multiple designators
     */
    public boolean isChainedDesignators() {
<span class="nc bnc" id="L142" title="All 2 branches missed.">        return designators.size() &gt; 1;</span>
    }
    
    /**
     * Checks if this list contains any array designators.
     * 
     * @return true if at least one designator is an array designator
     */
    public boolean hasArrayDesignator() {
<span class="nc" id="L151">        return designators.stream().anyMatch(DesignatorNode::isArrayDesignator);</span>
    }
    
    /**
     * Checks if this list contains any member designators.
     * 
     * @return true if at least one designator is a member designator
     */
    public boolean hasMemberDesignator() {
<span class="nc" id="L160">        return designators.stream().anyMatch(DesignatorNode::isMemberDesignator);</span>
    }
    
    /**
     * Checks if all designators are array designators.
     * 
     * @return true if all designators are array designators
     */
    public boolean isAllArrayDesignators() {
<span class="nc" id="L169">        return designators.stream().allMatch(DesignatorNode::isArrayDesignator);</span>
    }
    
    /**
     * Checks if all designators are member designators.
     * 
     * @return true if all designators are member designators
     */
    public boolean isAllMemberDesignators() {
<span class="nc" id="L178">        return designators.stream().allMatch(DesignatorNode::isMemberDesignator);</span>
    }
    
    /**
     * Checks if this represents a simple array access pattern (all array designators).
     * 
     * @return true if this is a simple array access pattern
     */
    public boolean isSimpleArrayAccess() {
<span class="nc" id="L187">        return isAllArrayDesignators();</span>
    }
    
    /**
     * Checks if this represents a simple member access pattern (all member designators).
     * 
     * @return true if this is a simple member access pattern
     */
    public boolean isSimpleMemberAccess() {
<span class="nc" id="L196">        return isAllMemberDesignators();</span>
    }
    
    /**
     * Checks if this represents a mixed access pattern (both array and member designators).
     * 
     * @return true if this is a mixed access pattern
     */
    public boolean isMixedAccess() {
<span class="nc bnc" id="L205" title="All 4 branches missed.">        return hasArrayDesignator() &amp;&amp; hasMemberDesignator();</span>
    }
    
    /**
     * Gets the depth of array access (number of array designators).
     * 
     * @return The number of array designators
     */
    public int getArrayAccessDepth() {
<span class="nc" id="L214">        return (int) designators.stream().filter(DesignatorNode::isArrayDesignator).count();</span>
    }
    
    /**
     * Gets the depth of member access (number of member designators).
     * 
     * @return The number of member designators
     */
    public int getMemberAccessDepth() {
<span class="nc" id="L223">        return (int) designators.stream().filter(DesignatorNode::isMemberDesignator).count();</span>
    }
    
    @Override
    public List&lt;AstNode&gt; getChildren() {
<span class="nc" id="L228">        return Collections.unmodifiableList(new ArrayList&lt;&gt;(designators));</span>
    }
    
    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L233">        return visitor.visitDesignatorList(this, context);</span>
    }
    
    @Override
    public String toString() {
<span class="nc" id="L238">        return String.format(&quot;DesignatorListNode{designatorCount=%d, location=%s}&quot;, </span>
<span class="nc" id="L239">                           designators.size(), getSourceLocation());</span>
    }
    
    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L244" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L245" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">        if (!super.equals(o)) return false;</span>
        
<span class="nc" id="L248">        DesignatorListNode that = (DesignatorListNode) o;</span>
<span class="nc" id="L249">        return Objects.equals(designators, that.designators);</span>
    }
    
    @Override
    public int hashCode() {
<span class="nc" id="L254">        return Objects.hash(super.hashCode(), designators);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>