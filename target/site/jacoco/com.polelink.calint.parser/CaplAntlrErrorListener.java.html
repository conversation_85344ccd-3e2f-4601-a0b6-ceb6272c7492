<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplAntlrErrorListener.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser</a> &gt; <span class="el_source">CaplAntlrErrorListener.java</span></div><h1>CaplAntlrErrorListener.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.FailedPredicateException;
import org.antlr.v4.runtime.InputMismatchException;
import org.antlr.v4.runtime.LexerNoViableAltException;
import org.antlr.v4.runtime.NoViableAltException;
import org.antlr.v4.runtime.Parser;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;
import org.antlr.v4.runtime.Token;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.util.SourceLocation;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.parser.error.analyzer.ErrorAnalyzerRegistry;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.context.ContextDetector;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.processor.LexerErrorProcessor;
import com.polelink.calint.parser.error.processor.ParserErrorProcessor;

/**
 * Enhanced ANTLR error listener for CAPL language parsing.
 *
 * This listener provides comprehensive error handling for both lexical and syntax analysis phases,
 * with CAPL-specific error recognition and user-friendly error messages.
 *
 * Features:
 * - Detailed error classification (lexer vs parser errors)
 * - CAPL-specific error context analysis
 * - Internationalized error messages
 * - Code context extraction for better debugging
 * - Comprehensive error recovery information
 * - Integration with new error analysis framework
 */
public class CaplAntlrErrorListener extends BaseErrorListener {

<span class="nc" id="L49">    private static final Logger LOGGER = LoggerFactory.getLogger(CaplAntlrErrorListener.class);</span>

<span class="nc" id="L51">    private final List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>
    private final SourceFile sourceFile;

    // New error analysis components
    private final ErrorAnalyzerRegistry analyzerRegistry;
    private final ContextDetector contextDetector;
    private final LexerErrorProcessor lexerProcessor;
    private final ParserErrorProcessor parserProcessor;

    // Flag to enable/disable new error analysis framework
    private final boolean useNewFramework;

    /**
     * Constructor with default behavior (uses legacy error handling).
     *
     * @param sourceFile the source file being parsed
     */
    public CaplAntlrErrorListener(SourceFile sourceFile) {
<span class="nc" id="L69">        this(sourceFile, null, null, null, null, false);</span>
<span class="nc" id="L70">    }</span>

    /**
     * Constructor with new error analysis framework.
     *
     * @param sourceFile the source file being parsed
     * @param analyzerRegistry the error analyzer registry
     * @param contextDetector the context detector
     */
    public CaplAntlrErrorListener(SourceFile sourceFile, ErrorAnalyzerRegistry analyzerRegistry, ContextDetector contextDetector) {
<span class="nc" id="L80">        this(sourceFile, analyzerRegistry, contextDetector,</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">             analyzerRegistry != null ? new LexerErrorProcessor(analyzerRegistry) : null,</span>
<span class="nc bnc" id="L82" title="All 4 branches missed.">             analyzerRegistry != null &amp;&amp; contextDetector != null ? new ParserErrorProcessor(analyzerRegistry, contextDetector) : null,</span>
             true);
<span class="nc" id="L84">    }</span>

    /**
     * Full constructor with framework control.
     *
     * @param sourceFile the source file being parsed
     * @param analyzerRegistry the error analyzer registry
     * @param contextDetector the context detector
     * @param lexerProcessor the lexer error processor
     * @param parserProcessor the parser error processor
     * @param useNewFramework whether to use the new error analysis framework
     */
    private CaplAntlrErrorListener(SourceFile sourceFile, ErrorAnalyzerRegistry analyzerRegistry,
                                  ContextDetector contextDetector, LexerErrorProcessor lexerProcessor,
<span class="nc" id="L98">                                  ParserErrorProcessor parserProcessor, boolean useNewFramework) {</span>
<span class="nc" id="L99">        this.sourceFile = sourceFile;</span>
<span class="nc" id="L100">        this.analyzerRegistry = analyzerRegistry;</span>
<span class="nc" id="L101">        this.contextDetector = contextDetector;</span>
<span class="nc" id="L102">        this.lexerProcessor = lexerProcessor;</span>
<span class="nc" id="L103">        this.parserProcessor = parserProcessor;</span>
<span class="nc bnc" id="L104" title="All 10 branches missed.">        this.useNewFramework = useNewFramework &amp;&amp; analyzerRegistry != null &amp;&amp; contextDetector != null</span>
                              &amp;&amp; lexerProcessor != null &amp;&amp; parserProcessor != null;

<span class="nc" id="L107">        LOGGER.debug(&quot;CaplAntlrErrorListener initialized with new framework: {}&quot;, this.useNewFramework);</span>
<span class="nc" id="L108">    }</span>

    @Override
    public void syntaxError(Recognizer&lt;?, ?&gt; recognizer, Object offendingSymbol, int line, int charPositionInLine, String originalAntlrMsg, RecognitionException e) {

<span class="nc bnc" id="L113" title="All 2 branches missed.">        if (useNewFramework) {</span>
<span class="nc" id="L114">            handleErrorWithNewFramework(recognizer, offendingSymbol, line, charPositionInLine, originalAntlrMsg, e);</span>
        } else {
<span class="nc" id="L116">            handleErrorWithLegacyFramework(recognizer, offendingSymbol, line, charPositionInLine, originalAntlrMsg, e);</span>
        }
<span class="nc" id="L118">    }</span>

    /**
     * Handles errors using the new error analysis framework.
     */
    private void handleErrorWithNewFramework(Recognizer&lt;?, ?&gt; recognizer, Object offendingSymbol, int line, int charPositionInLine, String originalAntlrMsg, RecognitionException e) {
        try {
<span class="nc" id="L125">            LOGGER.debug(&quot;Processing error with new framework: line={}, column={}&quot;, line, charPositionInLine);</span>

            // Create error context with enhanced information
<span class="nc" id="L128">            ErrorContext context = createEnhancedErrorContext(recognizer, offendingSymbol, line, charPositionInLine, originalAntlrMsg, e);</span>

            // Process error based on recognizer type
<span class="nc" id="L131">            ErrorResult result = null;</span>
<span class="nc bnc" id="L132" title="All 4 branches missed.">            if (isLexerError(recognizer) &amp;&amp; lexerProcessor != null) {</span>
<span class="nc" id="L133">                LOGGER.debug(&quot;Processing lexer error with LexerErrorProcessor&quot;);</span>
<span class="nc" id="L134">                result = lexerProcessor.processLexerError(context);</span>
<span class="nc bnc" id="L135" title="All 4 branches missed.">            } else if (isParserError(recognizer) &amp;&amp; parserProcessor != null) {</span>
<span class="nc" id="L136">                LOGGER.debug(&quot;Processing parser error with ParserErrorProcessor&quot;);</span>
<span class="nc" id="L137">                result = parserProcessor.processParserError(context);</span>
            }

            // Convert result to issue if successful
<span class="nc bnc" id="L141" title="All 2 branches missed.">            if (result != null) {</span>
<span class="nc" id="L142">                IssueObject issue = convertErrorResultToIssue(result, context);</span>
<span class="nc" id="L143">                issues.add(issue);</span>
<span class="nc" id="L144">                LOGGER.debug(&quot;Successfully processed error with new framework: {}&quot;, result.getRuleId());</span>
<span class="nc" id="L145">                return;</span>
            }

            // Fallback to legacy handling if no result
<span class="nc" id="L149">            LOGGER.debug(&quot;No result from new framework, falling back to legacy handling&quot;);</span>
<span class="nc" id="L150">            handleErrorWithLegacyFramework(recognizer, offendingSymbol, line, charPositionInLine, originalAntlrMsg, e);</span>

<span class="nc" id="L152">        } catch (Exception ex) {</span>
            // Provide detailed context for debugging while maintaining graceful fallback
<span class="nc" id="L154">            LOGGER.warn(&quot;Error in new framework, falling back to legacy. Error: {} at line {}, column {}. &quot; +</span>
                       &quot;Recognizer: {}, Symbol: {}&quot;,
<span class="nc" id="L156">                       ex.getMessage(),</span>
<span class="nc" id="L157">                       line,</span>
<span class="nc" id="L158">                       charPositionInLine,</span>
<span class="nc bnc" id="L159" title="All 2 branches missed.">                       recognizer != null ? recognizer.getClass().getSimpleName() : &quot;null&quot;,</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">                       offendingSymbol != null ? offendingSymbol.getClass().getSimpleName() : &quot;null&quot;);</span>

            // Log stack trace at DEBUG level for detailed troubleshooting
<span class="nc" id="L163">            LOGGER.debug(&quot;New framework error details:&quot;, ex);</span>

<span class="nc" id="L165">            handleErrorWithLegacyFramework(recognizer, offendingSymbol, line, charPositionInLine, originalAntlrMsg, e);</span>
<span class="nc" id="L166">        }</span>
<span class="nc" id="L167">    }</span>

    /**
     * Handles errors using the legacy framework (original implementation).
     */
    private void handleErrorWithLegacyFramework(Recognizer&lt;?, ?&gt; recognizer, Object offendingSymbol, int line, int charPositionInLine, String originalAntlrMsg, RecognitionException e) {
<span class="nc" id="L173">        String ruleId = &quot;SYNTAX-GENERAL&quot;;</span>
<span class="nc" id="L174">        String improvedMsg = originalAntlrMsg;</span>
<span class="nc" id="L175">        String offendingText = &quot;&quot;;</span>

        // Initialize offending location variables early
<span class="nc" id="L178">        int offendingLine = Math.max(1, line); // Ensure positive line number</span>
<span class="nc" id="L179">        int offendingColumnStart = Math.max(1, charPositionInLine + 1); // 1-indexed for SourceLocation, ensure positive</span>
<span class="nc" id="L180">        int offendingColumnEnd = Math.max(1, charPositionInLine + 1);   // Default to single point, will be adjusted</span>

<span class="nc bnc" id="L182" title="All 2 branches missed.">        if (offendingSymbol instanceof Token) {</span>
<span class="nc" id="L183">            offendingText = ((Token) offendingSymbol).getText();</span>
<span class="nc bnc" id="L184" title="All 2 branches missed.">            if (offendingText == null) {</span>
<span class="nc" id="L185">                offendingText = &quot;&quot;;</span>
            }
        }

        // Use a single variable for the final text that caused the error, for message formatting
<span class="nc bnc" id="L190" title="All 4 branches missed.">        String offendingSymbolTextForMsg = (offendingText == null || offendingText.isEmpty()) ?</span>
<span class="nc bnc" id="L191" title="All 4 branches missed.">            (e != null &amp;&amp; e.getOffendingToken() != null ? &quot;''&quot; + e.getOffendingToken().getText() + &quot;''&quot; : I18n.l(&quot;general.placeholder.currentLocation&quot;)) :</span>
<span class="nc" id="L192">            &quot;''&quot; + offendingText + &quot;''&quot;;</span>


<span class="nc bnc" id="L195" title="All 4 branches missed.">        if (recognizer != null &amp;&amp; recognizer.getClass().getSimpleName().equals(&quot;CAPLLexer&quot;)) {</span>
<span class="nc" id="L196">            ruleId = handleLexerError(e, offendingSymbolTextForMsg, originalAntlrMsg);</span>
<span class="nc" id="L197">            improvedMsg = generateLexerErrorMessage(e, offendingSymbolTextForMsg, originalAntlrMsg);</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">        } else if (recognizer instanceof Parser) {</span>
<span class="nc" id="L199">            ruleId = handleParserError(e, recognizer, offendingSymbolTextForMsg, originalAntlrMsg);</span>
<span class="nc" id="L200">            improvedMsg = generateParserErrorMessage(e, recognizer, offendingSymbolTextForMsg, originalAntlrMsg);</span>
        }
        // Determine final offending line and column for SourceLocation
        // Values might have been updated if identifierForMessageError was not null and specificMessageGeneratedForNVA was true.
        // This block determines offendingLine, offendingColumnStart, offendingColumnEnd.
        // It uses the most specific token available: offendingSymbol if it's a Token, 
        // otherwise the exception's offendingToken, or falls back to the initial line/charPosition.
<span class="nc bnc" id="L207" title="All 2 branches missed.">        if (offendingSymbol instanceof Token) { // If offendingSymbol is a Token, use its precise location</span>
<span class="nc" id="L208">            Token osToken = (Token) offendingSymbol;</span>
<span class="nc" id="L209">            offendingLine = Math.max(1, osToken.getLine());</span>
<span class="nc" id="L210">            offendingColumnStart = Math.max(1, osToken.getCharPositionInLine() + 1);</span>
<span class="nc bnc" id="L211" title="All 2 branches missed.">            offendingColumnEnd = offendingColumnStart + (osToken.getText() != null ? osToken.getText().length() : 0) -1 ;</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">            if (offendingColumnEnd &lt; offendingColumnStart) offendingColumnEnd = offendingColumnStart; // handles zero-length tokens</span>
<span class="nc bnc" id="L213" title="All 4 branches missed.">        } else if (e != null &amp;&amp; e.getOffendingToken() != null) { // Else, if exception has an offending token, use that</span>
<span class="nc" id="L214">            Token errorToken = e.getOffendingToken();</span>
<span class="nc" id="L215">            offendingLine = Math.max(1, errorToken.getLine());</span>
<span class="nc" id="L216">            offendingColumnStart = Math.max(1, errorToken.getCharPositionInLine() + 1);</span>
<span class="nc bnc" id="L217" title="All 2 branches missed.">            offendingColumnEnd = offendingColumnStart + (errorToken.getText() != null ? errorToken.getText().length() : 0) -1;</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">            if (offendingColumnEnd &lt; offendingColumnStart) offendingColumnEnd = offendingColumnStart;</span>
        } else {
            // Fallback: Use the initial line and charPositionInLine passed to syntaxError.
            // These are already set in offendingLine, offendingColumnStart by default.
            // offendingColumnEnd remains as initially set (charPositionInLine + 1) if no token info.
        }


<span class="nc" id="L226">        SourceLocation location = new SourceLocation(sourceFile.getAbsolutePath().toString(), offendingLine, offendingColumnStart, offendingLine, offendingColumnEnd);</span>
<span class="nc" id="L227">        String codeContext = null;</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">        if (sourceFile.getContent() != null) {</span>
<span class="nc" id="L229">            String[] linesArray = sourceFile.getContent().split(&quot;\\R&quot;, -1); // Split by any newline sequence</span>
<span class="nc" id="L230">            int errorLineIndex = offendingLine - 1; // Convert 1-based line to 0-based index</span>

<span class="nc" id="L232">            StringBuilder contextBuilder = new StringBuilder();</span>
            // Line before
<span class="nc bnc" id="L234" title="All 4 branches missed.">            if (errorLineIndex &gt; 0 &amp;&amp; errorLineIndex -1 &lt; linesArray.length) { // Check bounds for line before</span>
<span class="nc" id="L235">                contextBuilder.append(linesArray[errorLineIndex - 1]).append(&quot;\n&quot;);</span>
            }
            // Actual error line
<span class="nc bnc" id="L238" title="All 4 branches missed.">            if (errorLineIndex &gt;= 0 &amp;&amp; errorLineIndex &lt; linesArray.length) { // Check bounds for error line</span>
<span class="nc" id="L239">                 contextBuilder.append(linesArray[errorLineIndex]).append(&quot;\n&quot;);</span>
            }
             // Line after
<span class="nc bnc" id="L242" title="All 2 branches missed.">            if (errorLineIndex + 1 &lt; linesArray.length) { // Check bounds for line after</span>
<span class="nc" id="L243">                contextBuilder.append(linesArray[errorLineIndex + 1]);</span>
            }
<span class="nc" id="L245">            codeContext = contextBuilder.toString().trim();</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">            if (codeContext.isEmpty()) {</span>
<span class="nc" id="L247">                codeContext = null; // Ensure truly null if empty</span>
            }
        }

<span class="nc" id="L251">        IssueObject issue = new IssueObject(</span>
                ruleId,
                improvedMsg,
                SeverityLevel.ERROR, // Syntax errors are typically errors
                location,
                // Use offendingText if available, otherwise null (already captured in offendingSymbolTextForMsg for messages)
<span class="nc bnc" id="L257" title="All 4 branches missed.">                (offendingText == null || offendingText.isEmpty()) ? null : offendingText,</span>
                codeContext
        );
<span class="nc" id="L260">        issues.add(issue);</span>
<span class="nc" id="L261">    }</span>

    public List&lt;IssueObject&gt; getIssues() {
<span class="nc" id="L264">        return new ArrayList&lt;&gt;(issues); // Return a copy to prevent external modification</span>
    }

    /**
     * Clears all collected issues.
     */
    public void clearIssues() {
<span class="nc" id="L271">        issues.clear();</span>
<span class="nc" id="L272">    }</span>

    /**
     * Gets the number of issues collected.
     *
     * @return the issue count
     */
    public int getIssueCount() {
<span class="nc" id="L280">        return issues.size();</span>
    }

    /**
     * Checks if there are any errors.
     *
     * @return true if there are errors, false otherwise
     */
    public boolean hasErrors() {
<span class="nc bnc" id="L289" title="All 2 branches missed.">        return issues.stream().anyMatch(issue -&gt; issue.getSeverity() == SeverityLevel.ERROR);</span>
    }

    /**
     * Checks if there are any warnings.
     *
     * @return true if there are warnings, false otherwise
     */
    public boolean hasWarnings() {
<span class="nc bnc" id="L298" title="All 2 branches missed.">        return issues.stream().anyMatch(issue -&gt; issue.getSeverity() == SeverityLevel.WARNING);</span>
    }

    /**
     * Checks if the new error analysis framework is enabled.
     *
     * @return true if new framework is enabled, false otherwise
     */
    public boolean isNewFrameworkEnabled() {
<span class="nc" id="L307">        return useNewFramework;</span>
    }

    /**
     * Handles lexer-specific errors and returns appropriate rule ID.
     *
     * @param e The recognition exception
     * @param offendingSymbolText The offending symbol text for messages
     * @param originalAntlrMsg The original ANTLR error message
     * @return The specific rule ID for the lexer error
     */
    private String handleLexerError(RecognitionException e, String offendingSymbolText, String originalAntlrMsg) {
<span class="nc bnc" id="L319" title="All 2 branches missed.">        if (e instanceof LexerNoViableAltException) {</span>
<span class="nc" id="L320">            return &quot;LEXER-INVALID-CHAR&quot;;</span>
<span class="nc bnc" id="L321" title="All 2 branches missed.">        } else if (e != null) {</span>
            // Handle other lexer exceptions
<span class="nc" id="L323">            String exceptionType = e.getClass().getSimpleName();</span>
<span class="nc" id="L324">            LOGGER.debug(&quot;Handling lexer error of type: {}&quot;, exceptionType);</span>
<span class="nc" id="L325">            return &quot;LEXER-&quot; + exceptionType.toUpperCase().replace(&quot;EXCEPTION&quot;, &quot;&quot;);</span>
        }
<span class="nc" id="L327">        return &quot;LEXER-ERROR&quot;;</span>
    }

    /**
     * Generates user-friendly error messages for lexer errors.
     *
     * @param e The recognition exception
     * @param offendingSymbolText The offending symbol text for messages
     * @param originalAntlrMsg The original ANTLR error message
     * @return The improved error message
     */
    private String generateLexerErrorMessage(RecognitionException e, String offendingSymbolText, String originalAntlrMsg) {
<span class="nc bnc" id="L339" title="All 2 branches missed.">        if (e instanceof LexerNoViableAltException) {</span>
<span class="nc" id="L340">            LexerNoViableAltException lnvae = (LexerNoViableAltException) e;</span>
<span class="nc" id="L341">            char badChar = (char) lnvae.getInputStream().LA(1);</span>
<span class="nc bnc" id="L342" title="All 2 branches missed.">            String badCharStr = (badChar == org.antlr.v4.runtime.IntStream.EOF) ? &quot;&lt;EOF&gt;&quot; : String.valueOf(badChar);</span>
<span class="nc" id="L343">            return I18n.l(&quot;error.lexer.invalidChar&quot;, badCharStr);</span>
        } else {
<span class="nc" id="L345">            return I18n.l(&quot;error.lexer.unrecognizedSymbol&quot;, offendingSymbolText, originalAntlrMsg);</span>
        }
    }

    /**
     * Handles parser-specific errors and returns appropriate rule ID.
     *
     * @param e The recognition exception
     * @param recognizer The parser recognizer
     * @param offendingSymbolText The offending symbol text for messages
     * @param originalAntlrMsg The original ANTLR error message
     * @return The specific rule ID for the parser error
     */
    private String handleParserError(RecognitionException e, Recognizer&lt;?, ?&gt; recognizer,
                                   String offendingSymbolText, String originalAntlrMsg) {
<span class="nc bnc" id="L360" title="All 2 branches missed.">        if (e instanceof InputMismatchException) {</span>
<span class="nc" id="L361">            return &quot;PARSER-INPUT-MISMATCH&quot;;</span>
<span class="nc bnc" id="L362" title="All 2 branches missed.">        } else if (e instanceof NoViableAltException) {</span>
            // Check for CAPL-specific context
<span class="nc" id="L364">            String contextSpecificRuleId = analyzeCaplContext((Parser) recognizer, e);</span>
<span class="nc bnc" id="L365" title="All 2 branches missed.">            return contextSpecificRuleId != null ? contextSpecificRuleId : &quot;PARSER-NO-VIABLE-ALT&quot;;</span>
<span class="nc bnc" id="L366" title="All 2 branches missed.">        } else if (e instanceof FailedPredicateException) {</span>
<span class="nc" id="L367">            return &quot;PARSER-FAILED-PREDICATE&quot;;</span>
<span class="nc bnc" id="L368" title="All 2 branches missed.">        } else if (e != null) {</span>
<span class="nc" id="L369">            String exceptionType = e.getClass().getSimpleName();</span>
<span class="nc" id="L370">            LOGGER.debug(&quot;Handling parser error of type: {}&quot;, exceptionType);</span>
<span class="nc" id="L371">            return &quot;PARSER-&quot; + exceptionType.toUpperCase().replace(&quot;EXCEPTION&quot;, &quot;&quot;);</span>
        }
<span class="nc" id="L373">        return &quot;PARSER-ERROR&quot;;</span>
    }

    /**
     * Generates user-friendly error messages for parser errors.
     *
     * @param e The recognition exception
     * @param recognizer The parser recognizer
     * @param offendingSymbolText The offending symbol text for messages
     * @param originalAntlrMsg The original ANTLR error message
     * @return The improved error message
     */
    private String generateParserErrorMessage(RecognitionException e, Recognizer&lt;?, ?&gt; recognizer,
                                            String offendingSymbolText, String originalAntlrMsg) {
<span class="nc bnc" id="L387" title="All 2 branches missed.">        if (e instanceof InputMismatchException) {</span>
<span class="nc" id="L388">            InputMismatchException ime = (InputMismatchException) e;</span>
<span class="nc" id="L389">            String expected = ime.getExpectedTokens().toString(recognizer.getVocabulary());</span>
<span class="nc" id="L390">            return I18n.l(&quot;error.parser.inputMismatch.expected&quot;, offendingSymbolText, expected);</span>
<span class="nc bnc" id="L391" title="All 2 branches missed.">        } else if (e instanceof NoViableAltException) {</span>
<span class="nc" id="L392">            return generateNoViableAltMessage(originalAntlrMsg, offendingSymbolText);</span>
<span class="nc bnc" id="L393" title="All 2 branches missed.">        } else if (e instanceof FailedPredicateException) {</span>
<span class="nc" id="L394">            FailedPredicateException fpe = (FailedPredicateException) e;</span>
<span class="nc" id="L395">            return I18n.l(&quot;error.parser.failedPredicate&quot;, offendingSymbolText, fpe.getPredicate());</span>
        } else {
<span class="nc" id="L397">            return I18n.l(&quot;error.parser.default&quot;, offendingSymbolText, originalAntlrMsg);</span>
        }
    }

    /**
     * Analyzes CAPL-specific parsing context to provide more specific error identification.
     *
     * @param parser The CAPL parser instance
     * @param e The recognition exception
     * @return A CAPL-specific rule ID, or null if no specific context is detected
     */
    private String analyzeCaplContext(Parser parser, RecognitionException e) {
        try {
            // Check if we're in a message declaration context
<span class="nc bnc" id="L411" title="All 2 branches missed.">            if (isInMessageDeclarationContext(parser)) {</span>
<span class="nc" id="L412">                return &quot;PARSER-MESSAGE-DECLARATION-ERROR&quot;;</span>
            }

            // Check if we're in an event handler context
<span class="nc bnc" id="L416" title="All 2 branches missed.">            if (isInEventHandlerContext(parser)) {</span>
<span class="nc" id="L417">                return &quot;PARSER-EVENT-HANDLER-ERROR&quot;;</span>
            }

            // Check if we're in a variable declaration context
<span class="nc bnc" id="L421" title="All 2 branches missed.">            if (isInVariableDeclarationContext(parser)) {</span>
<span class="nc" id="L422">                return &quot;PARSER-VARIABLE-DECLARATION-ERROR&quot;;</span>
            }

            // Add more CAPL-specific context checks as needed
<span class="nc" id="L426">            LOGGER.debug(&quot;No specific CAPL context detected for error analysis&quot;);</span>
<span class="nc" id="L427">            return null;</span>
<span class="nc" id="L428">        } catch (Exception ex) {</span>
<span class="nc" id="L429">            LOGGER.debug(&quot;Error during CAPL context analysis: {}&quot;, ex.getMessage());</span>
<span class="nc" id="L430">            return null;</span>
        }
    }

    /**
     * Generates improved error messages for NoViableAltException.
     *
     * @param originalAntlrMsg The original ANTLR error message
     * @param offendingSymbolText The offending symbol text
     * @return The improved error message
     */
    private String generateNoViableAltMessage(String originalAntlrMsg, String offendingSymbolText) {
<span class="nc bnc" id="L442" title="All 4 branches missed.">        if (originalAntlrMsg != null &amp;&amp; originalAntlrMsg.startsWith(&quot;no viable alternative at input&quot;)) {</span>
<span class="nc" id="L443">            Pattern pattern = Pattern.compile(&quot;no viable alternative at input '(.*)'&quot;);</span>
<span class="nc" id="L444">            Matcher matcher = pattern.matcher(originalAntlrMsg);</span>
<span class="nc bnc" id="L445" title="All 2 branches missed.">            if (matcher.find()) {</span>
<span class="nc" id="L446">                String extractedSnippet = matcher.group(1);</span>
                // Replace ANTLR's escape sequences with literal characters for display
<span class="nc" id="L448">                extractedSnippet = extractedSnippet.replace(&quot;\\n&quot;, &quot;\n&quot;)</span>
<span class="nc" id="L449">                                                 .replace(&quot;\\r&quot;, &quot;\r&quot;)</span>
<span class="nc" id="L450">                                                 .replace(&quot;\\t&quot;, &quot;\t&quot;)</span>
<span class="nc" id="L451">                                                 .replace(&quot;\\'&quot;, &quot;'&quot;);</span>
<span class="nc" id="L452">                return I18n.l(&quot;error.parser.noViableAlt.extracted&quot;, &quot;'&quot; + extractedSnippet + &quot;'&quot;);</span>
            } else {
<span class="nc" id="L454">                return I18n.l(&quot;error.parser.noViableAlt.fallback&quot;, offendingSymbolText);</span>
            }
        } else {
<span class="nc" id="L457">            return I18n.l(&quot;error.parser.noViableAlt.fallback&quot;, offendingSymbolText);</span>
        }
    }

    /**
     * Checks if the parser is currently in a message declaration context.
     *
     * @param parser The parser instance
     * @return true if in message declaration context, false otherwise
     */
    private boolean isInMessageDeclarationContext(Parser parser) {
        try {
            // Check the parser's rule context stack for message-related rules
<span class="nc bnc" id="L470" title="All 2 branches missed.">            if (parser.getContext() != null) {</span>
<span class="nc" id="L471">                String contextText = parser.getContext().getText();</span>
<span class="nc bnc" id="L472" title="All 2 branches missed.">                return contextText.contains(&quot;message&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L473" title="All 4 branches missed.">                       (contextText.contains(&quot;*&quot;) || contextText.matches(&quot;.*0x[0-9a-fA-F]+.*&quot;));</span>
            }
<span class="nc" id="L475">        } catch (Exception e) {</span>
<span class="nc" id="L476">            LOGGER.debug(&quot;Error checking message declaration context: {}&quot;, e.getMessage());</span>
<span class="nc" id="L477">        }</span>
<span class="nc" id="L478">        return false;</span>
    }

    /**
     * Checks if the parser is currently in an event handler context.
     *
     * @param parser The parser instance
     * @return true if in event handler context, false otherwise
     */
    private boolean isInEventHandlerContext(Parser parser) {
        try {
<span class="nc bnc" id="L489" title="All 2 branches missed.">            if (parser.getContext() != null) {</span>
<span class="nc" id="L490">                String contextText = parser.getContext().getText();</span>
<span class="nc bnc" id="L491" title="All 4 branches missed.">                return contextText.startsWith(&quot;on&quot;) || contextText.contains(&quot;on &quot;);</span>
            }
<span class="nc" id="L493">        } catch (Exception e) {</span>
<span class="nc" id="L494">            LOGGER.debug(&quot;Error checking event handler context: {}&quot;, e.getMessage());</span>
<span class="nc" id="L495">        }</span>
<span class="nc" id="L496">        return false;</span>
    }

    /**
     * Checks if the parser is currently in a variable declaration context.
     *
     * @param parser The parser instance
     * @return true if in variable declaration context, false otherwise
     */
    private boolean isInVariableDeclarationContext(Parser parser) {
        try {
<span class="nc bnc" id="L507" title="All 2 branches missed.">            if (parser.getContext() != null) {</span>
<span class="nc" id="L508">                String contextText = parser.getContext().getText();</span>
<span class="nc bnc" id="L509" title="All 2 branches missed.">                return contextText.contains(&quot;variables&quot;) ||</span>
<span class="nc bnc" id="L510" title="All 2 branches missed.">                       contextText.matches(&quot;.*(int|char|float|double|timer|message)\\s+\\w+.*&quot;);</span>
            }
<span class="nc" id="L512">        } catch (Exception e) {</span>
<span class="nc" id="L513">            LOGGER.debug(&quot;Error checking variable declaration context: {}&quot;, e.getMessage());</span>
<span class="nc" id="L514">        }</span>
<span class="nc" id="L515">        return false;</span>
    }

    /**
     * Converts an ErrorResult from the new framework to an IssueObject for compatibility.
     *
     * @param result the error result from analysis
     * @param context the error context
     * @return the converted IssueObject
     */
    private IssueObject convertErrorResultToIssue(ErrorResult result, ErrorContext context) {
        // Create source location from context
<span class="nc" id="L527">        SourceLocation location = createSourceLocationFromContext(context);</span>

        // Extract offending text
<span class="nc" id="L530">        String offendingText = null;</span>
<span class="nc bnc" id="L531" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L532">            offendingText = ((Token) context.getOffendingSymbol()).getText();</span>
        }

        // Create code context (simplified version)
<span class="nc" id="L536">        String codeContext = createCodeContext(context.getLine());</span>

        // Build enhanced message with suggestion if available
<span class="nc" id="L539">        String enhancedMessage = result.getEnhancedMessage();</span>
<span class="nc bnc" id="L540" title="All 4 branches missed.">        if (result.getSuggestion() != null &amp;&amp; !result.getSuggestion().isEmpty()) {</span>
<span class="nc" id="L541">            enhancedMessage += &quot; Suggestion: &quot; + result.getSuggestion();</span>
        }

<span class="nc" id="L544">        return new IssueObject(</span>
<span class="nc" id="L545">            result.getRuleId(),</span>
            enhancedMessage,
<span class="nc" id="L547">            result.getSeverity(),</span>
            location,
            offendingText,
            codeContext
        );
    }

    /**
     * Creates a SourceLocation from ErrorContext.
     */
    private SourceLocation createSourceLocationFromContext(ErrorContext context) {
<span class="nc" id="L558">        int line = Math.max(1, context.getLine());</span>
<span class="nc" id="L559">        int columnStart = Math.max(1, context.getCharPositionInLine() + 1); // Convert to 1-based, ensure positive</span>
<span class="nc" id="L560">        int columnEnd = columnStart;</span>

        // Try to get more precise location from offending symbol
<span class="nc bnc" id="L563" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L564">            Token token = (Token) context.getOffendingSymbol();</span>
<span class="nc" id="L565">            line = Math.max(1, token.getLine());</span>
<span class="nc" id="L566">            columnStart = Math.max(1, token.getCharPositionInLine() + 1);</span>
<span class="nc bnc" id="L567" title="All 2 branches missed.">            columnEnd = columnStart + (token.getText() != null ? token.getText().length() : 1) - 1;</span>
<span class="nc bnc" id="L568" title="All 2 branches missed.">            if (columnEnd &lt; columnStart) {</span>
<span class="nc" id="L569">                columnEnd = columnStart;</span>
            }
        }

<span class="nc" id="L573">        return new SourceLocation(</span>
<span class="nc" id="L574">            sourceFile.getAbsolutePath().toString(),</span>
            line,
            columnStart,
            line,
            columnEnd
        );
    }

    /**
     * Creates code context around the error line.
     */
    private String createCodeContext(int errorLine) {
<span class="nc bnc" id="L586" title="All 2 branches missed.">        if (sourceFile.getContent() == null) {</span>
<span class="nc" id="L587">            return null;</span>
        }

<span class="nc" id="L590">        String[] lines = sourceFile.getContent().split(&quot;\\R&quot;, -1);</span>
<span class="nc" id="L591">        int errorLineIndex = errorLine - 1; // Convert to 0-based</span>

<span class="nc" id="L593">        StringBuilder contextBuilder = new StringBuilder();</span>

        // Line before
<span class="nc bnc" id="L596" title="All 4 branches missed.">        if (errorLineIndex &gt; 0 &amp;&amp; errorLineIndex - 1 &lt; lines.length) {</span>
<span class="nc" id="L597">            contextBuilder.append(lines[errorLineIndex - 1]).append(&quot;\n&quot;);</span>
        }

        // Error line
<span class="nc bnc" id="L601" title="All 4 branches missed.">        if (errorLineIndex &gt;= 0 &amp;&amp; errorLineIndex &lt; lines.length) {</span>
<span class="nc" id="L602">            contextBuilder.append(lines[errorLineIndex]).append(&quot;\n&quot;);</span>
        }

        // Line after
<span class="nc bnc" id="L606" title="All 2 branches missed.">        if (errorLineIndex + 1 &lt; lines.length) {</span>
<span class="nc" id="L607">            contextBuilder.append(lines[errorLineIndex + 1]);</span>
        }

<span class="nc" id="L610">        String context = contextBuilder.toString().trim();</span>
<span class="nc bnc" id="L611" title="All 2 branches missed.">        return context.isEmpty() ? null : context;</span>
    }

    // ========== Enhanced Error Context Creation (T020) ==========

    /**
     * Creates an enhanced error context with detailed information extraction.
     * This implements the T020 requirement for error context extraction logic.
     */
    private ErrorContext createEnhancedErrorContext(Recognizer&lt;?, ?&gt; recognizer, Object offendingSymbol,
                                                   int line, int charPositionInLine, String originalAntlrMsg,
                                                   RecognitionException e) {
<span class="nc" id="L623">        ErrorContext.Builder contextBuilder = ErrorContext.builder()</span>
<span class="nc" id="L624">            .recognizer(recognizer)</span>
<span class="nc" id="L625">            .sourceFile(sourceFile)</span>
<span class="nc" id="L626">            .exception(e)</span>
<span class="nc" id="L627">            .offendingSymbol(offendingSymbol)</span>
<span class="nc" id="L628">            .line(line)</span>
<span class="nc" id="L629">            .charPositionInLine(charPositionInLine)</span>
<span class="nc" id="L630">            .originalMessage(originalAntlrMsg);</span>

        // Extract preceding tokens (T020 requirement)
<span class="nc bnc" id="L633" title="All 4 branches missed.">        if (recognizer instanceof Parser &amp;&amp; offendingSymbol instanceof Token) {</span>
<span class="nc" id="L634">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens((Parser) recognizer, (Token) offendingSymbol);</span>
<span class="nc" id="L635">            contextBuilder.precedingTokens(precedingTokens);</span>
        }

        // Extract rule stack (T020 requirement)
<span class="nc bnc" id="L639" title="All 2 branches missed.">        if (recognizer instanceof Parser) {</span>
<span class="nc" id="L640">            List&lt;String&gt; ruleStack = extractRuleStack((Parser) recognizer);</span>
<span class="nc" id="L641">            contextBuilder.ruleStack(ruleStack);</span>
        }

        // Detect parse context using ContextDetector
<span class="nc" id="L645">        CaplParseContext parseContext = CaplParseContext.UNKNOWN;</span>
<span class="nc bnc" id="L646" title="All 6 branches missed.">        if (contextDetector != null &amp;&amp; recognizer instanceof Parser &amp;&amp; offendingSymbol instanceof Token) {</span>
            try {
<span class="nc" id="L648">                parseContext = contextDetector.detectContext((Parser) recognizer, (Token) offendingSymbol);</span>
<span class="nc" id="L649">                LOGGER.debug(&quot;Detected parse context: {}&quot;, parseContext);</span>
<span class="nc" id="L650">            } catch (Exception ex) {</span>
<span class="nc" id="L651">                LOGGER.debug(&quot;Context detection failed: {}&quot;, ex.getMessage());</span>
<span class="nc" id="L652">            }</span>
        }
<span class="nc" id="L654">        contextBuilder.parseContext(parseContext);</span>

<span class="nc" id="L656">        return contextBuilder.build();</span>
    }

    /**
     * Extracts preceding tokens from the parser's token stream.
     * Implements T020 requirement for token sequence extraction logic.
     */
    private List&lt;Token&gt; extractPrecedingTokens(Parser parser, Token offendingToken) {
<span class="nc" id="L664">        List&lt;Token&gt; precedingTokens = new ArrayList&lt;&gt;();</span>
        try {
<span class="nc bnc" id="L666" title="All 4 branches missed.">            if (parser.getTokenStream() != null &amp;&amp; offendingToken != null) {</span>
<span class="nc" id="L667">                int tokenIndex = offendingToken.getTokenIndex();</span>
<span class="nc" id="L668">                int startIndex = Math.max(0, tokenIndex - 10); // Get up to 10 preceding tokens</span>

<span class="nc bnc" id="L670" title="All 2 branches missed.">                for (int i = startIndex; i &lt; tokenIndex; i++) {</span>
<span class="nc" id="L671">                    Token token = parser.getTokenStream().get(i);</span>
<span class="nc bnc" id="L672" title="All 4 branches missed.">                    if (token != null &amp;&amp; token.getChannel() == Token.DEFAULT_CHANNEL) {</span>
<span class="nc" id="L673">                        precedingTokens.add(token);</span>
                    }
                }
            }
<span class="nc" id="L677">        } catch (Exception e) {</span>
<span class="nc" id="L678">            LOGGER.debug(&quot;Failed to extract preceding tokens: {}&quot;, e.getMessage());</span>
<span class="nc" id="L679">        }</span>
<span class="nc" id="L680">        return precedingTokens;</span>
    }

    /**
     * Extracts the current rule stack from the parser.
     * Implements T020 requirement for rule stack analysis logic.
     */
    private List&lt;String&gt; extractRuleStack(Parser parser) {
<span class="nc" id="L688">        List&lt;String&gt; ruleStack = new ArrayList&lt;&gt;();</span>
        try {
<span class="nc bnc" id="L690" title="All 2 branches missed.">            if (parser.getContext() != null) {</span>
<span class="nc" id="L691">                org.antlr.v4.runtime.ParserRuleContext context = parser.getContext();</span>
<span class="nc bnc" id="L692" title="All 2 branches missed.">                while (context != null) {</span>
<span class="nc" id="L693">                    int ruleIndex = context.getRuleIndex();</span>
<span class="nc bnc" id="L694" title="All 4 branches missed.">                    if (ruleIndex &gt;= 0 &amp;&amp; ruleIndex &lt; parser.getRuleNames().length) {</span>
<span class="nc" id="L695">                        ruleStack.add(parser.getRuleNames()[ruleIndex]);</span>
                    }
<span class="nc" id="L697">                    context = context.getParent();</span>
<span class="nc" id="L698">                }</span>
            }
<span class="nc" id="L700">        } catch (Exception e) {</span>
<span class="nc" id="L701">            LOGGER.debug(&quot;Failed to extract rule stack: {}&quot;, e.getMessage());</span>
<span class="nc" id="L702">        }</span>
<span class="nc" id="L703">        return ruleStack;</span>
    }

    /**
     * Checks if the error is from a lexer.
     */
    private boolean isLexerError(Recognizer&lt;?, ?&gt; recognizer) {
<span class="nc bnc" id="L710" title="All 2 branches missed.">        if (recognizer == null) {</span>
<span class="nc" id="L711">            return false;</span>
        }
<span class="nc bnc" id="L713" title="All 4 branches missed.">        return recognizer.getClass().getSimpleName().equals(&quot;CAPLLexer&quot;) ||</span>
               recognizer instanceof org.antlr.v4.runtime.Lexer;
    }

    /**
     * Checks if the error is from a parser.
     */
    private boolean isParserError(Recognizer&lt;?, ?&gt; recognizer) {
<span class="nc bnc" id="L721" title="All 4 branches missed.">        return recognizer != null &amp;&amp; recognizer instanceof Parser;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>