<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ParsingResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser</a> &gt; <span class="el_source">ParsingResult.java</span></div><h1>ParsingResult.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser;

import com.polelink.calint.util.SourceFile; // Corrected package
import com.polelink.calint.issue.IssueObject; // Placeholder for IssueObject, to be implemented
import org.antlr.v4.runtime.tree.ParseTree;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Encapsulates the results of the lexical and syntactic analysis phases.
 * Generated by the LexerParser.
 */
public final class ParsingResult {
    private final Map&lt;SourceFile, ParseTree&gt; parseTrees;
    private final List&lt;IssueObject&gt; issues;

    /**
     * Constructs a new ParsingResult.
     *
     * @param parseTrees A map of source files to their corresponding ANTLR ParseTrees.
     * @param issues A list of issues detected during parsing.
     */
<span class="nc" id="L26">    public ParsingResult(Map&lt;SourceFile, ParseTree&gt; parseTrees, List&lt;IssueObject&gt; issues) {</span>
<span class="nc bnc" id="L27" title="All 2 branches missed.">        this.parseTrees = Collections.unmodifiableMap(parseTrees != null ? parseTrees : Collections.emptyMap());</span>
<span class="nc bnc" id="L28" title="All 2 branches missed.">        this.issues = Collections.unmodifiableList(issues != null ? issues : Collections.emptyList());</span>
<span class="nc" id="L29">    }</span>

    /**
     * Gets the map of source files to their ANTLR ParseTrees.
     *
     * @return An unmodifiable map of parse trees.
     */
    public Map&lt;SourceFile, ParseTree&gt; getParseTrees() {
<span class="nc" id="L37">        return parseTrees;</span>
    }

    /**
     * Gets the list of issues detected during parsing.
     *
     * @return An unmodifiable list of issues.
     */
    public List&lt;IssueObject&gt; getIssues() {
<span class="nc" id="L46">        return issues;</span>
    }

    /**
     * Checks if any issues were detected during parsing.
     *
     * @return true if issues were detected, false otherwise.
     */
    public boolean hasIssues() {
<span class="nc bnc" id="L55" title="All 2 branches missed.">        return !issues.isEmpty();</span>
    }

    /**
     * (Optional) Helper method to get the ParseTree for a specific source file.
     *
     * @param sourceFile The source file for which to retrieve the ParseTree.
     * @return The ParseTree for the given source file, or null if not found.
     */
    public ParseTree getParseTreeFor(SourceFile sourceFile) {
<span class="nc" id="L65">        Objects.requireNonNull(sourceFile, &quot;sourceFile cannot be null&quot;);</span>
<span class="nc" id="L66">        return parseTrees.get(sourceFile);</span>
    }

    // No equals/hashCode/toString defined in the design doc for this, 
    // as instances are likely unique per parsing run and identity comparison might be sufficient.
    // If needed for specific use cases (e.g., caching results based on content), they can be added.
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>