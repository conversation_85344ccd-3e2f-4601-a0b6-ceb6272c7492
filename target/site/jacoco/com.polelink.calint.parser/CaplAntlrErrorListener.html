<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplAntlrErrorListener</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser</a> &gt; <span class="el_class">CaplAntlrErrorListener</span></div><h1>CaplAntlrErrorListener</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,307 of 1,307</td><td class="ctr2">0%</td><td class="bar">208 of 208</td><td class="ctr2">0%</td><td class="ctr1">136</td><td class="ctr2">136</td><td class="ctr1">274</td><td class="ctr2">274</td><td class="ctr1">32</td><td class="ctr2">32</td></tr></tfoot><tbody><tr><td id="a16"><a href="CaplAntlrErrorListener.java.html#L173" class="el_method">handleErrorWithLegacyFramework(Recognizer, Object, int, int, String, RecognitionException)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="280" alt="280"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="50" alt="50"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h0">50</td><td class="ctr2" id="i0">50</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a17"><a href="CaplAntlrErrorListener.java.html#L125" class="el_method">handleErrorWithNewFramework(Recognizer, Object, int, int, String, RecognitionException)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="133" alt="133"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h1">27</td><td class="ctr2" id="i1">27</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="CaplAntlrErrorListener.java.html#L623" class="el_method">createEnhancedErrorContext(Recognizer, Object, int, int, String, RecognitionException)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="83" alt="83"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h2">23</td><td class="ctr2" id="i2">23</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="CaplAntlrErrorListener.java.html#L586" class="el_method">createCodeContext(int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="78" alt="78"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h3">13</td><td class="ctr2" id="i3">13</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a13"><a href="CaplAntlrErrorListener.java.html#L387" class="el_method">generateParserErrorMessage(RecognitionException, Recognizer, String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="66" alt="66"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h11">10</td><td class="ctr2" id="i11">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a8"><a href="CaplAntlrErrorListener.java.html#L558" class="el_method">createSourceLocationFromContext(ErrorContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="64" alt="64"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="CaplAntlrErrorListener.java.html#L442" class="el_method">generateNoViableAltMessage(String, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="62" alt="62"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g11">4</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="CaplAntlrErrorListener.java.html#L664" class="el_method">extractPrecedingTokens(Parser, Token)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="48" alt="48"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a5"><a href="CaplAntlrErrorListener.java.html#L527" class="el_method">convertErrorResultToIssue(ErrorResult, ErrorContext)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="47" alt="47"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h9">11</td><td class="ctr2" id="i9">11</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="CaplAntlrErrorListener.java.html#L51" class="el_method">CaplAntlrErrorListener(SourceFile, ErrorAnalyzerRegistry, ContextDetector, LexerErrorProcessor, ParserErrorProcessor, boolean)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="44" alt="44"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f5">6</td><td class="ctr2" id="g5">6</td><td class="ctr1" id="h12">10</td><td class="ctr2" id="i12">10</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a19"><a href="CaplAntlrErrorListener.java.html#L360" class="el_method">handleParserError(RecognitionException, Recognizer, String, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="44" alt="44"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f6">6</td><td class="ctr2" id="g6">6</td><td class="ctr1" id="h8">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="CaplAntlrErrorListener.java.html#L339" class="el_method">generateLexerErrorMessage(RecognitionException, String, String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="42" alt="42"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h17">6</td><td class="ctr2" id="i17">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a10"><a href="CaplAntlrErrorListener.java.html#L688" class="el_method">extractRuleStack(Parser)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="42" alt="42"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h4">13</td><td class="ctr2" id="i4">13</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a23"><a href="CaplAntlrErrorListener.java.html#L470" class="el_method">isInMessageDeclarationContext(Parser)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="32" alt="32"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">5</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h13">8</td><td class="ctr2" id="i13">8</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a0"><a href="CaplAntlrErrorListener.java.html#L411" class="el_method">analyzeCaplContext(Parser, RecognitionException)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="31" alt="31"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h10">11</td><td class="ctr2" id="i10">11</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a22"><a href="CaplAntlrErrorListener.java.html#L489" class="el_method">isInEventHandlerContext(Parser)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="28" alt="28"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a24"><a href="CaplAntlrErrorListener.java.html#L507" class="el_method">isInVariableDeclarationContext(Parser)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="28" alt="28"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h14">8</td><td class="ctr2" id="i14">8</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a2"><a href="CaplAntlrErrorListener.java.html#L80" class="el_method">CaplAntlrErrorListener(SourceFile, ErrorAnalyzerRegistry, ContextDetector)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="26" alt="26"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h18">4</td><td class="ctr2" id="i18">4</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a18"><a href="CaplAntlrErrorListener.java.html#L319" class="el_method">handleLexerError(RecognitionException, String, String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="24" alt="24"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h16">7</td><td class="ctr2" id="i16">7</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a31"><a href="CaplAntlrErrorListener.java.html#L113" class="el_method">syntaxError(Recognizer, Object, int, int, String, RecognitionException)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="21" alt="21"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h19">4</td><td class="ctr2" id="i19">4</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a25"><a href="CaplAntlrErrorListener.java.html#L710" class="el_method">isLexerError(Recognizer)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="17" alt="17"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f17">4</td><td class="ctr2" id="g17">4</td><td class="ctr1" id="h20">3</td><td class="ctr2" id="i20">3</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a1"><a href="CaplAntlrErrorListener.java.html#L69" class="el_method">CaplAntlrErrorListener(SourceFile)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="9" alt="9"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h21">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a27"><a href="CaplAntlrErrorListener.java.html#L721" class="el_method">isParserError(Recognizer)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="9" alt="9"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a29"><a href="CaplAntlrErrorListener.java.html#L298" class="el_method">lambda$hasWarnings$1(IssueObject)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a28"><a href="CaplAntlrErrorListener.java.html#L289" class="el_method">lambda$hasErrors$0(IssueObject)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a15"><a href="CaplAntlrErrorListener.java.html#L264" class="el_method">getIssues()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a20"><a href="CaplAntlrErrorListener.java.html#L289" class="el_method">hasErrors()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a21"><a href="CaplAntlrErrorListener.java.html#L298" class="el_method">hasWarnings()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a4"><a href="CaplAntlrErrorListener.java.html#L271" class="el_method">clearIssues()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a14"><a href="CaplAntlrErrorListener.java.html#L280" class="el_method">getIssueCount()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a30"><a href="CaplAntlrErrorListener.java.html#L49" class="el_method">static {...}</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a26"><a href="CaplAntlrErrorListener.java.html#L307" class="el_method">isNewFrameworkEnabled()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>