<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AntlrLexerParser.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser</a> &gt; <span class="el_source">AntlrLexerParser.java</span></div><h1>AntlrLexerParser.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap; // Corrected package
import java.util.List;
import java.util.Map;

import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.tree.ParseTree;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.parser.error.analyzer.ErrorAnalyzerRegistry;
import com.polelink.calint.parser.error.analyzer.lexer.CaplTokenAnalyzer;
import com.polelink.calint.parser.error.analyzer.lexer.InvalidCharacterAnalyzer;
import com.polelink.calint.parser.error.analyzer.lexer.NumericLiteralAnalyzer;
import com.polelink.calint.parser.error.analyzer.lexer.StringLiteralAnalyzer;
import com.polelink.calint.parser.error.analyzer.lexer.TokenErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.lexer.UnrecognizedTokenAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.AssociativeArrayDeclarationErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.BlockStructureErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.BuildInTypeErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.CaplObjectTypeErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.CaplVarDeclarationErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.CompilationUnitErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.ConstLiteralDeclarationErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.DeclarationErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.EventHandlerErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.ExpressionErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.MessageTypeErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.NoViableAltAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.OtherCaplObjectTypeErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.SectionStructureErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.SignalTypeErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.StatementErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.StructuralErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.TypeDefinitionErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.parser.TypeSpecifierErrorAnalyzer;
import com.polelink.calint.parser.error.context.ContextDetector;
import com.polelink.calint.util.SourceFile; // Re-added for List&lt;IssueObject&gt;
import com.polelink.calint.util.SourceLocation;
import com.polelink.capl.grammar.CAPLLexer;
import com.polelink.capl.grammar.CAPLParser;

/**
 * Implementation of {@link LexerParser} using ANTLR generated lexer and parser.
 */
<span class="nc" id="L53">public class AntlrLexerParser implements LexerParser {</span>

<span class="nc" id="L55">    private static final Logger LOGGER = LoggerFactory.getLogger(AntlrLexerParser.class);</span>

    /**
     * Parses a single source file using ANTLR.
     *
     * It creates a lexer and a parser, attaches a custom error listener,
     * and attempts to parse the file content. The resulting parse tree and any issues
     * encountered during parsing are collected into a {@link ParsingResult}.
     *
     * @param sourceFile The {@link SourceFile} object to parse.
     * @return A {@link ParsingResult} containing the parse tree and any issues.
     */
    @Override
    public ParsingResult parse(SourceFile sourceFile) {
<span class="nc" id="L69">        Map&lt;SourceFile, ParseTree&gt; parseTrees = new HashMap&lt;&gt;();</span>
<span class="nc" id="L70">        List&lt;IssueObject&gt; allIssues = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L72" title="All 2 branches missed.">        if (sourceFile == null) {</span>
            // Or throw IllegalArgumentException, depending on desired contract
<span class="nc" id="L74">            return new ParsingResult(Collections.emptyMap(), Collections.emptyList());</span>
        }

        // Create error analysis framework components
<span class="nc" id="L78">        ErrorAnalyzerRegistry analyzerRegistry = createErrorAnalyzerRegistry();</span>
<span class="nc" id="L79">        ContextDetector contextDetector = new ContextDetector();</span>

            try {
                // Create a CharStream from the source file content
                // Using sourceFile.getContent() and sourceFile.getAbsolutePath().toString() for stream name
<span class="nc" id="L84">                org.antlr.v4.runtime.CharStream charStream = CharStreams.fromString(sourceFile.getContent(), sourceFile.getAbsolutePath().toString());</span>


                // Create a lexer
<span class="nc" id="L88">                Lexer lexer = new CAPLLexer(charStream);</span>
<span class="nc" id="L89">                lexer.removeErrorListeners(); // Remove default console error listener</span>
<span class="nc" id="L90">                CaplAntlrErrorListener lexerErrorListener = new CaplAntlrErrorListener(sourceFile, analyzerRegistry, contextDetector);</span>
<span class="nc" id="L91">                lexer.addErrorListener(lexerErrorListener);</span>

                // Create a token stream
<span class="nc" id="L94">                CommonTokenStream tokenStream = new CommonTokenStream(lexer);</span>

                // Create a parser
<span class="nc" id="L97">                CAPLParser parser = new CAPLParser(tokenStream);</span>
<span class="nc" id="L98">                parser.removeErrorListeners(); // Remove default console error listener</span>
<span class="nc" id="L99">                CaplAntlrErrorListener parserErrorListener = new CaplAntlrErrorListener(sourceFile, analyzerRegistry, contextDetector);</span>
<span class="nc" id="L100">                parser.addErrorListener(parserErrorListener);</span>

                // Attempt to parse, using the entry rule of your grammar (e.g., 'program' or 'compilationUnit')
                // Assuming 'program' is the entry rule in CAPL.g4
<span class="nc" id="L104">                ParseTree parseTree = parser.compilationUnit(); // Entry rule from CAPL.g4</span>

<span class="nc" id="L106">                parseTrees.put(sourceFile, parseTree);</span>
<span class="nc" id="L107">                List&lt;IssueObject&gt; lexerIssues = lexerErrorListener.getIssues();</span>
<span class="nc" id="L108">                List&lt;IssueObject&gt; parserIssues = parserErrorListener.getIssues();</span>

<span class="nc" id="L110">                LOGGER.debug(&quot;Lexer issues for file '{}': {}&quot;, sourceFile.getAbsolutePath(), lexerIssues);</span>
<span class="nc" id="L111">                LOGGER.debug(&quot;Parser issues for file '{}': {}&quot;, sourceFile.getAbsolutePath(), parserIssues);</span>

<span class="nc" id="L113">                allIssues.addAll(lexerIssues);</span>
<span class="nc" id="L114">                allIssues.addAll(parserIssues);</span>

<span class="nc" id="L116">            } catch (RuntimeException re) {</span>
                // Catch runtime exceptions that might occur during parsing setup or execution.
                // These are often unexpected and might indicate issues beyond ANTLR's recognized syntax errors.
<span class="nc" id="L119">                SourceLocation location = new SourceLocation(sourceFile.getAbsolutePath().toString(), 1, 1, 1, 1); // Default to file start</span>
<span class="nc" id="L120">                allIssues.add(new IssueObject(</span>
                        &quot;PARSER-RUNTIME-ERR&quot;, // Specific Rule ID for runtime errors
<span class="nc" id="L122">                        &quot;Internal runtime error during parsing: &quot; + re.getMessage(),</span>
                        SeverityLevel.ERROR,
                        location
                ));
<span class="nc" id="L126">            } catch (Exception e) {</span>
                // Catch other, non-runtime exceptions. This is a fallback.
<span class="nc" id="L128">                SourceLocation location = new SourceLocation(sourceFile.getAbsolutePath().toString(), 1, 1, 1, 1); // Default to file start</span>
<span class="nc" id="L129">                allIssues.add(new IssueObject(</span>
                        &quot;PARSER-GENERAL-ERR&quot;, // Specific Rule ID for other general exceptions
<span class="nc" id="L131">                        &quot;Unexpected exception during parsing: &quot; + e.getMessage(),</span>
                        SeverityLevel.ERROR,
                        location
                ));
<span class="nc" id="L135">            }</span>
<span class="nc" id="L136">        return new ParsingResult(parseTrees, allIssues);</span>
    }

    /**
     * Creates and configures the error analyzer registry with all available analyzers.
     *
     * @return configured error analyzer registry
     */
    private ErrorAnalyzerRegistry createErrorAnalyzerRegistry() {
<span class="nc" id="L145">        ErrorAnalyzerRegistry registry = new ErrorAnalyzerRegistry();</span>

        // Register lexer analyzers
<span class="nc" id="L148">        registry.register(new TokenErrorAnalyzer());</span>
<span class="nc" id="L149">        registry.register(new InvalidCharacterAnalyzer());</span>
<span class="nc" id="L150">        registry.register(new UnrecognizedTokenAnalyzer());</span>
<span class="nc" id="L151">        registry.register(new StringLiteralAnalyzer());</span>
<span class="nc" id="L152">        registry.register(new NumericLiteralAnalyzer());</span>
<span class="nc" id="L153">        registry.register(new CaplTokenAnalyzer());</span>

        // Register parser analyzers
<span class="nc" id="L156">        registry.register(new NoViableAltAnalyzer());</span>
<span class="nc" id="L157">        registry.register(new StructuralErrorAnalyzer());</span>
<span class="nc" id="L158">        registry.register(new DeclarationErrorAnalyzer());</span>
<span class="nc" id="L159">        registry.register(new EventHandlerErrorAnalyzer());</span>
<span class="nc" id="L160">        registry.register(new ExpressionErrorAnalyzer());</span>
<span class="nc" id="L161">        registry.register(new StatementErrorAnalyzer());</span>
<span class="nc" id="L162">        registry.register(new MessageTypeErrorAnalyzer());</span>
<span class="nc" id="L163">        registry.register(new SignalTypeErrorAnalyzer());</span>
<span class="nc" id="L164">        registry.register(new OtherCaplObjectTypeErrorAnalyzer());</span>
<span class="nc" id="L165">        registry.register(new CompilationUnitErrorAnalyzer());</span>
<span class="nc" id="L166">        registry.register(new SectionStructureErrorAnalyzer());</span>
<span class="nc" id="L167">        registry.register(new BlockStructureErrorAnalyzer());</span>
<span class="nc" id="L168">        registry.register(new CaplVarDeclarationErrorAnalyzer());</span>
<span class="nc" id="L169">        registry.register(new TypeSpecifierErrorAnalyzer());</span>
<span class="nc" id="L170">        registry.register(new BuildInTypeErrorAnalyzer());</span>
<span class="nc" id="L171">        registry.register(new CaplObjectTypeErrorAnalyzer());</span>
<span class="nc" id="L172">        registry.register(new TypeDefinitionErrorAnalyzer());</span>
<span class="nc" id="L173">        registry.register(new AssociativeArrayDeclarationErrorAnalyzer());</span>
<span class="nc" id="L174">        registry.register(new ConstLiteralDeclarationErrorAnalyzer());</span>

<span class="nc" id="L176">        LOGGER.debug(&quot;Registered {} error analyzers&quot;, registry.getAllAnalyzers().size());</span>
<span class="nc" id="L177">        return registry;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>