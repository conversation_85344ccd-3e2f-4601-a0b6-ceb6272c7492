<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LexerErrorProcessor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.processor</a> &gt; <span class="el_source">LexerErrorProcessor.java</span></div><h1>LexerErrorProcessor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.processor;

import com.polelink.calint.parser.error.analyzer.ErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.ErrorAnalyzerRegistry;
import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * Processor for lexer errors.
 *
 * This class coordinates the analysis and processing of lexical errors
 * encountered during CAPL tokenization. It manages the selection and
 * execution of appropriate lexer error analyzers.
 */
public class LexerErrorProcessor {

<span class="nc" id="L27">    private static final Logger LOGGER = LoggerFactory.getLogger(LexerErrorProcessor.class);</span>

    private final ErrorAnalyzerRegistry analyzerRegistry;

    /**
     * Creates a new lexer error processor.
     *
     * @param analyzerRegistry the registry containing lexer error analyzers
     */
<span class="nc" id="L36">    public LexerErrorProcessor(ErrorAnalyzerRegistry analyzerRegistry) {</span>
<span class="nc" id="L37">        this.analyzerRegistry = analyzerRegistry;</span>
<span class="nc" id="L38">    }</span>

    /**
     * Processes a lexer error using the most appropriate analyzer.
     *
     * @param context the error context containing lexer error information
     * @return the error analysis result
     */
    public ErrorResult processLexerError(ErrorContext context) {
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (context == null) {</span>
<span class="nc" id="L48">            LOGGER.warn(&quot;Received null error context&quot;);</span>
<span class="nc" id="L49">            return createFallbackLexerError(&quot;Null error context&quot;);</span>
        }

<span class="nc bnc" id="L52" title="All 2 branches missed.">        if (!context.isLexerError()) {</span>
<span class="nc" id="L53">            LOGGER.warn(&quot;Received non-lexer error context: {}&quot;, context.getRecognizer().getClass().getSimpleName());</span>
<span class="nc" id="L54">            return createFallbackLexerError(&quot;Not a lexer error&quot;);</span>
        }

        try {
            // Find lexer-specific analyzers
<span class="nc" id="L59">            List&lt;ErrorAnalyzer&gt; lexerAnalyzers = analyzerRegistry.getAnalyzersByType(AnalyzerType.LEXER);</span>

<span class="nc" id="L61">            LOGGER.debug(&quot;Processing lexer error with {} available analyzers&quot;, lexerAnalyzers.size());</span>

            // Try each analyzer in priority order
<span class="nc bnc" id="L64" title="All 2 branches missed.">            for (ErrorAnalyzer analyzer : lexerAnalyzers) {</span>
                try {
<span class="nc bnc" id="L66" title="All 2 branches missed.">                    if (analyzer.canAnalyze(context)) {</span>
<span class="nc" id="L67">                        LOGGER.debug(&quot;Using lexer analyzer: {}&quot;, analyzer.getAnalyzerName());</span>

<span class="nc" id="L69">                        ErrorResult result = analyzer.analyze(context);</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">                        if (result != null) {</span>
<span class="nc" id="L71">                            LOGGER.debug(&quot;Lexer analysis successful with analyzer: {}&quot;, analyzer.getAnalyzerName());</span>
<span class="nc" id="L72">                            return enhanceLexerErrorResult(result, context);</span>
                        }
                    }
<span class="nc" id="L75">                } catch (Exception e) {</span>
<span class="nc" id="L76">                    LOGGER.warn(&quot;Error in lexer analyzer {}: {}&quot;, analyzer.getAnalyzerName(), e.getMessage());</span>
<span class="nc" id="L77">                }</span>
<span class="nc" id="L78">            }</span>

            // No analyzer could handle the error
<span class="nc" id="L81">            LOGGER.debug(&quot;No lexer analyzer could handle the error, using fallback&quot;);</span>
<span class="nc" id="L82">            return createFallbackLexerError(context);</span>

<span class="nc" id="L84">        } catch (Exception e) {</span>
<span class="nc" id="L85">            LOGGER.error(&quot;Unexpected error during lexer error processing: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L86">            return createFallbackLexerError(&quot;Processing error: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Enhances the lexer error result with additional context information.
     *
     * @param result the original error result
     * @param context the error context
     * @return the enhanced error result
     */
    private ErrorResult enhanceLexerErrorResult(ErrorResult result, ErrorContext context) {
<span class="nc bnc" id="L98" title="All 2 branches missed.">        if (result == null) {</span>
<span class="nc" id="L99">            return null;</span>
        }

        // Add lexer-specific metadata
<span class="nc" id="L103">        Map&lt;String, Object&gt; enhancedMetadata = new HashMap&lt;&gt;(result.getMetadata());</span>
<span class="nc" id="L104">        enhancedMetadata.put(&quot;errorType&quot;, &quot;lexer&quot;);</span>
<span class="nc" id="L105">        enhancedMetadata.put(&quot;recognizerType&quot;, context.getRecognizer().getClass().getSimpleName());</span>
<span class="nc" id="L106">        enhancedMetadata.put(&quot;line&quot;, context.getLine());</span>
<span class="nc" id="L107">        enhancedMetadata.put(&quot;charPosition&quot;, context.getCharPositionInLine());</span>

<span class="nc bnc" id="L109" title="All 2 branches missed.">        if (context.getOffendingSymbol() != null) {</span>
<span class="nc" id="L110">            enhancedMetadata.put(&quot;offendingSymbol&quot;, context.getOffendingSymbol().toString());</span>
        }

<span class="nc" id="L113">        return ErrorResult.builder()</span>
<span class="nc" id="L114">            .ruleId(result.getRuleId())</span>
<span class="nc" id="L115">            .enhancedMessage(result.getEnhancedMessage())</span>
<span class="nc" id="L116">            .severity(result.getSeverity())</span>
<span class="nc" id="L117">            .suggestion(result.getSuggestion())</span>
<span class="nc" id="L118">            .metadata(enhancedMetadata)</span>
<span class="nc" id="L119">            .build();</span>
    }

    /**
     * Creates a fallback lexer error result when no analyzer can handle the error.
     *
     * @param context the error context
     * @return a fallback error result
     */
    private ErrorResult createFallbackLexerError(ErrorContext context) {
<span class="nc" id="L129">        String offendingText = &quot;unknown&quot;;</span>
<span class="nc bnc" id="L130" title="All 4 branches missed.">        if (context != null &amp;&amp; context.getOffendingSymbol() != null) {</span>
<span class="nc" id="L131">            offendingText = context.getOffendingSymbol().toString();</span>
        }

<span class="nc" id="L134">        return createFallbackLexerError(&quot;Unhandled lexer error near: &quot; + offendingText);</span>
    }

    /**
     * Creates a fallback lexer error result with a custom message.
     *
     * @param reason the reason for the fallback
     * @return a fallback error result
     */
    private ErrorResult createFallbackLexerError(String reason) {
<span class="nc" id="L144">        String message = I18n.l(&quot;analyzer.lexer.message.genericError&quot;);</span>
<span class="nc" id="L145">        String suggestion = I18n.l(&quot;analyzer.lexer.suggestion.genericError&quot;);</span>

<span class="nc" id="L147">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L148">        metadata.put(&quot;errorType&quot;, &quot;lexer_fallback&quot;);</span>
<span class="nc" id="L149">        metadata.put(&quot;reason&quot;, reason);</span>

<span class="nc" id="L151">        return ErrorResult.builder()</span>
<span class="nc" id="L152">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L153">            .enhancedMessage(message)</span>
<span class="nc" id="L154">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L155">            .suggestion(suggestion)</span>
<span class="nc" id="L156">            .metadata(metadata)</span>
<span class="nc" id="L157">            .build();</span>
    }

    /**
     * Gets the number of registered lexer analyzers.
     *
     * @return the number of lexer analyzers
     */
    public int getLexerAnalyzerCount() {
<span class="nc" id="L166">        return analyzerRegistry.getAnalyzersByType(AnalyzerType.LEXER).size();</span>
    }

    /**
     * Checks if the processor has any lexer analyzers registered.
     *
     * @return true if lexer analyzers are available
     */
    public boolean hasLexerAnalyzers() {
<span class="nc bnc" id="L175" title="All 2 branches missed.">        return getLexerAnalyzerCount() &gt; 0;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>