<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ParserErrorProcessor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.processor</a> &gt; <span class="el_source">ParserErrorProcessor.java</span></div><h1>ParserErrorProcessor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.processor;

import com.polelink.calint.parser.error.analyzer.ErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.ErrorAnalyzerRegistry;
import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.ContextDetector;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Processor for parser errors.
 *
 * This class coordinates the analysis and processing of syntax errors
 * encountered during CAPL parsing. It manages the selection and
 * execution of appropriate parser error analyzers based on context.
 */
public class ParserErrorProcessor {

<span class="nc" id="L30">    private static final Logger LOGGER = LoggerFactory.getLogger(ParserErrorProcessor.class);</span>

    private final ErrorAnalyzerRegistry analyzerRegistry;
    private final ContextDetector contextDetector;

    /**
     * Creates a new parser error processor.
     *
     * @param analyzerRegistry the registry containing parser error analyzers
     * @param contextDetector the context detector for determining parse context
     */
<span class="nc" id="L41">    public ParserErrorProcessor(ErrorAnalyzerRegistry analyzerRegistry, ContextDetector contextDetector) {</span>
<span class="nc" id="L42">        this.analyzerRegistry = analyzerRegistry;</span>
<span class="nc" id="L43">        this.contextDetector = contextDetector;</span>
<span class="nc" id="L44">    }</span>

    /**
     * Processes a parser error using the most appropriate analyzer.
     *
     * @param context the error context containing parser error information
     * @return the error analysis result
     */
    public ErrorResult processParserError(ErrorContext context) {
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (context == null) {</span>
<span class="nc" id="L54">            LOGGER.warn(&quot;Received null error context&quot;);</span>
<span class="nc" id="L55">            return createFallbackParserError(&quot;Null error context&quot;);</span>
        }

<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (context.isLexerError()) {</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">            String recognizerType = context.getRecognizer() != null ?</span>
<span class="nc" id="L60">                                   context.getRecognizer().getClass().getSimpleName() : &quot;null&quot;;</span>
<span class="nc" id="L61">            LOGGER.warn(&quot;Received lexer error context in parser processor: {}&quot;, recognizerType);</span>
<span class="nc" id="L62">            return createFallbackParserError(&quot;Not a parser error&quot;);</span>
        }

        try {
            // Detect parse context if not already set
<span class="nc" id="L67">            CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L68" title="All 4 branches missed.">            if (parseContext == null &amp;&amp; context.getRecognizer() != null) {</span>
<span class="nc" id="L69">                parseContext = contextDetector.detectContext(</span>
<span class="nc" id="L70">                    (org.antlr.v4.runtime.Parser) context.getRecognizer(),</span>
<span class="nc" id="L71">                    (org.antlr.v4.runtime.Token) context.getOffendingSymbol()</span>
                );
<span class="nc" id="L73">                LOGGER.debug(&quot;Detected parse context: {}&quot;, parseContext);</span>
            }

            // Create enhanced context with detected parse context
<span class="nc" id="L77">            ErrorContext enhancedContext = context.toBuilder()</span>
<span class="nc" id="L78">                .parseContext(parseContext)</span>
<span class="nc" id="L79">                .build();</span>

            // Find parser-specific analyzers
<span class="nc" id="L82">            List&lt;ErrorAnalyzer&gt; parserAnalyzers = new ArrayList&lt;&gt;(analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_STRUCTURAL));</span>
<span class="nc" id="L83">            parserAnalyzers.addAll(analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_DECLARATION));</span>
<span class="nc" id="L84">            parserAnalyzers.addAll(analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_EVENT));</span>
<span class="nc" id="L85">            parserAnalyzers.addAll(analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_EXPRESSION));</span>

<span class="nc" id="L87">            LOGGER.debug(&quot;Processing parser error with {} available analyzers for context: {}&quot;,</span>
<span class="nc" id="L88">                        parserAnalyzers.size(), parseContext);</span>

            // Try each analyzer in priority order
<span class="nc bnc" id="L91" title="All 2 branches missed.">            for (ErrorAnalyzer analyzer : parserAnalyzers) {</span>
                try {
<span class="nc bnc" id="L93" title="All 2 branches missed.">                    if (analyzer.canAnalyze(enhancedContext)) {</span>
<span class="nc" id="L94">                        LOGGER.debug(&quot;Using parser analyzer: {}&quot;, analyzer.getAnalyzerName());</span>

<span class="nc" id="L96">                        ErrorResult result = analyzer.analyze(enhancedContext);</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">                        if (result != null) {</span>
<span class="nc" id="L98">                            LOGGER.debug(&quot;Parser analysis successful with analyzer: {}&quot;, analyzer.getAnalyzerName());</span>
<span class="nc" id="L99">                            return enhanceParserErrorResult(result, enhancedContext);</span>
                        }
                    }
<span class="nc" id="L102">                } catch (Exception e) {</span>
<span class="nc" id="L103">                    LOGGER.warn(&quot;Error in parser analyzer {}: {}&quot;, analyzer.getAnalyzerName(), e.getMessage());</span>
<span class="nc" id="L104">                }</span>
<span class="nc" id="L105">            }</span>

            // No analyzer could handle the error
<span class="nc" id="L108">            LOGGER.debug(&quot;No parser analyzer could handle the error, using fallback&quot;);</span>
<span class="nc" id="L109">            return createFallbackParserError(enhancedContext);</span>

<span class="nc" id="L111">        } catch (Exception e) {</span>
<span class="nc" id="L112">            LOGGER.error(&quot;Unexpected error during parser error processing: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L113">            return createFallbackParserError(&quot;Processing error: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Enhances the parser error result with additional context information.
     *
     * @param result the original error result
     * @param context the error context
     * @return the enhanced error result
     */
    private ErrorResult enhanceParserErrorResult(ErrorResult result, ErrorContext context) {
<span class="nc bnc" id="L125" title="All 2 branches missed.">        if (result == null) {</span>
<span class="nc" id="L126">            return null;</span>
        }

        // Add parser-specific metadata
<span class="nc" id="L130">        Map&lt;String, Object&gt; enhancedMetadata = new HashMap&lt;&gt;(result.getMetadata());</span>
<span class="nc" id="L131">        enhancedMetadata.put(&quot;errorType&quot;, &quot;parser&quot;);</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">        enhancedMetadata.put(&quot;recognizerType&quot;, context.getRecognizer() != null ?</span>
<span class="nc" id="L133">                            context.getRecognizer().getClass().getSimpleName() : &quot;unknown&quot;);</span>
<span class="nc" id="L134">        enhancedMetadata.put(&quot;parseContext&quot;, context.getParseContext());</span>
<span class="nc" id="L135">        enhancedMetadata.put(&quot;line&quot;, context.getLine());</span>
<span class="nc" id="L136">        enhancedMetadata.put(&quot;charPosition&quot;, context.getCharPositionInLine());</span>

<span class="nc bnc" id="L138" title="All 2 branches missed.">        if (context.getOffendingSymbol() != null) {</span>
<span class="nc" id="L139">            enhancedMetadata.put(&quot;offendingSymbol&quot;, context.getOffendingSymbol().toString());</span>
        }

<span class="nc bnc" id="L142" title="All 4 branches missed.">        if (context.getRuleStack() != null &amp;&amp; !context.getRuleStack().isEmpty()) {</span>
<span class="nc" id="L143">            enhancedMetadata.put(&quot;ruleStack&quot;, context.getRuleStack());</span>
        }

<span class="nc" id="L146">        return ErrorResult.builder()</span>
<span class="nc" id="L147">            .ruleId(result.getRuleId())</span>
<span class="nc" id="L148">            .enhancedMessage(result.getEnhancedMessage())</span>
<span class="nc" id="L149">            .severity(result.getSeverity())</span>
<span class="nc" id="L150">            .suggestion(result.getSuggestion())</span>
<span class="nc" id="L151">            .metadata(enhancedMetadata)</span>
<span class="nc" id="L152">            .build();</span>
    }

    /**
     * Creates a fallback parser error result when no analyzer can handle the error.
     *
     * @param context the error context
     * @return a fallback error result
     */
    private ErrorResult createFallbackParserError(ErrorContext context) {
<span class="nc" id="L162">        String offendingText = &quot;unknown&quot;;</span>
<span class="nc" id="L163">        String parseContextInfo = &quot;unknown&quot;;</span>

<span class="nc bnc" id="L165" title="All 2 branches missed.">        if (context != null) {</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">            if (context.getOffendingSymbol() != null) {</span>
<span class="nc" id="L167">                offendingText = context.getOffendingSymbol().toString();</span>
            }
<span class="nc bnc" id="L169" title="All 2 branches missed.">            if (context.getParseContext() != null) {</span>
<span class="nc" id="L170">                parseContextInfo = context.getParseContext().toString();</span>
            }
        }

<span class="nc" id="L174">        return createFallbackParserError(&quot;Unhandled parser error in context &quot; + parseContextInfo + &quot; near: &quot; + offendingText);</span>
    }

    /**
     * Creates a fallback parser error result with a custom message.
     *
     * @param reason the reason for the fallback
     * @return a fallback error result
     */
    private ErrorResult createFallbackParserError(String reason) {
<span class="nc" id="L184">        String message = I18n.l(&quot;analyzer.fallback.message.analysisFailed&quot;, &quot;parser&quot;, reason);</span>
<span class="nc" id="L185">        String suggestion = I18n.l(&quot;analyzer.fallback.suggestion.checkSyntax&quot;);</span>

<span class="nc" id="L187">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L188">        metadata.put(&quot;errorType&quot;, &quot;parser_fallback&quot;);</span>
<span class="nc" id="L189">        metadata.put(&quot;reason&quot;, reason);</span>

<span class="nc" id="L191">        return ErrorResult.builder()</span>
<span class="nc" id="L192">            .ruleId(CaplErrorType.PARSER_ERROR.getErrorCode())</span>
<span class="nc" id="L193">            .enhancedMessage(message)</span>
<span class="nc" id="L194">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L195">            .suggestion(suggestion)</span>
<span class="nc" id="L196">            .metadata(metadata)</span>
<span class="nc" id="L197">            .build();</span>
    }

    /**
     * Gets the number of registered parser analyzers.
     *
     * @return the number of parser analyzers
     */
    public int getParserAnalyzerCount() {
<span class="nc" id="L206">        int count = 0;</span>
<span class="nc" id="L207">        count += analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_STRUCTURAL).size();</span>
<span class="nc" id="L208">        count += analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_DECLARATION).size();</span>
<span class="nc" id="L209">        count += analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_EVENT).size();</span>
<span class="nc" id="L210">        count += analyzerRegistry.getAnalyzersByType(AnalyzerType.PARSER_EXPRESSION).size();</span>
<span class="nc" id="L211">        return count;</span>
    }

    /**
     * Checks if the processor has any parser analyzers registered.
     *
     * @return true if parser analyzers are available
     */
    public boolean hasParserAnalyzers() {
<span class="nc bnc" id="L220" title="All 2 branches missed.">        return getParserAnalyzerCount() &gt; 0;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>