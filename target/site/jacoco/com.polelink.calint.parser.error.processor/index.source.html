<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.parser.error.processor</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.parser.error.processor</span></div><h1>com.polelink.calint.parser.error.processor</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">615 of 615</td><td class="ctr2">0%</td><td class="bar">54 of 54</td><td class="ctr2">0%</td><td class="ctr1">43</td><td class="ctr2">43</td><td class="ctr1">153</td><td class="ctr2">153</td><td class="ctr1">16</td><td class="ctr2">16</td><td class="ctr1">2</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a1"><a href="ParserErrorProcessor.java.html" class="el_source">ParserErrorProcessor.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="375" alt="375"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h0">91</td><td class="ctr2" id="i0">91</td><td class="ctr1" id="j0">8</td><td class="ctr2" id="k0">8</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="LexerErrorProcessor.java.html" class="el_source">LexerErrorProcessor.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="240" alt="240"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="20" alt="20"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">18</td><td class="ctr2" id="g1">18</td><td class="ctr1" id="h1">62</td><td class="ctr2" id="i1">62</td><td class="ctr1" id="j1">8</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>