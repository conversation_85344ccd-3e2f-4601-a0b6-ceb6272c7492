<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RulesConfigManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules.config</a> &gt; <span class="el_source">RulesConfigManager.java</span></div><h1>RulesConfigManager.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import com.fasterxml.jackson.core.type.TypeReference;
import com.polelink.calint.issue.SeverityLevel;

/**
 * Manages rule configurations loaded from rules-config.yaml.
 */
public class RulesConfigManager {

<span class="nc" id="L27">    private static final Logger LOGGER = LoggerFactory.getLogger(RulesConfigManager.class);</span>
    public static final String DEFAULT_RULES_CONFIG_FILENAME = &quot;rules-config.yaml&quot;;
    public static final String RULE_METADATA_FILENAME = &quot;rule-metadata.yaml&quot;;

    private final Map&lt;String, RuleDefinition&gt; ruleDefinitions;
    private final Map&lt;String, RuleConfig&gt; ruleConfigurations;

    /**
     * Private constructor to enforce loading through factory methods.
     * @param ruleDefinitions The map of rule ID to RuleDefinition from rule-metadata.yaml.
     * @param ruleConfigurations The map of rule ID to RuleConfig from rules-config.yaml.
     */
<span class="nc" id="L39">    private RulesConfigManager(Map&lt;String, RuleDefinition&gt; ruleDefinitions, Map&lt;String, RuleConfig&gt; ruleConfigurations) {</span>
<span class="nc bnc" id="L40" title="All 2 branches missed.">        this.ruleDefinitions = ruleDefinitions != null ? Collections.unmodifiableMap(new HashMap&lt;&gt;(ruleDefinitions)) : Collections.emptyMap();</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">        this.ruleConfigurations = ruleConfigurations != null ? Collections.unmodifiableMap(new HashMap&lt;&gt;(ruleConfigurations)) : Collections.emptyMap();</span>
<span class="nc" id="L42">    }</span>

    /**
     * Loads rule configurations from the specified YAML file path.
     *
     * @param configFilePath The path to the rules-config.yaml file.
     * @return An instance of RulesConfigManager.
     */
    public static RulesConfigManager load(Path configFilePath) {
<span class="nc" id="L51">        ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());</span>
<span class="nc" id="L52">        Map&lt;String, RuleDefinition&gt; loadedRuleDefinitions = Collections.&lt;String, RuleDefinition&gt;emptyMap();</span>

        // Load rule-metadata.yaml from classpath
<span class="nc" id="L55">        try (InputStream metaIs = RulesConfigManager.class.getClassLoader().getResourceAsStream(RULE_METADATA_FILENAME)) {</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">            if (metaIs == null) {</span>
<span class="nc" id="L57">                LOGGER.error(&quot;CRITICAL: Rule metadata file '{}' not found in classpath. No default rule definitions will be available.&quot;, RULE_METADATA_FILENAME);</span>
            } else {
<span class="nc" id="L59">                List&lt;RuleDefinition&gt; defList = yamlMapper.readValue(metaIs, new TypeReference&lt;List&lt;RuleDefinition&gt;&gt;() {});</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">                if (defList != null) {</span>
<span class="nc" id="L61">                    loadedRuleDefinitions = defList.stream()</span>
<span class="nc" id="L62">                        .filter(Objects::nonNull)</span>
<span class="nc bnc" id="L63" title="All 4 branches missed.">                        .filter(def -&gt; def.getId() != null &amp;&amp; !def.getId().trim().isEmpty())</span>
<span class="nc" id="L64">                        .collect(Collectors.toMap(RuleDefinition::getId, def -&gt; def, (def1, def2) -&gt; {</span>
<span class="nc" id="L65">                            LOGGER.warn(&quot;Duplicate rule ID '{}' found in {}. Using the first occurrence.&quot;, def1.getId(), RULE_METADATA_FILENAME);</span>
<span class="nc" id="L66">                            return def1;</span>
                        }));
<span class="nc" id="L68">                    LOGGER.debug(&quot;Successfully loaded {} rule definitions from {}.&quot;, loadedRuleDefinitions.size(), RULE_METADATA_FILENAME);</span>
                } else {
<span class="nc" id="L70">                    LOGGER.warn(&quot;Rule metadata file '{}' was empty or malformed. No default rule definitions loaded.&quot;, RULE_METADATA_FILENAME);</span>
                }
            }
<span class="nc" id="L73">        } catch (IOException e) {</span>
<span class="nc" id="L74">            LOGGER.error(&quot;Failed to load or parse rule metadata from '{}': {}. No default rule definitions will be available.&quot;, </span>
<span class="nc" id="L75">                         RULE_METADATA_FILENAME, e.getMessage(), e);</span>
<span class="nc" id="L76">            loadedRuleDefinitions = Collections.&lt;String, RuleDefinition&gt;emptyMap(); // Ensure it's empty on error</span>
<span class="nc" id="L77">        }</span>

        // Load rules-config.yaml from the given path
        Map&lt;String, RuleConfig&gt; configsFromFile; // Declare here, assign in branches

<span class="nc" id="L82">        File configFile = configFilePath.toFile();</span>
<span class="nc bnc" id="L83" title="All 4 branches missed.">        if (configFile.exists() &amp;&amp; configFile.isFile()) {</span>
            try {
                // Deserialize the YAML file into Map&lt;String, RuleConfig&gt;
<span class="nc" id="L86">                Map&lt;String, RuleConfig&gt; tempConfigs = yamlMapper.readValue(configFile, new TypeReference&lt;Map&lt;String, RuleConfig&gt;&gt;() {});</span>
                
<span class="nc bnc" id="L88" title="All 2 branches missed.">                if (tempConfigs != null) {</span>
<span class="nc" id="L89">                    configsFromFile = tempConfigs;</span>
                    // The RuleConfig ID is implicitly the key in the map, no need to set it on the object itself.
<span class="nc" id="L91">                    LOGGER.debug(&quot;Successfully loaded {} rule configurations from: {}&quot;, configsFromFile.size(), configFilePath);</span>
                } else {
<span class="nc" id="L93">                    LOGGER.warn(&quot;Rules configuration file {} was parsed as null. Using empty configuration.&quot;, configFilePath);</span>
<span class="nc" id="L94">                    configsFromFile = Collections.&lt;String, RuleConfig&gt;emptyMap();</span>
                }
<span class="nc" id="L96">            } catch (IOException e) {</span>
<span class="nc" id="L97">                LOGGER.error(&quot;Failed to load or parse rule configurations from {}: {}. Using empty configuration.&quot;, </span>
<span class="nc" id="L98">                             configFilePath, e.getMessage(), e);</span>
<span class="nc" id="L99">                configsFromFile = Collections.&lt;String, RuleConfig&gt;emptyMap(); // Ensure it's empty on error</span>
<span class="nc" id="L100">            }</span>
        } else {
<span class="nc" id="L102">            LOGGER.warn(&quot;Rules configuration file not found at: {}. Using empty rule configuration.&quot;, configFilePath);</span>
<span class="nc" id="L103">            configsFromFile = Collections.&lt;String, RuleConfig&gt;emptyMap(); // Ensure it's empty if file not found</span>
        }
        
        // configsFromFile is guaranteed to be non-null here by the logic above
<span class="nc" id="L107">        return new RulesConfigManager(loadedRuleDefinitions, configsFromFile);</span>
    }
    
    /**
     * Gets the configuration for a specific rule ID from rules-config.yaml.
     * This does not yet consider defaults from metadata or rule definitions.
     *
     * @param ruleId The ID of the rule.
     * @return An Optional containing the RuleConfig if found, otherwise empty.
     */
    public Optional&lt;RuleConfig&gt; getRuleConfigFromYaml(String ruleId) {
<span class="nc" id="L118">        return Optional.ofNullable(ruleConfigurations.get(ruleId));</span>
    }

    /**
     * Gets all rule configurations loaded directly from the YAML file.
     *
     * @return An unmodifiable map of rule ID to RuleConfig.
     */
    public Map&lt;String, RuleConfig&gt; getAllRuleConfigsFromYaml() {
<span class="nc" id="L127">        return ruleConfigurations;</span>
    }

    // Further methods will be needed to integrate P1, P2, P3, P4 logic
    // e.g., getEffectiveRuleConfig(String ruleId, RuleMetadata metadata, DefaultActivationPolicy policy)

    /**
     * Gets the {@link RuleDefinition} for a specific rule ID, loaded from rule-metadata.yaml.
     *
     * @param ruleId The ID of the rule.
     * @return An Optional containing the {@link RuleDefinition} if found, otherwise empty.
     */
    public Optional&lt;RuleDefinition&gt; getRuleDefinition(String ruleId) {
<span class="nc" id="L140">        return Optional.ofNullable(ruleDefinitions.get(ruleId));</span>
    }

    /**
     * Gets all {@link RuleDefinition}s loaded from rule-metadata.yaml.
     *
     * @return An unmodifiable map of rule ID to {@link RuleDefinition}.
     */
    public Map&lt;String, RuleDefinition&gt; getAllRuleDefinitions() {
<span class="nc" id="L149">        return ruleDefinitions;</span>
    }

    /**
     * Calculates the effective configuration for a given rule ID, considering defaults
     * from rule-metadata.yaml and overrides from rules-config.yaml.
     * CLI overrides are not yet implemented here.
     *
     * @param ruleId The ID of the rule.
     * @return An Optional containing the effective {@link RuleConfig}. 
     *         If the rule is not defined in metadata, or if it's ultimately disabled by configuration,
     *         this Optional will be empty, indicating the rule should not be run.
     */
    public Optional&lt;RuleConfig&gt; getEffectiveRuleConfig(String ruleId) {
<span class="nc" id="L163">        Optional&lt;RuleDefinition&gt; optRuleDefinition = getRuleDefinition(ruleId);</span>

<span class="nc bnc" id="L165" title="All 2 branches missed.">        if (!optRuleDefinition.isPresent()) {</span>
            // Rule ID not found in rule-metadata.yaml, so it's not a known/valid rule.
            // A warning might be logged elsewhere if a configuration attempts to configure an unknown rule (P5).
<span class="nc" id="L168">            return Optional.empty();</span>
        }

<span class="nc" id="L171">        RuleDefinition ruleDefinition = optRuleDefinition.get();</span>

        // P1 &amp; P2: Start with defaults from rule-metadata.yaml (rules are enabled by default with their defined severity)
<span class="nc" id="L174">        boolean effectiveEnabled = true; </span>
<span class="nc" id="L175">        SeverityLevel effectiveSeverity = ruleDefinition.getDefaultSeverity();</span>
<span class="nc" id="L176">        Map&lt;String, Object&gt; effectiveOptions = new HashMap&lt;&gt;(); // Initialize as an empty map</span>

        // P2: Apply overrides from rules-config.yaml
<span class="nc" id="L179">        Optional&lt;RuleConfig&gt; yamlRuleConfigOpt = getRuleConfigFromYaml(ruleId);</span>
<span class="nc bnc" id="L180" title="All 2 branches missed.">        if (yamlRuleConfigOpt.isPresent()) {</span>
<span class="nc" id="L181">            RuleConfig yamlRuleConfig = yamlRuleConfigOpt.get();</span>
            // rules-config.yaml explicitly sets the enabled status. If a rule is in rules-config.yaml,
            // its 'enabled' field there dictates its status, overriding the default 'true'.
<span class="nc" id="L184">            effectiveEnabled = yamlRuleConfig.isEnabled(); </span>

<span class="nc bnc" id="L186" title="All 2 branches missed.">            if (yamlRuleConfig.getSeverity() != null) {</span>
<span class="nc" id="L187">                effectiveSeverity = yamlRuleConfig.getSeverity(); // Override severity if specified in rules-config.yaml</span>
            }
            // Load options from yamlRuleConfig
<span class="nc bnc" id="L190" title="All 4 branches missed.">            if (yamlRuleConfig.getOptions() != null &amp;&amp; !yamlRuleConfig.getOptions().isEmpty()) {</span>
<span class="nc" id="L191">                effectiveOptions = yamlRuleConfig.getOptions();</span>
            }
        }

        // P3: Placeholder for CLI overrides - to be implemented later
        // If CLI overrides exist for this ruleId:
        //   - Update effectiveEnabled (CLI --disable-rule takes precedence)
        //   - Update effectiveSeverity (CLI --override-severity takes precedence)

<span class="nc bnc" id="L200" title="All 2 branches missed.">        if (!effectiveEnabled) {</span>
            // If the rule is ultimately disabled by any configuration layer, it should not be run.
<span class="nc" id="L202">            return Optional.empty(); </span>
        }

        // If the rule is defined and enabled, return its effective configuration.
        // Use the collected effectiveOptions here.
<span class="nc" id="L207">        return Optional.of(new RuleConfig(true, effectiveSeverity, effectiveOptions)); // RuleConfig's 'enabled' field here is true because we've passed the !effectiveEnabled check</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>