<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RuleConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules.config</a> &gt; <span class="el_source">RuleConfig.java</span></div><h1>RuleConfig.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules.config; // Moved from com.polelink.calint.config

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.polelink.calint.issue.SeverityLevel; // Added import

/**
 * Represents the configuration for a single linting rule.
 * Instances of this class are typically deserialized from a configuration file (e.g., JSON).
 */
public final class RuleConfig {

    private final boolean enabled;
    private final SeverityLevel severity; // Can be null if not specified, to use default
    private final Map&lt;String, Object&gt; options;

    /**
     * Constructor used by Jackson for deserialization and for manual creation.
     *
     * @param enabled  Whether the rule is enabled. Defaults to true if null is passed.
     * @param severity The severity level for this rule. Can be null.
     * @param options  Specific options for this rule. Can be null.
     */
    @JsonCreator
    public RuleConfig(
            @JsonProperty(&quot;enabled&quot;) Boolean enabled,
            @JsonProperty(&quot;severity&quot;) SeverityLevel severity,
<span class="nc" id="L32">            @JsonProperty(&quot;options&quot;) Map&lt;String, Object&gt; options) {</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">        this.enabled = (enabled != null) ? enabled : true; // Default to true if not specified</span>
<span class="nc" id="L34">        this.severity = severity; // Retain null if not specified</span>
<span class="nc bnc" id="L35" title="All 2 branches missed.">        this.options = (options != null) ? Collections.unmodifiableMap(options) : Collections.emptyMap();</span>
<span class="nc" id="L36">    }</span>

    /**
     * Convenience constructor for creating a default (enabled, no specific severity or options) RuleConfig.
     */
    public RuleConfig() {
<span class="nc" id="L42">        this(true, null, null);</span>
<span class="nc" id="L43">    }</span>

    public boolean isEnabled() {
<span class="nc" id="L46">        return enabled;</span>
    }

    public SeverityLevel getSeverity() {
<span class="nc" id="L50">        return severity;</span>
    }

    public Map&lt;String, Object&gt; getOptions() {
<span class="nc" id="L54">        return options;</span>
    }

    @SuppressWarnings(&quot;unchecked&quot;)
    public &lt;T&gt; T getOption(String key, T defaultValue) {
<span class="nc" id="L59">        return (T) options.getOrDefault(key, defaultValue);</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L64" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L65" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc" id="L66">        RuleConfig that = (RuleConfig) o;</span>
<span class="nc bnc" id="L67" title="All 4 branches missed.">        return enabled == that.enabled &amp;&amp;</span>
               severity == that.severity &amp;&amp;
<span class="nc bnc" id="L69" title="All 2 branches missed.">               Objects.equals(options, that.options);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L74">        return Objects.hash(enabled, severity, options);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L79">        return &quot;RuleConfig{&quot; +</span>
               &quot;enabled=&quot; + enabled +
               &quot;, severity=&quot; + severity +
               &quot;, options=&quot; + options +
               '}';
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>