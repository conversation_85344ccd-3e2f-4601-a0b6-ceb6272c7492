<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RuleDefinition.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules.config</a> &gt; <span class="el_source">RuleDefinition.java</span></div><h1>RuleDefinition.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules.config;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.rules.categories.RuleCategory;

import java.util.Objects;

/**
 * Represents the definition of a rule as loaded from the rule-metadata.yaml file.
 * This class stores the intrinsic properties of a rule, such as its ID, category,
 * description, and default severity.
 */
public class RuleDefinition {

    private String id;
    private RuleCategory category;
    private String description;
    private SeverityLevel defaultSeverity;

    // Jackson typically requires a no-arg constructor for deserialization
<span class="nc" id="L21">    public RuleDefinition() {</span>
<span class="nc" id="L22">    }</span>

<span class="nc" id="L24">    public RuleDefinition(String id, RuleCategory category, String description, SeverityLevel defaultSeverity) {</span>
<span class="nc" id="L25">        this.id = id;</span>
<span class="nc" id="L26">        this.category = category;</span>
<span class="nc" id="L27">        this.description = description;</span>
<span class="nc" id="L28">        this.defaultSeverity = defaultSeverity;</span>
<span class="nc" id="L29">    }</span>

    public String getId() {
<span class="nc" id="L32">        return id;</span>
    }

    public void setId(String id) {
<span class="nc" id="L36">        this.id = id;</span>
<span class="nc" id="L37">    }</span>

    public RuleCategory getCategory() {
<span class="nc" id="L40">        return category;</span>
    }

    public void setCategory(RuleCategory category) {
<span class="nc" id="L44">        this.category = category;</span>
<span class="nc" id="L45">    }</span>

    public String getDescription() {
<span class="nc" id="L48">        return description;</span>
    }

    public void setDescription(String description) {
<span class="nc" id="L52">        this.description = description;</span>
<span class="nc" id="L53">    }</span>

    public SeverityLevel getDefaultSeverity() {
<span class="nc" id="L56">        return defaultSeverity;</span>
    }

    public void setDefaultSeverity(SeverityLevel defaultSeverity) {
<span class="nc" id="L60">        this.defaultSeverity = defaultSeverity;</span>
<span class="nc" id="L61">    }</span>

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L66" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc" id="L67">        RuleDefinition that = (RuleDefinition) o;</span>
<span class="nc bnc" id="L68" title="All 4 branches missed.">        return Objects.equals(id, that.id) &amp;&amp;</span>
               category == that.category &amp;&amp;
<span class="nc bnc" id="L70" title="All 4 branches missed.">               Objects.equals(description, that.description) &amp;&amp;</span>
               defaultSeverity == that.defaultSeverity;
    }

    @Override
    public int hashCode() {
<span class="nc" id="L76">        return Objects.hash(id, category, description, defaultSeverity);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L81">        return &quot;RuleDefinition{&quot; +</span>
               &quot;id='&quot; + id + '\'' +
               &quot;, category=&quot; + category +
               &quot;, description='&quot; + description + '\'' +
               &quot;, defaultSeverity=&quot; + defaultSeverity +
               '}';
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>