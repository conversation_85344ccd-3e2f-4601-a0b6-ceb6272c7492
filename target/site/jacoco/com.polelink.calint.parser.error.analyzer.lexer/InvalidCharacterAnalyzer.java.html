<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InvalidCharacterAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">InvalidCharacterAnalyzer.java</span></div><h1>InvalidCharacterAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.LexerNoViableAltException;
import org.antlr.v4.runtime.RecognitionException;

import java.util.Set;

/**
 * Analyzer for invalid character errors.
 * 
 * This analyzer specifically handles LexerNoViableAltException errors
 * that occur when the lexer encounters characters it cannot recognize.
 */
public class InvalidCharacterAnalyzer extends LexerErrorAnalyzer {
    
    public InvalidCharacterAnalyzer() {
<span class="nc" id="L22">        super(&quot;InvalidCharacterAnalyzer&quot;, 50, Set.of()); // High priority, supports all contexts</span>
<span class="nc" id="L23">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
<span class="nc" id="L27">        RecognitionException exception = context.getException();</span>
<span class="nc" id="L28">        return exception instanceof LexerNoViableAltException;</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L33">        LexerNoViableAltException noViableAltException = (LexerNoViableAltException) exception;</span>
        
<span class="nc" id="L35">        char badChar = extractBadCharacter(noViableAltException);</span>
<span class="nc" id="L36">        String formattedChar = formatCharacterForDisplay(badChar);</span>
        
<span class="nc" id="L38">        String message = I18n.l(&quot;analyzer.invalidChar.message.invalidChar&quot;, formattedChar);</span>
<span class="nc" id="L39">        String suggestion = generateSuggestion(badChar);</span>
        
<span class="nc" id="L41">        return ErrorResult.builder()</span>
<span class="nc" id="L42">            .ruleId(CaplErrorType.LEXER_INVALID_CHAR.getErrorCode())</span>
<span class="nc" id="L43">            .enhancedMessage(message)</span>
<span class="nc" id="L44">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L45">            .suggestion(suggestion)</span>
<span class="nc" id="L46">            .addMetadata(&quot;invalidCharacter&quot;, formattedChar)</span>
<span class="nc" id="L47">            .addMetadata(&quot;characterCode&quot;, (int) badChar)</span>
<span class="nc" id="L48">            .addMetadata(&quot;isEOF&quot;, isEOF(badChar))</span>
<span class="nc" id="L49">            .addMetadata(&quot;isControl&quot;, Character.isISOControl(badChar))</span>
<span class="nc" id="L50">            .addMetadata(&quot;isWhitespace&quot;, Character.isWhitespace(badChar))</span>
<span class="nc" id="L51">            .build();</span>
    }
    
    /**
     * Generates a context-aware suggestion for fixing the invalid character.
     * 
     * @param badChar the invalid character
     * @return a helpful suggestion string
     */
    private String generateSuggestion(char badChar) {
<span class="nc bnc" id="L61" title="All 2 branches missed.">        if (isEOF(badChar)) {</span>
<span class="nc" id="L62">            return I18n.l(&quot;analyzer.invalidChar.suggestion.unexpectedEOF&quot;);</span>
        }
        
        // Handle common problematic characters
<span class="nc bnc" id="L66" title="All 7 branches missed.">        switch (badChar) {</span>
            case '\u201C':
            case '\u201D':
<span class="nc" id="L69">                return I18n.l(&quot;analyzer.invalidChar.suggestion.straightDoubleQuotes&quot;);</span>

            case '\u2018':
            case '\u2019':
<span class="nc" id="L73">                return I18n.l(&quot;analyzer.invalidChar.suggestion.straightSingleQuotes&quot;);</span>

            case '\u2013':
            case '\u2014':
<span class="nc" id="L77">                return I18n.l(&quot;analyzer.invalidChar.suggestion.regularMinusSign&quot;);</span>

            case '\u2026':
<span class="nc" id="L80">                return I18n.l(&quot;analyzer.invalidChar.suggestion.threePeriods&quot;);</span>

            case '\u00A0': // Non-breaking space
<span class="nc" id="L83">                return I18n.l(&quot;analyzer.invalidChar.suggestion.nonBreakingSpace&quot;);</span>

            case '\u2028': // Line separator
            case '\u2029': // Paragraph separator
<span class="nc" id="L87">                return I18n.l(&quot;analyzer.invalidChar.suggestion.regularNewline&quot;);</span>
            
            default:
<span class="nc" id="L90">                return generateCategoryBasedSuggestion(badChar);</span>
        }
    }
    
    /**
     * Generates suggestions based on character category.
     * 
     * @param badChar the invalid character
     * @return a category-based suggestion
     */
    private String generateCategoryBasedSuggestion(char badChar) {
        // Check for common symbol confusions first (before ASCII check)
<span class="nc bnc" id="L102" title="All 2 branches missed.">        if (isPossibleSymbolConfusion(badChar)) {</span>
<span class="nc" id="L103">            return getSimilarSymbolSuggestion(badChar);</span>
        }

<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (Character.isWhitespace(badChar)) {</span>
<span class="nc" id="L107">            return I18n.l(&quot;analyzer.invalidChar.suggestion.nonBreakingSpace&quot;);</span>
        }

<span class="nc bnc" id="L110" title="All 2 branches missed.">        if (Character.isISOControl(badChar)) {</span>
<span class="nc" id="L111">            return I18n.l(&quot;analyzer.invalidChar.suggestion.controlChar&quot;);</span>
        }

<span class="nc bnc" id="L114" title="All 2 branches missed.">        if (badChar &gt; 127) {</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">            if (Character.isLetter(badChar)) {</span>
<span class="nc" id="L116">                return I18n.l(&quot;analyzer.invalidChar.suggestion.nonEnglishLetter&quot;);</span>
            }

<span class="nc bnc" id="L119" title="All 2 branches missed.">            if (Character.isDigit(badChar)) {</span>
<span class="nc" id="L120">                return I18n.l(&quot;analyzer.invalidChar.suggestion.nonArabicDigit&quot;);</span>
            }

<span class="nc" id="L123">            return I18n.l(&quot;analyzer.invalidChar.suggestion.highAscii&quot;);</span>
        }

<span class="nc" id="L126">        return I18n.l(&quot;analyzer.invalidChar.suggestion.generic&quot;);</span>
    }
    
    /**
     * Checks if the character might be a confused symbol.
     * 
     * @param ch the character to check
     * @return true if it might be a symbol confusion
     */
    private boolean isPossibleSymbolConfusion(char ch) {
        // Common symbol confusions
<span class="nc bnc" id="L137" title="All 28 branches missed.">        return ch == '\u00D7' || ch == '\u00F7' || ch == '\u2260' || ch == '\u2264' || ch == '\u2265' ||</span>
               ch == '\u00B1' || ch == '\u00B0' || ch == '\u00A2' || ch == '\u00A3' || ch == '\u00A5' ||
               ch == '\u00A7' || ch == '\u00A9' || ch == '\u00AE' || ch == '\u2122';
    }
    
    /**
     * Gets suggestion for similar symbols.
     * 
     * @param ch the confused character
     * @return a suggestion for the correct symbol
     */
    private String getSimilarSymbolSuggestion(char ch) {
<span class="nc bnc" id="L149" title="All 7 branches missed.">        switch (ch) {</span>
            case '\u00D7':
<span class="nc" id="L151">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;*&quot;, &quot;multiplication&quot;);</span>
            case '\u00F7':
<span class="nc" id="L153">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;/&quot;, &quot;division&quot;);</span>
            case '\u2260':
<span class="nc" id="L155">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;!=&quot;, &quot;not equal&quot;);</span>
            case '\u2264':
<span class="nc" id="L157">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;&lt;=&quot;, &quot;less than or equal&quot;);</span>
            case '\u2265':
<span class="nc" id="L159">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;&gt;=&quot;, &quot;greater than or equal&quot;);</span>
            case '\u00B1':
<span class="nc" id="L161">                return I18n.l(&quot;analyzer.invalidChar.suggestion.symbolConfusion&quot;, &quot;+ or -&quot;, &quot;plus/minus&quot;);</span>
            default:
<span class="nc" id="L163">                return I18n.l(&quot;analyzer.invalidChar.suggestion.replaceWithAscii&quot;);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>