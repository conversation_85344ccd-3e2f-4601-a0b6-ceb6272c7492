<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NumericLiteralAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">NumericLiteralAnalyzer.java</span></div><h1>NumericLiteralAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.RecognitionException;

import java.util.Set;

/**
 * Analyzer for numeric literal errors.
 * 
 * This analyzer handles errors related to integer, floating-point,
 * and hexadecimal number literals in CAPL.
 */
public class NumericLiteralAnalyzer extends LexerErrorAnalyzer {
    
    public NumericLiteralAnalyzer() {
<span class="nc" id="L21">        super(&quot;NumericLiteralAnalyzer&quot;, 80, Set.of()); // Medium-high priority</span>
<span class="nc" id="L22">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
<span class="nc" id="L26">        String tokenText = extractOffendingSymbolText(context.getOffendingSymbol());</span>

<span class="nc bnc" id="L28" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L29">            return false;</span>
        }

        // Check if this looks like a numeric literal
<span class="nc" id="L33">        return isNumericLiteral(tokenText);</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L38">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
        
<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L41">            return createGenericNumericError(I18n.l(&quot;analyzer.numeric.message.genericNumeric&quot;));</span>
        }
        
        // Analyze different types of numeric literal errors
<span class="nc bnc" id="L45" title="All 2 branches missed.">        if (isHexadecimalLiteral(tokenText)) {</span>
<span class="nc" id="L46">            return analyzeHexadecimalError(tokenText);</span>
        }
        
<span class="nc bnc" id="L49" title="All 2 branches missed.">        if (isFloatingPointLiteral(tokenText)) {</span>
<span class="nc" id="L50">            return analyzeFloatingPointError(tokenText);</span>
        }
        
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (isIntegerLiteral(tokenText)) {</span>
<span class="nc" id="L54">            return analyzeIntegerError(tokenText);</span>
        }
        
        // Generic numeric error
<span class="nc" id="L58">        return createGenericNumericError(I18n.l(&quot;analyzer.numeric.message.genericNumeric&quot;));</span>
    }
    
    /**
     * Analyzes hexadecimal literal errors.
     */
    private ErrorResult analyzeHexadecimalError(String tokenText) {
<span class="nc" id="L65">        String message = I18n.l(&quot;analyzer.numeric.message.invalidHex&quot;);</span>
<span class="nc" id="L66">        String suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidHex&quot;);</span>

<span class="nc bnc" id="L68" title="All 2 branches missed.">        if (tokenText.length() &lt;= 2) {</span>
<span class="nc" id="L69">            message = I18n.l(&quot;analyzer.numeric.message.incompleteHex&quot;);</span>
<span class="nc" id="L70">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.incompleteHex&quot;);</span>
        } else {
<span class="nc" id="L72">            String hexPart = tokenText.substring(2);</span>
<span class="nc" id="L73">            char invalidChar = findInvalidHexCharacter(hexPart);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">            if (invalidChar != '\0') {</span>
<span class="nc" id="L75">                message = I18n.l(&quot;analyzer.numeric.message.invalidHexChar&quot;, invalidChar);</span>
<span class="nc" id="L76">                suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidHexChar&quot;);</span>
            }
        }
        
<span class="nc" id="L80">        return ErrorResult.builder()</span>
<span class="nc" id="L81">            .ruleId(CaplErrorType.LEXER_NUMERIC_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L82">            .enhancedMessage(message)</span>
<span class="nc" id="L83">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L84">            .suggestion(suggestion)</span>
<span class="nc" id="L85">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L86">            .addMetadata(&quot;numberType&quot;, &quot;hexadecimal&quot;)</span>
<span class="nc" id="L87">            .addMetadata(&quot;errorType&quot;, &quot;invalid_hex_format&quot;)</span>
<span class="nc" id="L88">            .build();</span>
    }
    
    /**
     * Analyzes floating-point literal errors.
     */
    private ErrorResult analyzeFloatingPointError(String tokenText) {
<span class="nc" id="L95">        String message = I18n.l(&quot;analyzer.numeric.message.invalidFloat&quot;);</span>
<span class="nc" id="L96">        String suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidFloat&quot;);</span>

        // Check for specific floating-point issues
<span class="nc bnc" id="L99" title="All 2 branches missed.">        if (tokenText.startsWith(&quot;.&quot;)) {</span>
<span class="nc" id="L100">            message = I18n.l(&quot;analyzer.numeric.message.floatStartsWithDot&quot;);</span>
<span class="nc" id="L101">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.floatStartsWithDot&quot;);</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">        } else if (tokenText.endsWith(&quot;.&quot;)) {</span>
<span class="nc" id="L103">            message = I18n.l(&quot;analyzer.numeric.message.floatEndsWithDot&quot;);</span>
<span class="nc" id="L104">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.floatEndsWithDot&quot;);</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">        } else if (countDecimalPoints(tokenText) &gt; 1) {</span>
<span class="nc" id="L106">            message = I18n.l(&quot;analyzer.numeric.message.multipleDecimal&quot;);</span>
<span class="nc" id="L107">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.multipleDecimal&quot;);</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">        } else if (hasInvalidFloatCharacters(tokenText)) {</span>
<span class="nc" id="L109">            char invalidChar = findInvalidFloatCharacter(tokenText);</span>
<span class="nc" id="L110">            message = I18n.l(&quot;analyzer.numeric.message.invalidFloatChar&quot;, invalidChar);</span>
<span class="nc" id="L111">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidFloatChar&quot;);</span>
<span class="nc bnc" id="L112" title="All 4 branches missed.">        } else if (tokenText.contains(&quot;e&quot;) || tokenText.contains(&quot;E&quot;)) {</span>
<span class="nc" id="L113">            message = I18n.l(&quot;analyzer.numeric.message.scientificNotation&quot;);</span>
<span class="nc" id="L114">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.scientificNotation&quot;);</span>
        }
        
<span class="nc" id="L117">        return ErrorResult.builder()</span>
<span class="nc" id="L118">            .ruleId(CaplErrorType.LEXER_NUMERIC_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L119">            .enhancedMessage(message)</span>
<span class="nc" id="L120">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L121">            .suggestion(suggestion)</span>
<span class="nc" id="L122">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L123">            .addMetadata(&quot;numberType&quot;, &quot;floating_point&quot;)</span>
<span class="nc" id="L124">            .addMetadata(&quot;decimalPoints&quot;, countDecimalPoints(tokenText))</span>
<span class="nc" id="L125">            .build();</span>
    }
    
    /**
     * Analyzes integer literal errors.
     */
    private ErrorResult analyzeIntegerError(String tokenText) {
<span class="nc" id="L132">        String message = I18n.l(&quot;analyzer.numeric.message.invalidIntegerChar&quot;, &quot;&quot;);</span>
<span class="nc" id="L133">        String suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidIntegerChar&quot;);</span>

        // Check for specific integer issues
<span class="nc bnc" id="L136" title="All 6 branches missed.">        if (tokenText.startsWith(&quot;0&quot;) &amp;&amp; tokenText.length() &gt; 1 &amp;&amp; !tokenText.startsWith(&quot;0x&quot;)) {</span>
<span class="nc" id="L137">            message = I18n.l(&quot;analyzer.numeric.message.invalidOctal&quot;);</span>
<span class="nc" id="L138">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidOctal&quot;);</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">        } else if (hasInvalidIntegerCharacters(tokenText)) {</span>
<span class="nc" id="L140">            char invalidChar = findInvalidIntegerCharacter(tokenText);</span>
<span class="nc" id="L141">            message = I18n.l(&quot;analyzer.numeric.message.invalidIntegerChar&quot;, invalidChar);</span>
<span class="nc" id="L142">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.invalidIntegerChar&quot;);</span>
<span class="nc bnc" id="L143" title="All 2 branches missed.">        } else if (isNumberTooBig(tokenText)) {</span>
<span class="nc" id="L144">            message = I18n.l(&quot;analyzer.numeric.message.numberTooBig&quot;);</span>
<span class="nc" id="L145">            suggestion = I18n.l(&quot;analyzer.numeric.suggestion.numberTooBig&quot;);</span>
        }
        
<span class="nc" id="L148">        return ErrorResult.builder()</span>
<span class="nc" id="L149">            .ruleId(CaplErrorType.LEXER_NUMERIC_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L150">            .enhancedMessage(message)</span>
<span class="nc" id="L151">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L152">            .suggestion(suggestion)</span>
<span class="nc" id="L153">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L154">            .addMetadata(&quot;numberType&quot;, &quot;integer&quot;)</span>
<span class="nc" id="L155">            .addMetadata(&quot;length&quot;, tokenText.length())</span>
<span class="nc" id="L156">            .build();</span>
    }
    
    /**
     * Creates a generic numeric error result.
     */
    private ErrorResult createGenericNumericError(String message) {
<span class="nc" id="L163">        return ErrorResult.builder()</span>
<span class="nc" id="L164">            .ruleId(CaplErrorType.LEXER_NUMERIC_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L165">            .enhancedMessage(message)</span>
<span class="nc" id="L166">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L167">            .suggestion(I18n.l(&quot;analyzer.numeric.suggestion.genericNumeric&quot;))</span>
<span class="nc" id="L168">            .addMetadata(&quot;errorType&quot;, &quot;generic&quot;)</span>
<span class="nc" id="L169">            .build();</span>
    }
    
    /**
     * Checks if the token looks like a numeric literal.
     */
    private boolean isNumericLiteral(String tokenText) {
<span class="nc bnc" id="L176" title="All 4 branches missed.">        if (tokenText == null || tokenText.isEmpty()) {</span>
<span class="nc" id="L177">            return false;</span>
        }
        
        // Check for various numeric patterns
<span class="nc bnc" id="L181" title="All 2 branches missed.">        return tokenText.matches(&quot;\\d.*&quot;) ||           // Starts with digit</span>
<span class="nc bnc" id="L182" title="All 2 branches missed.">               tokenText.matches(&quot;0[xX].*&quot;) ||         // Hexadecimal</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">               tokenText.startsWith(&quot;.&quot;) ||            // Starts with decimal point</span>
<span class="nc bnc" id="L184" title="All 2 branches missed.">               tokenText.contains(&quot;.&quot;) ||              // Contains decimal point</span>
<span class="nc bnc" id="L185" title="All 2 branches missed.">               tokenText.matches(&quot;.*\\d.*&quot;);           // Contains digits</span>
    }
    
    /**
     * Checks if the token is a hexadecimal literal.
     */
    private boolean isHexadecimalLiteral(String tokenText) {
<span class="nc bnc" id="L192" title="All 4 branches missed.">        return tokenText.startsWith(&quot;0x&quot;) || tokenText.startsWith(&quot;0X&quot;);</span>
    }
    
    /**
     * Checks if the token is a floating-point literal.
     */
    private boolean isFloatingPointLiteral(String tokenText) {
<span class="nc bnc" id="L199" title="All 6 branches missed.">        return tokenText.contains(&quot;.&quot;) || tokenText.contains(&quot;e&quot;) || tokenText.contains(&quot;E&quot;);</span>
    }
    
    /**
     * Checks if the token is an integer literal.
     */
    private boolean isIntegerLiteral(String tokenText) {
<span class="nc bnc" id="L206" title="All 2 branches missed.">        return tokenText.matches(&quot;\\d+&quot;) ||</span>
<span class="nc bnc" id="L207" title="All 6 branches missed.">               (tokenText.startsWith(&quot;0&quot;) &amp;&amp; !tokenText.startsWith(&quot;0x&quot;) &amp;&amp; !tokenText.startsWith(&quot;0X&quot;)) ||</span>
<span class="nc bnc" id="L208" title="All 8 branches missed.">               (tokenText.matches(&quot;\\d.*&quot;) &amp;&amp; !tokenText.contains(&quot;.&quot;) &amp;&amp; !tokenText.contains(&quot;e&quot;) &amp;&amp; !tokenText.contains(&quot;E&quot;));</span>
    }
    
    /**
     * Finds invalid character in hexadecimal number.
     */
    private char findInvalidHexCharacter(String hexPart) {
<span class="nc bnc" id="L215" title="All 2 branches missed.">        for (char ch : hexPart.toCharArray()) {</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">            if (!isValidHexCharacter(ch)) {</span>
<span class="nc" id="L217">                return ch;</span>
            }
        }
<span class="nc" id="L220">        return '\0';</span>
    }
    
    /**
     * Checks if character is valid in hexadecimal number.
     */
    private boolean isValidHexCharacter(char ch) {
<span class="nc bnc" id="L227" title="All 12 branches missed.">        return (ch &gt;= '0' &amp;&amp; ch &lt;= '9') || </span>
               (ch &gt;= 'A' &amp;&amp; ch &lt;= 'F') || 
               (ch &gt;= 'a' &amp;&amp; ch &lt;= 'f');
    }
    
    /**
     * Counts decimal points in the token.
     */
    private int countDecimalPoints(String tokenText) {
<span class="nc" id="L236">        int count = 0;</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">        for (char ch : tokenText.toCharArray()) {</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">            if (ch == '.') {</span>
<span class="nc" id="L239">                count++;</span>
            }
        }
<span class="nc" id="L242">        return count;</span>
    }
    
    /**
     * Checks if floating-point number has invalid characters.
     */
    private boolean hasInvalidFloatCharacters(String tokenText) {
<span class="nc bnc" id="L249" title="All 2 branches missed.">        return findInvalidFloatCharacter(tokenText) != '\0';</span>
    }
    
    /**
     * Finds invalid character in floating-point number.
     */
    private char findInvalidFloatCharacter(String tokenText) {
<span class="nc bnc" id="L256" title="All 2 branches missed.">        for (char ch : tokenText.toCharArray()) {</span>
<span class="nc bnc" id="L257" title="All 12 branches missed.">            if (!Character.isDigit(ch) &amp;&amp; ch != '.' &amp;&amp; ch != 'e' &amp;&amp; ch != 'E' &amp;&amp; ch != '+' &amp;&amp; ch != '-') {</span>
<span class="nc" id="L258">                return ch;</span>
            }
        }
<span class="nc" id="L261">        return '\0';</span>
    }
    
    /**
     * Checks if integer has invalid characters.
     */
    private boolean hasInvalidIntegerCharacters(String tokenText) {
<span class="nc bnc" id="L268" title="All 2 branches missed.">        return findInvalidIntegerCharacter(tokenText) != '\0';</span>
    }
    
    /**
     * Finds invalid character in integer.
     */
    private char findInvalidIntegerCharacter(String tokenText) {
<span class="nc bnc" id="L275" title="All 2 branches missed.">        for (char ch : tokenText.toCharArray()) {</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">            if (!Character.isDigit(ch)) {</span>
<span class="nc" id="L277">                return ch;</span>
            }
        }
<span class="nc" id="L280">        return '\0';</span>
    }
    
    /**
     * Checks if the number is too big for typical integer types.
     */
    private boolean isNumberTooBig(String tokenText) {
<span class="nc bnc" id="L287" title="All 2 branches missed.">        if (tokenText.length() &gt; 10) { // More than 10 digits is likely too big for int</span>
<span class="nc" id="L288">            return true;</span>
        }
        
        try {
<span class="nc" id="L292">            Long.parseLong(tokenText);</span>
<span class="nc" id="L293">            return false;</span>
<span class="nc" id="L294">        } catch (NumberFormatException e) {</span>
<span class="nc" id="L295">            return true;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>