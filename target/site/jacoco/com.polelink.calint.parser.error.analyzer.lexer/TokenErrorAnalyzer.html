<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TokenErrorAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_class">TokenErrorAnalyzer</span></div><h1>TokenErrorAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">619 of 619</td><td class="ctr2">0%</td><td class="bar">83 of 83</td><td class="ctr2">0%</td><td class="ctr1">59</td><td class="ctr2">59</td><td class="ctr1">142</td><td class="ctr2">142</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a4"><a href="TokenErrorAnalyzer.java.html#L155" class="el_method">analyzeNumericLiteralError(RecognitionException, Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="130" alt="130"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h0">29</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="TokenErrorAnalyzer.java.html#L201" class="el_method">analyzeCaplTokenError(RecognitionException, Object)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="90" alt="90"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f5">6</td><td class="ctr2" id="g5">6</td><td class="ctr1" id="h1">23</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="TokenErrorAnalyzer.java.html#L121" class="el_method">analyzeStringLiteralError(RecognitionException, Object)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="83" alt="83"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="TokenErrorAnalyzer.java.html#L238" class="el_method">analyzeGenericLexerError(RecognitionException, Object)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="58" alt="58"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h4">13</td><td class="ctr2" id="i4">13</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="TokenErrorAnalyzer.java.html#L293" class="el_method">generateUnrecognizedTokenSuggestion(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="53" alt="53"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="11" alt="11"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="TokenErrorAnalyzer.java.html#L71" class="el_method">analyzeInvalidCharacter(RecognitionException, Object)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="51" alt="51"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h3">15</td><td class="ctr2" id="i3">15</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="TokenErrorAnalyzer.java.html#L37" class="el_method">analyzeLexerError(RecognitionException, Object)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="50" alt="50"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h7">9</td><td class="ctr2" id="i7">9</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="TokenErrorAnalyzer.java.html#L265" class="el_method">generateInvalidCharacterSuggestion(char)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="50" alt="50"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="TokenErrorAnalyzer.java.html#L100" class="el_method">analyzeUnrecognizedToken(RecognitionException, Object)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h6">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="TokenErrorAnalyzer.java.html#L167" class="el_method">lambda$analyzeNumericLiteralError$0(int)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="7" alt="7"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a12"><a href="TokenErrorAnalyzer.java.html#L26" class="el_method">TokenErrorAnalyzer()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="TokenErrorAnalyzer.java.html#L23" class="el_method">static {...}</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="TokenErrorAnalyzer.java.html#L32" class="el_method">canAnalyzeLexerError(ErrorContext)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>