<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LexerErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">LexerErrorAnalyzer.java</span></div><h1>LexerErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.parser.error.analyzer.AbstractErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.LexerNoViableAltException;
import org.antlr.v4.runtime.RecognitionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * Abstract base class for lexer error analyzers.
 *
 * This class provides common functionality for analyzing lexical errors
 * that occur during the tokenization phase of CAPL parsing.
 */
public abstract class LexerErrorAnalyzer extends AbstractErrorAnalyzer {

<span class="nc" id="L23">    private static final Logger LOGGER = LoggerFactory.getLogger(LexerErrorAnalyzer.class);</span>

    /**
     * Constructor for LexerErrorAnalyzer.
     *
     * @param analyzerName the name of this analyzer
     * @param priority the priority of this analyzer
     * @param supportedContexts the set of contexts this analyzer supports
     */
    protected LexerErrorAnalyzer(String analyzerName, int priority, Set&lt;CaplParseContext&gt; supportedContexts) {
<span class="nc" id="L33">        super(analyzerName, AnalyzerType.LEXER, priority, supportedContexts);</span>
<span class="nc" id="L34">    }</span>

    /**
     * Constructor with default priority.
     *
     * @param analyzerName the name of this analyzer
     * @param supportedContexts the set of contexts this analyzer supports
     */
    protected LexerErrorAnalyzer(String analyzerName, Set&lt;CaplParseContext&gt; supportedContexts) {
<span class="nc" id="L43">        super(analyzerName, AnalyzerType.LEXER, supportedContexts);</span>
<span class="nc" id="L44">    }</span>

    @Override
    protected final boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc bnc" id="L48" title="All 2 branches missed.">        if (!context.isLexerError()) {</span>
<span class="nc" id="L49">            return false;</span>
        }

<span class="nc" id="L52">        return canAnalyzeLexerError(context);</span>
    }

    @Override
    protected final ErrorResult analyzeSpecific(ErrorContext context) {
<span class="nc" id="L57">        RecognitionException exception = context.getException();</span>
<span class="nc" id="L58">        Object offendingSymbol = context.getOffendingSymbol();</span>

<span class="nc" id="L60">        return analyzeLexerError(exception, offendingSymbol);</span>
    }

    /**
     * Checks if this analyzer can handle the specific lexer error.
     *
     * @param context the error context
     * @return true if this analyzer can handle the error
     */
    protected abstract boolean canAnalyzeLexerError(ErrorContext context);

    /**
     * Analyzes the lexer error and returns an enhanced error result.
     *
     * @param exception the recognition exception that occurred
     * @param offendingSymbol the symbol that caused the error
     * @return the analysis result
     */
    protected abstract ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol);

    /**
     * Extracts the text of the offending symbol.
     *
     * @param offendingSymbol the offending symbol object
     * @return the text representation of the symbol, or null if not available
     */
    protected String extractOffendingSymbolText(Object offendingSymbol) {
<span class="nc bnc" id="L87" title="All 2 branches missed.">        if (offendingSymbol == null) {</span>
<span class="nc" id="L88">            return null;</span>
        }

<span class="nc bnc" id="L91" title="All 2 branches missed.">        if (offendingSymbol instanceof org.antlr.v4.runtime.Token) {</span>
<span class="nc" id="L92">            org.antlr.v4.runtime.Token token = (org.antlr.v4.runtime.Token) offendingSymbol;</span>
<span class="nc" id="L93">            return token.getText();</span>
        }

<span class="nc" id="L96">        return offendingSymbol.toString();</span>
    }

    /**
     * Extracts the bad character from a LexerNoViableAltException.
     *
     * @param exception the lexer exception
     * @return the bad character, or null character if not available
     */
    protected char extractBadCharacter(LexerNoViableAltException exception) {
<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (exception == null) {</span>
<span class="nc" id="L107">            return '\0';</span>
        }

        try {
            // Get the character at the error position
<span class="nc" id="L112">            org.antlr.v4.runtime.CharStream input = exception.getInputStream();</span>
<span class="nc bnc" id="L113" title="All 6 branches missed.">            if (input != null &amp;&amp; exception.getStartIndex() &gt;= 0 &amp;&amp; exception.getStartIndex() &lt; input.size()) {</span>
<span class="nc" id="L114">                return (char) input.LA(1); // Look ahead 1 character</span>
            }
<span class="nc" id="L116">        } catch (Exception e) {</span>
<span class="nc" id="L117">            LOGGER.debug(&quot;Failed to extract bad character from exception: {}&quot;, e.getMessage());</span>
<span class="nc" id="L118">        }</span>

<span class="nc" id="L120">        return '\0';</span>
    }

    /**
     * Checks if the character represents end of file.
     *
     * @param ch the character to check
     * @return true if the character represents EOF
     */
    protected boolean isEOF(char ch) {
<span class="nc bnc" id="L130" title="All 4 branches missed.">        return ch == '\0' || ch == (char) -1;</span>
    }

    /**
     * Formats a character for display in error messages.
     *
     * @param ch the character to format
     * @return a formatted string representation of the character
     */
    protected String formatCharacterForDisplay(char ch) {
<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (isEOF(ch)) {</span>
<span class="nc" id="L141">            return &quot;&lt;EOF&gt;&quot;;</span>
        }

<span class="nc bnc" id="L144" title="All 6 branches missed.">        switch (ch) {</span>
            case '\n':
<span class="nc" id="L146">                return &quot;\\n&quot;;</span>
            case '\r':
<span class="nc" id="L148">                return &quot;\\r&quot;;</span>
            case '\t':
<span class="nc" id="L150">                return &quot;\\t&quot;;</span>
            case ' ':
<span class="nc" id="L152">                return &quot;&lt;space&gt;&quot;;</span>
            case '\0':
<span class="nc" id="L154">                return &quot;&lt;null&gt;&quot;;</span>
            default:
<span class="nc bnc" id="L156" title="All 2 branches missed.">                if (Character.isISOControl(ch)) {</span>
<span class="nc" id="L157">                    return String.format(&quot;\\u%04x&quot;, (int) ch);</span>
                } else {
<span class="nc" id="L159">                    return &quot;'&quot; + ch + &quot;'&quot;;</span>
                }
        }
    }

    /**
     * Checks if the exception is a LexerNoViableAltException.
     *
     * @param exception the exception to check
     * @return true if it's a LexerNoViableAltException
     */
    protected boolean isNoViableAltException(RecognitionException exception) {
<span class="nc" id="L171">        return exception instanceof LexerNoViableAltException;</span>
    }

    /**
     * Gets the lexer error category based on the exception type.
     *
     * @param exception the recognition exception
     * @return the error category
     */
    protected LexerErrorCategory getErrorCategory(RecognitionException exception) {
<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (exception instanceof LexerNoViableAltException) {</span>
<span class="nc" id="L182">            return LexerErrorCategory.INVALID_CHARACTER;</span>
        }

        // For other types of lexer exceptions, we need to analyze the context
<span class="nc" id="L186">        return LexerErrorCategory.UNRECOGNIZED_TOKEN;</span>
    }

    /**
     * Gets the lexer error category based on the exception type and offending symbol.
     * This is an enhanced version that considers the offending symbol content.
     *
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the error category
     */
    protected LexerErrorCategory getErrorCategory(RecognitionException exception, Object offendingSymbol) {
        // First check exception type
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (exception instanceof LexerNoViableAltException) {</span>
<span class="nc" id="L200">            return LexerErrorCategory.INVALID_CHARACTER;</span>
        }

        // Then analyze the offending symbol content
<span class="nc" id="L204">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">        if (tokenText != null) {</span>
            // Check for string literal patterns
<span class="nc bnc" id="L207" title="All 4 branches missed.">            if (tokenText.startsWith(&quot;\&quot;&quot;) || tokenText.startsWith(&quot;'&quot;) ||</span>
<span class="nc bnc" id="L208" title="All 4 branches missed.">                tokenText.contains(&quot;\\&quot;) || hasStringLiteralPattern(tokenText)) {</span>
<span class="nc" id="L209">                return LexerErrorCategory.STRING_LITERAL_ERROR;</span>
            }

            // Check for numeric literal patterns
<span class="nc bnc" id="L213" title="All 2 branches missed.">            if (isNumericLiteralPattern(tokenText)) {</span>
<span class="nc" id="L214">                return LexerErrorCategory.NUMERIC_LITERAL_ERROR;</span>
            }

            // Check for CAPL-specific token patterns
<span class="nc bnc" id="L218" title="All 2 branches missed.">            if (isCaplTokenPattern(tokenText)) {</span>
<span class="nc" id="L219">                return LexerErrorCategory.CAPL_TOKEN_ERROR;</span>
            }

            // If it's a recognizable token pattern, treat as unrecognized token
<span class="nc" id="L223">            return LexerErrorCategory.UNRECOGNIZED_TOKEN;</span>
        }

        // If no offending symbol or it's null, treat as generic lexer error
<span class="nc" id="L227">        return LexerErrorCategory.GENERIC_ERROR;</span>
    }

    /**
     * Checks if the token has string literal patterns.
     */
    private boolean hasStringLiteralPattern(String tokenText) {
<span class="nc bnc" id="L234" title="All 4 branches missed.">        return tokenText.contains(&quot;\&quot;&quot;) || tokenText.contains(&quot;'&quot;) ||</span>
<span class="nc bnc" id="L235" title="All 2 branches missed.">               tokenText.contains(&quot;\\&quot;);</span>
    }

    /**
     * Checks if the token matches numeric literal patterns.
     */
    private boolean isNumericLiteralPattern(String tokenText) {
<span class="nc bnc" id="L242" title="All 4 branches missed.">        if (tokenText == null || tokenText.isEmpty()) {</span>
<span class="nc" id="L243">            return false;</span>
        }

        // Check for hex patterns (including incomplete ones like &quot;0x&quot;)
<span class="nc bnc" id="L247" title="All 4 branches missed.">        if (tokenText.startsWith(&quot;0x&quot;) || tokenText.startsWith(&quot;0X&quot;)) {</span>
<span class="nc" id="L248">            return true;</span>
        }

        // Check for floating point patterns (including invalid ones like &quot;3.14.15&quot;)
<span class="nc bnc" id="L252" title="All 2 branches missed.">        if (tokenText.contains(&quot;.&quot;)) {</span>
            // Count decimal points
<span class="nc bnc" id="L254" title="All 2 branches missed.">            long decimalCount = tokenText.chars().filter(ch -&gt; ch == '.').count();</span>
<span class="nc bnc" id="L255" title="All 4 branches missed.">            if (decimalCount &gt; 1 || tokenText.matches(&quot;.*\\d.*&quot;)) {</span>
<span class="nc" id="L256">                return true;</span>
            }
        }

        // Check for invalid integer patterns (digits mixed with letters)
<span class="nc bnc" id="L261" title="All 4 branches missed.">        if (tokenText.matches(&quot;\\d+[a-zA-Z]+.*&quot;) || tokenText.matches(&quot;.*[a-zA-Z]+\\d+.*&quot;)) {</span>
<span class="nc" id="L262">            return true;</span>
        }

        // Check for pure digit sequences that might be malformed
<span class="nc bnc" id="L266" title="All 4 branches missed.">        if (tokenText.matches(&quot;\\d+&quot;) &amp;&amp; tokenText.length() &gt; 10) { // Very long numbers might be errors</span>
<span class="nc" id="L267">            return true;</span>
        }

<span class="nc" id="L270">        return false;</span>
    }

    /**
     * Checks if the token matches CAPL-specific patterns.
     */
    private boolean isCaplTokenPattern(String tokenText) {
<span class="nc bnc" id="L277" title="All 4 branches missed.">        if (tokenText == null || tokenText.isEmpty()) {</span>
<span class="nc" id="L278">            return false;</span>
        }

        // System variable references
<span class="nc bnc" id="L282" title="All 2 branches missed.">        if (tokenText.startsWith(&quot;@&quot;)) {</span>
<span class="nc" id="L283">            return true;</span>
        }

        // Signal references
<span class="nc bnc" id="L287" title="All 2 branches missed.">        if (tokenText.startsWith(&quot;$&quot;)) {</span>
<span class="nc" id="L288">            return true;</span>
        }

<span class="nc" id="L291">        return false;</span>
    }

    /**
     * Enumeration of lexer error categories.
     */
<span class="nc" id="L297">    protected enum LexerErrorCategory {</span>
<span class="nc" id="L298">        INVALID_CHARACTER,</span>
<span class="nc" id="L299">        UNRECOGNIZED_TOKEN,</span>
<span class="nc" id="L300">        STRING_LITERAL_ERROR,</span>
<span class="nc" id="L301">        NUMERIC_LITERAL_ERROR,</span>
<span class="nc" id="L302">        CAPL_TOKEN_ERROR,</span>
<span class="nc" id="L303">        GENERIC_ERROR</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>