<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UnrecognizedTokenAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">UnrecognizedTokenAnalyzer.java</span></div><h1>UnrecognizedTokenAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.LexerNoViableAltException;
import org.antlr.v4.runtime.RecognitionException;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Analyzer for unrecognized token errors.
 * 
 * This analyzer handles cases where the lexer cannot form a valid token
 * from the input characters, typically for misspelled keywords or
 * invalid token combinations.
 */
public class UnrecognizedTokenAnalyzer extends LexerErrorAnalyzer {
    
    // Common CAPL keyword misspellings and their corrections
<span class="nc" id="L25">    private static final Map&lt;String, String&gt; KEYWORD_CORRECTIONS = new HashMap&lt;&gt;();</span>
    
    static {
        // Type keywords
<span class="nc" id="L29">        KEYWORD_CORRECTIONS.put(&quot;mesage&quot;, &quot;message&quot;);</span>
<span class="nc" id="L30">        KEYWORD_CORRECTIONS.put(&quot;messag&quot;, &quot;message&quot;);</span>
<span class="nc" id="L31">        KEYWORD_CORRECTIONS.put(&quot;singal&quot;, &quot;signal&quot;);</span>
<span class="nc" id="L32">        KEYWORD_CORRECTIONS.put(&quot;signl&quot;, &quot;signal&quot;);</span>
<span class="nc" id="L33">        KEYWORD_CORRECTIONS.put(&quot;timer&quot;, &quot;msTimer&quot;);</span>
<span class="nc" id="L34">        KEYWORD_CORRECTIONS.put(&quot;tmr&quot;, &quot;msTimer&quot;);</span>
        
        // Control flow keywords
<span class="nc" id="L37">        KEYWORD_CORRECTIONS.put(&quot;fi&quot;, &quot;if&quot;);</span>
<span class="nc" id="L38">        KEYWORD_CORRECTIONS.put(&quot;esle&quot;, &quot;else&quot;);</span>
<span class="nc" id="L39">        KEYWORD_CORRECTIONS.put(&quot;els&quot;, &quot;else&quot;);</span>
<span class="nc" id="L40">        KEYWORD_CORRECTIONS.put(&quot;whil&quot;, &quot;while&quot;);</span>
<span class="nc" id="L41">        KEYWORD_CORRECTIONS.put(&quot;wile&quot;, &quot;while&quot;);</span>
<span class="nc" id="L42">        KEYWORD_CORRECTIONS.put(&quot;fo&quot;, &quot;for&quot;);</span>
<span class="nc" id="L43">        KEYWORD_CORRECTIONS.put(&quot;forech&quot;, &quot;foreach&quot;);</span>
<span class="nc" id="L44">        KEYWORD_CORRECTIONS.put(&quot;swich&quot;, &quot;switch&quot;);</span>
<span class="nc" id="L45">        KEYWORD_CORRECTIONS.put(&quot;swithc&quot;, &quot;switch&quot;);</span>
<span class="nc" id="L46">        KEYWORD_CORRECTIONS.put(&quot;cas&quot;, &quot;case&quot;);</span>
<span class="nc" id="L47">        KEYWORD_CORRECTIONS.put(&quot;defalt&quot;, &quot;default&quot;);</span>
<span class="nc" id="L48">        KEYWORD_CORRECTIONS.put(&quot;defualt&quot;, &quot;default&quot;);</span>
<span class="nc" id="L49">        KEYWORD_CORRECTIONS.put(&quot;brak&quot;, &quot;break&quot;);</span>
<span class="nc" id="L50">        KEYWORD_CORRECTIONS.put(&quot;breka&quot;, &quot;break&quot;);</span>
<span class="nc" id="L51">        KEYWORD_CORRECTIONS.put(&quot;continu&quot;, &quot;continue&quot;);</span>
<span class="nc" id="L52">        KEYWORD_CORRECTIONS.put(&quot;contin&quot;, &quot;continue&quot;);</span>
<span class="nc" id="L53">        KEYWORD_CORRECTIONS.put(&quot;retrn&quot;, &quot;return&quot;);</span>
<span class="nc" id="L54">        KEYWORD_CORRECTIONS.put(&quot;retrun&quot;, &quot;return&quot;);</span>
        
        // Type keywords
<span class="nc" id="L57">        KEYWORD_CORRECTIONS.put(&quot;itn&quot;, &quot;int&quot;);</span>
<span class="nc" id="L58">        KEYWORD_CORRECTIONS.put(&quot;iteger&quot;, &quot;int&quot;);</span>
<span class="nc" id="L59">        KEYWORD_CORRECTIONS.put(&quot;flot&quot;, &quot;float&quot;);</span>
<span class="nc" id="L60">        KEYWORD_CORRECTIONS.put(&quot;flaot&quot;, &quot;float&quot;);</span>
<span class="nc" id="L61">        KEYWORD_CORRECTIONS.put(&quot;doubl&quot;, &quot;double&quot;);</span>
<span class="nc" id="L62">        KEYWORD_CORRECTIONS.put(&quot;dubble&quot;, &quot;double&quot;);</span>
<span class="nc" id="L63">        KEYWORD_CORRECTIONS.put(&quot;chr&quot;, &quot;char&quot;);</span>
<span class="nc" id="L64">        KEYWORD_CORRECTIONS.put(&quot;cahre&quot;, &quot;char&quot;);</span>
<span class="nc" id="L65">        KEYWORD_CORRECTIONS.put(&quot;byt&quot;, &quot;byte&quot;);</span>
<span class="nc" id="L66">        KEYWORD_CORRECTIONS.put(&quot;wrd&quot;, &quot;word&quot;);</span>
<span class="nc" id="L67">        KEYWORD_CORRECTIONS.put(&quot;dwrd&quot;, &quot;dword&quot;);</span>
<span class="nc" id="L68">        KEYWORD_CORRECTIONS.put(&quot;qwrd&quot;, &quot;qword&quot;);</span>
<span class="nc" id="L69">        KEYWORD_CORRECTIONS.put(&quot;vod&quot;, &quot;void&quot;);</span>
<span class="nc" id="L70">        KEYWORD_CORRECTIONS.put(&quot;viod&quot;, &quot;void&quot;);</span>
        
        // CAPL-specific keywords
<span class="nc" id="L73">        KEYWORD_CORRECTIONS.put(&quot;includ&quot;, &quot;#include&quot;);</span>
<span class="nc" id="L74">        KEYWORD_CORRECTIONS.put(&quot;incude&quot;, &quot;#include&quot;);</span>
<span class="nc" id="L75">        KEYWORD_CORRECTIONS.put(&quot;defin&quot;, &quot;#define&quot;);</span>
<span class="nc" id="L76">        KEYWORD_CORRECTIONS.put(&quot;defne&quot;, &quot;#define&quot;);</span>
<span class="nc" id="L77">        KEYWORD_CORRECTIONS.put(&quot;pragm&quot;, &quot;#pragma&quot;);</span>
<span class="nc" id="L78">        KEYWORD_CORRECTIONS.put(&quot;prgma&quot;, &quot;#pragma&quot;);</span>
        
        // Event keywords
<span class="nc" id="L81">        KEYWORD_CORRECTIONS.put(&quot;o&quot;, &quot;on&quot;);</span>
<span class="nc" id="L82">        KEYWORD_CORRECTIONS.put(&quot;sysva&quot;, &quot;sysvar&quot;);</span>
<span class="nc" id="L83">        KEYWORD_CORRECTIONS.put(&quot;sysvr&quot;, &quot;sysvar&quot;);</span>
<span class="nc" id="L84">        KEYWORD_CORRECTIONS.put(&quot;envva&quot;, &quot;envvar&quot;);</span>
<span class="nc" id="L85">        KEYWORD_CORRECTIONS.put(&quot;envvr&quot;, &quot;envvar&quot;);</span>
        
        // Bus-specific keywords
<span class="nc" id="L88">        KEYWORD_CORRECTIONS.put(&quot;flexra&quot;, &quot;flexray&quot;);</span>
<span class="nc" id="L89">        KEYWORD_CORRECTIONS.put(&quot;flexry&quot;, &quot;flexray&quot;);</span>
<span class="nc" id="L90">        KEYWORD_CORRECTIONS.put(&quot;frfram&quot;, &quot;frFrame&quot;);</span>
<span class="nc" id="L91">        KEYWORD_CORRECTIONS.put(&quot;frfame&quot;, &quot;frFrame&quot;);</span>
<span class="nc" id="L92">        KEYWORD_CORRECTIONS.put(&quot;frerro&quot;, &quot;frError&quot;);</span>
<span class="nc" id="L93">        KEYWORD_CORRECTIONS.put(&quot;frerr&quot;, &quot;frError&quot;);</span>
<span class="nc" id="L94">        KEYWORD_CORRECTIONS.put(&quot;frpd&quot;, &quot;frPDU&quot;);</span>
<span class="nc" id="L95">        KEYWORD_CORRECTIONS.put(&quot;frpdu&quot;, &quot;frPDU&quot;);</span>
<span class="nc" id="L96">        KEYWORD_CORRECTIONS.put(&quot;linfram&quot;, &quot;linFrame&quot;);</span>
<span class="nc" id="L97">        KEYWORD_CORRECTIONS.put(&quot;linfame&quot;, &quot;linFrame&quot;);</span>
        
        // Diagnostic keywords
<span class="nc" id="L100">        KEYWORD_CORRECTIONS.put(&quot;diagreq&quot;, &quot;diagRequest&quot;);</span>
<span class="nc" id="L101">        KEYWORD_CORRECTIONS.put(&quot;diagrequest&quot;, &quot;diagRequest&quot;);</span>
<span class="nc" id="L102">        KEYWORD_CORRECTIONS.put(&quot;diagres&quot;, &quot;diagResponse&quot;);</span>
<span class="nc" id="L103">        KEYWORD_CORRECTIONS.put(&quot;diagresponse&quot;, &quot;diagResponse&quot;);</span>
        
        // Other keywords
<span class="nc" id="L106">        KEYWORD_CORRECTIONS.put(&quot;const&quot;, &quot;const&quot;);</span>
<span class="nc" id="L107">        KEYWORD_CORRECTIONS.put(&quot;conts&quot;, &quot;const&quot;);</span>
<span class="nc" id="L108">        KEYWORD_CORRECTIONS.put(&quot;stati&quot;, &quot;static&quot;);</span>
<span class="nc" id="L109">        KEYWORD_CORRECTIONS.put(&quot;statc&quot;, &quot;static&quot;);</span>
<span class="nc" id="L110">        KEYWORD_CORRECTIONS.put(&quot;publi&quot;, &quot;public&quot;);</span>
<span class="nc" id="L111">        KEYWORD_CORRECTIONS.put(&quot;publc&quot;, &quot;public&quot;);</span>
<span class="nc" id="L112">        KEYWORD_CORRECTIONS.put(&quot;privat&quot;, &quot;private&quot;);</span>
<span class="nc" id="L113">        KEYWORD_CORRECTIONS.put(&quot;privte&quot;, &quot;private&quot;);</span>
<span class="nc" id="L114">    }</span>
    
    public UnrecognizedTokenAnalyzer() {
<span class="nc" id="L117">        super(&quot;UnrecognizedTokenAnalyzer&quot;, 100, Set.of()); // Lower priority than InvalidCharacterAnalyzer</span>
<span class="nc" id="L118">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
<span class="nc" id="L122">        RecognitionException exception = context.getException();</span>
        
        // Handle cases that are not LexerNoViableAltException
        // or cases where we have some token text to analyze
<span class="nc bnc" id="L126" title="All 2 branches missed.">        return !(exception instanceof LexerNoViableAltException) || </span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">               extractOffendingSymbolText(context.getOffendingSymbol()) != null;</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L132">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
        
<span class="nc bnc" id="L134" title="All 4 branches missed.">        if (tokenText == null || tokenText.isEmpty()) {</span>
<span class="nc" id="L135">            return analyzeEmptyToken(exception);</span>
        }
        
        // Check for keyword misspellings
<span class="nc" id="L139">        String correction = findKeywordCorrection(tokenText);</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (correction != null) {</span>
<span class="nc" id="L141">            return analyzeKeywordMisspelling(tokenText, correction);</span>
        }
        
        // Check for common token patterns
<span class="nc bnc" id="L145" title="All 2 branches missed.">        if (isLikelyIdentifier(tokenText)) {</span>
<span class="nc" id="L146">            return analyzeIdentifierLikeToken(tokenText);</span>
        }
        
<span class="nc bnc" id="L149" title="All 2 branches missed.">        if (isLikelyNumber(tokenText)) {</span>
<span class="nc" id="L150">            return analyzeNumberLikeToken(tokenText);</span>
        }
        
<span class="nc bnc" id="L153" title="All 2 branches missed.">        if (isLikelyString(tokenText)) {</span>
<span class="nc" id="L154">            return analyzeStringLikeToken(tokenText);</span>
        }
        
        // Generic unrecognized token
<span class="nc" id="L158">        return analyzeGenericUnrecognizedToken(tokenText);</span>
    }
    
    /**
     * Analyzes empty or null token errors.
     */
    private ErrorResult analyzeEmptyToken(RecognitionException exception) {
<span class="nc" id="L165">        String message = I18n.l(&quot;analyzer.unrecognized.message.genericUnrecognized&quot;);</span>
<span class="nc" id="L166">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.genericUnrecognized&quot;);</span>
        
<span class="nc" id="L168">        return ErrorResult.builder()</span>
<span class="nc" id="L169">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L170">            .enhancedMessage(message)</span>
<span class="nc" id="L171">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L172">            .suggestion(suggestion)</span>
<span class="nc" id="L173">            .addMetadata(&quot;tokenType&quot;, &quot;empty&quot;)</span>
<span class="nc" id="L174">            .build();</span>
    }
    
    /**
     * Analyzes keyword misspelling errors.
     */
    private ErrorResult analyzeKeywordMisspelling(String tokenText, String correction) {
<span class="nc" id="L181">        String message = I18n.l(&quot;analyzer.unrecognized.message.misspelledKeyword&quot;, tokenText);</span>
<span class="nc" id="L182">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.misspelledKeyword&quot;, correction);</span>
        
<span class="nc" id="L184">        return ErrorResult.builder()</span>
<span class="nc" id="L185">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L186">            .enhancedMessage(message)</span>
<span class="nc" id="L187">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L188">            .suggestion(suggestion)</span>
<span class="nc" id="L189">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L190">            .addMetadata(&quot;suggestedCorrection&quot;, correction)</span>
<span class="nc" id="L191">            .addMetadata(&quot;errorType&quot;, &quot;keyword_misspelling&quot;)</span>
<span class="nc" id="L192">            .build();</span>
    }
    
    /**
     * Analyzes identifier-like tokens.
     */
    private ErrorResult analyzeIdentifierLikeToken(String tokenText) {
<span class="nc" id="L199">        String message = I18n.l(&quot;analyzer.unrecognized.message.unrecognizedToken&quot;, tokenText);</span>
<span class="nc" id="L200">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.unrecognizedToken&quot;);</span>

        // Check for common identifier issues
<span class="nc bnc" id="L203" title="All 2 branches missed.">        if (tokenText.contains(&quot;-&quot;)) {</span>
<span class="nc" id="L204">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.identifierHyphens&quot;);</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">        } else if (tokenText.matches(&quot;\\d.*&quot;)) {</span>
<span class="nc" id="L206">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.identifierStartsWithDigit&quot;);</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">        } else if (tokenText.contains(&quot; &quot;)) {</span>
<span class="nc" id="L208">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.identifierSpaces&quot;);</span>
        }
        
<span class="nc" id="L211">        return ErrorResult.builder()</span>
<span class="nc" id="L212">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L213">            .enhancedMessage(message)</span>
<span class="nc" id="L214">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L215">            .suggestion(suggestion)</span>
<span class="nc" id="L216">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L217">            .addMetadata(&quot;errorType&quot;, &quot;identifier_like&quot;)</span>
<span class="nc" id="L218">            .build();</span>
    }
    
    /**
     * Analyzes number-like tokens.
     */
    private ErrorResult analyzeNumberLikeToken(String tokenText) {
<span class="nc" id="L225">        String message = I18n.l(&quot;analyzer.unrecognized.message.invalidNumberFormat&quot;, tokenText);</span>
<span class="nc" id="L226">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.checkNumberFormat&quot;);</span>

<span class="nc bnc" id="L228" title="All 4 branches missed.">        if (tokenText.startsWith(&quot;0x&quot;) || tokenText.startsWith(&quot;0X&quot;)) {</span>
<span class="nc" id="L229">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.hexNumberFormat&quot;);</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">        } else if (tokenText.contains(&quot;.&quot;)) {</span>
<span class="nc" id="L231">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.decimalPointPlacement&quot;);</span>
        } else {
<span class="nc" id="L233">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.numbersOnlyDigits&quot;);</span>
        }
        
<span class="nc" id="L236">        return ErrorResult.builder()</span>
<span class="nc" id="L237">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L238">            .enhancedMessage(message)</span>
<span class="nc" id="L239">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L240">            .suggestion(suggestion)</span>
<span class="nc" id="L241">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L242">            .addMetadata(&quot;errorType&quot;, &quot;number_like&quot;)</span>
<span class="nc" id="L243">            .build();</span>
    }
    
    /**
     * Analyzes string-like tokens.
     */
    private ErrorResult analyzeStringLikeToken(String tokenText) {
<span class="nc" id="L250">        String message = I18n.l(&quot;analyzer.unrecognized.message.invalidStringLiteral&quot;, tokenText);</span>
<span class="nc" id="L251">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.checkStringLiteralSyntax&quot;);</span>

<span class="nc bnc" id="L253" title="All 4 branches missed.">        if (tokenText.startsWith(&quot;\&quot;&quot;) &amp;&amp; !tokenText.endsWith(&quot;\&quot;&quot;)) {</span>
<span class="nc" id="L254">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.stringMissingClosingQuote&quot;);</span>
<span class="nc bnc" id="L255" title="All 4 branches missed.">        } else if (tokenText.startsWith(&quot;'&quot;) &amp;&amp; !tokenText.endsWith(&quot;'&quot;)) {</span>
<span class="nc" id="L256">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.charMissingClosingQuote&quot;);</span>
<span class="nc bnc" id="L257" title="All 2 branches missed.">        } else if (tokenText.contains(&quot;\\&quot;)) {</span>
<span class="nc" id="L258">            suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.checkEscapeSequences&quot;);</span>
        }
        
<span class="nc" id="L261">        return ErrorResult.builder()</span>
<span class="nc" id="L262">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L263">            .enhancedMessage(message)</span>
<span class="nc" id="L264">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L265">            .suggestion(suggestion)</span>
<span class="nc" id="L266">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L267">            .addMetadata(&quot;errorType&quot;, &quot;string_like&quot;)</span>
<span class="nc" id="L268">            .build();</span>
    }
    
    /**
     * Analyzes generic unrecognized tokens.
     */
    private ErrorResult analyzeGenericUnrecognizedToken(String tokenText) {
<span class="nc" id="L275">        String message = I18n.l(&quot;analyzer.unrecognized.message.unrecognizedToken&quot;, tokenText);</span>
<span class="nc" id="L276">        String suggestion = I18n.l(&quot;analyzer.unrecognized.suggestion.unrecognizedToken&quot;);</span>
        
<span class="nc" id="L278">        return ErrorResult.builder()</span>
<span class="nc" id="L279">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L280">            .enhancedMessage(message)</span>
<span class="nc" id="L281">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L282">            .suggestion(suggestion)</span>
<span class="nc" id="L283">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L284">            .addMetadata(&quot;errorType&quot;, &quot;generic&quot;)</span>
<span class="nc" id="L285">            .build();</span>
    }
    
    /**
     * Finds a keyword correction for the given token.
     */
    private String findKeywordCorrection(String tokenText) {
<span class="nc bnc" id="L292" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L293">            return null;</span>
        }
        
        // Direct lookup
<span class="nc" id="L297">        String correction = KEYWORD_CORRECTIONS.get(tokenText.toLowerCase());</span>
<span class="nc bnc" id="L298" title="All 2 branches missed.">        if (correction != null) {</span>
<span class="nc" id="L299">            return correction;</span>
        }
        
        // Fuzzy matching for close spellings
<span class="nc" id="L303">        return findClosestKeyword(tokenText);</span>
    }
    
    /**
     * Finds the closest keyword using simple edit distance.
     */
    private String findClosestKeyword(String tokenText) {
<span class="nc" id="L310">        String closest = null;</span>
<span class="nc" id="L311">        int minDistance = Integer.MAX_VALUE;</span>
        
<span class="nc bnc" id="L313" title="All 2 branches missed.">        for (String keyword : KEYWORD_CORRECTIONS.keySet()) {</span>
<span class="nc" id="L314">            int distance = calculateEditDistance(tokenText.toLowerCase(), keyword);</span>
<span class="nc bnc" id="L315" title="All 4 branches missed.">            if (distance &lt;= 2 &amp;&amp; distance &lt; minDistance) { // Allow up to 2 character differences</span>
<span class="nc" id="L316">                minDistance = distance;</span>
<span class="nc" id="L317">                closest = KEYWORD_CORRECTIONS.get(keyword);</span>
            }
<span class="nc" id="L319">        }</span>
        
<span class="nc" id="L321">        return closest;</span>
    }
    
    /**
     * Calculates simple edit distance between two strings.
     */
    private int calculateEditDistance(String s1, String s2) {
<span class="nc" id="L328">        int len1 = s1.length();</span>
<span class="nc" id="L329">        int len2 = s2.length();</span>
        
<span class="nc bnc" id="L331" title="All 2 branches missed.">        if (len1 == 0) return len2;</span>
<span class="nc bnc" id="L332" title="All 2 branches missed.">        if (len2 == 0) return len1;</span>
        
<span class="nc" id="L334">        int[][] dp = new int[len1 + 1][len2 + 1];</span>
        
<span class="nc bnc" id="L336" title="All 2 branches missed.">        for (int i = 0; i &lt;= len1; i++) {</span>
<span class="nc" id="L337">            dp[i][0] = i;</span>
        }
<span class="nc bnc" id="L339" title="All 2 branches missed.">        for (int j = 0; j &lt;= len2; j++) {</span>
<span class="nc" id="L340">            dp[0][j] = j;</span>
        }
        
<span class="nc bnc" id="L343" title="All 2 branches missed.">        for (int i = 1; i &lt;= len1; i++) {</span>
<span class="nc bnc" id="L344" title="All 2 branches missed.">            for (int j = 1; j &lt;= len2; j++) {</span>
<span class="nc bnc" id="L345" title="All 2 branches missed.">                int cost = (s1.charAt(i - 1) == s2.charAt(j - 1)) ? 0 : 1;</span>
<span class="nc" id="L346">                dp[i][j] = Math.min(Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1), dp[i - 1][j - 1] + cost);</span>
            }
        }
        
<span class="nc" id="L350">        return dp[len1][len2];</span>
    }
    
    /**
     * Checks if the token looks like an identifier.
     */
    private boolean isLikelyIdentifier(String tokenText) {
        // Standard identifier pattern: starts with letter or underscore
<span class="nc bnc" id="L358" title="All 2 branches missed.">        if (tokenText.matches(&quot;[a-zA-Z_].*&quot;)) {</span>
<span class="nc" id="L359">            return true;</span>
        }

        // Special case: starts with digit but contains letters (likely invalid identifier attempt)
        // But exclude hex numbers (0x...) and pure numbers
<span class="nc bnc" id="L364" title="All 4 branches missed.">        if (tokenText.matches(&quot;\\d.*&quot;) &amp;&amp; tokenText.matches(&quot;.*[a-zA-Z_].*&quot;)) {</span>
            // Exclude hex numbers
<span class="nc bnc" id="L366" title="All 4 branches missed.">            if (tokenText.startsWith(&quot;0x&quot;) || tokenText.startsWith(&quot;0X&quot;)) {</span>
<span class="nc" id="L367">                return false;</span>
            }
<span class="nc" id="L369">            return true;</span>
        }

<span class="nc" id="L372">        return false;</span>
    }
    
    /**
     * Checks if the token looks like a number.
     */
    private boolean isLikelyNumber(String tokenText) {
<span class="nc bnc" id="L379" title="All 4 branches missed.">        return tokenText.matches(&quot;\\d.*&quot;) || tokenText.matches(&quot;0[xX].*&quot;);</span>
    }
    
    /**
     * Checks if the token looks like a string.
     */
    private boolean isLikelyString(String tokenText) {
<span class="nc bnc" id="L386" title="All 4 branches missed.">        return tokenText.startsWith(&quot;\&quot;&quot;) || tokenText.startsWith(&quot;'&quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>