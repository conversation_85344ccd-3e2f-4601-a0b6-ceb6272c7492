<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplTokenAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">CaplTokenAnalyzer.java</span></div><h1>CaplTokenAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.RecognitionException;

import java.util.Set;

/**
 * Analyzer for CAPL-specific token errors.
 * 
 * This analyzer handles errors related to CAPL-specific syntax elements
 * such as system variable references (@variable), signal references ($signal),
 * and other CAPL-specific tokens.
 */
public class CaplTokenAnalyzer extends LexerErrorAnalyzer {
    
    public CaplTokenAnalyzer() {
<span class="nc" id="L22">        super(&quot;CaplTokenAnalyzer&quot;, 85, Set.of()); // Medium-high priority</span>
<span class="nc" id="L23">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
<span class="nc" id="L27">        String tokenText = extractOffendingSymbolText(context.getOffendingSymbol());</span>

<span class="nc bnc" id="L29" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L30">            return false;</span>
        }

        // Check if this looks like a CAPL-specific token
<span class="nc" id="L34">        return isCaplSpecificToken(tokenText);</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L39">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
        
<span class="nc bnc" id="L41" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L42">            return createGenericCaplTokenError(I18n.l(&quot;analyzer.caplToken.message.unknownCaplToken&quot;));</span>
        }
        
        // Analyze different types of CAPL token errors
<span class="nc bnc" id="L46" title="All 2 branches missed.">        if (isSystemVariableReference(tokenText)) {</span>
<span class="nc" id="L47">            return analyzeSystemVariableError(tokenText);</span>
        }
        
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (isSignalReference(tokenText)) {</span>
<span class="nc" id="L51">            return analyzeSignalReferenceError(tokenText);</span>
        }
        
<span class="nc bnc" id="L54" title="All 2 branches missed.">        if (isPreprocessorDirective(tokenText)) {</span>
<span class="nc" id="L55">            return analyzePreprocessorError(tokenText);</span>
        }
        
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (isEventKeyword(tokenText)) {</span>
<span class="nc" id="L59">            return analyzeEventKeywordError(tokenText);</span>
        }
        
<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (isBusSpecificKeyword(tokenText)) {</span>
<span class="nc" id="L63">            return analyzeBusSpecificError(tokenText);</span>
        }
        
        // Generic CAPL token error
<span class="nc" id="L67">        return createGenericCaplTokenError(I18n.l(&quot;analyzer.caplToken.message.genericCaplToken&quot;, tokenText));</span>
    }
    
    /**
     * Analyzes system variable reference errors.
     */
    private ErrorResult analyzeSystemVariableError(String tokenText) {
        String message;
        String suggestion;

<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (tokenText.equals(&quot;@&quot;)) {</span>
<span class="nc" id="L78">            message = I18n.l(&quot;analyzer.caplToken.message.incompleteSystemVar&quot;);</span>
<span class="nc" id="L79">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.incompleteSystemVar&quot;);</span>
<span class="nc bnc" id="L80" title="All 4 branches missed.">        } else if (tokenText.startsWith(&quot;@&quot;) &amp;&amp; tokenText.length() &gt; 1) {</span>
<span class="nc" id="L81">            String varName = tokenText.substring(1);</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">            if (!isValidIdentifier(varName)) {</span>
<span class="nc" id="L83">                message = I18n.l(&quot;analyzer.caplToken.message.invalidSystemVarName&quot;);</span>
<span class="nc" id="L84">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.invalidSystemVarName&quot;);</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">            } else if (varName.contains(&quot;::&quot;)) {</span>
                // Namespace separator is valid in system variables
<span class="nc" id="L87">                message = I18n.l(&quot;analyzer.caplToken.message.systemVarFormat&quot;);</span>
<span class="nc" id="L88">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.systemVarNamespace&quot;);</span>
            } else {
                // Valid identifier but might be in wrong context
<span class="nc" id="L91">                message = I18n.l(&quot;analyzer.caplToken.message.systemVarFormat&quot;);</span>
<span class="nc" id="L92">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.systemVarContext&quot;);</span>
            }
<span class="nc" id="L94">        } else {</span>
<span class="nc" id="L95">            message = I18n.l(&quot;analyzer.caplToken.message.invalidSystemVarName&quot;);</span>
<span class="nc" id="L96">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.invalidSystemVarName&quot;);</span>
        }
        
<span class="nc" id="L99">        return ErrorResult.builder()</span>
<span class="nc" id="L100">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L101">            .enhancedMessage(message)</span>
<span class="nc" id="L102">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L103">            .suggestion(suggestion)</span>
<span class="nc" id="L104">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L105">            .addMetadata(&quot;tokenType&quot;, &quot;system_variable&quot;)</span>
<span class="nc" id="L106">            .build();</span>
    }
    
    /**
     * Analyzes signal reference errors.
     */
    private ErrorResult analyzeSignalReferenceError(String tokenText) {
        String message;
        String suggestion;

<span class="nc bnc" id="L116" title="All 2 branches missed.">        if (tokenText.equals(&quot;$&quot;)) {</span>
<span class="nc" id="L117">            message = I18n.l(&quot;analyzer.caplToken.message.incompleteSignal&quot;);</span>
<span class="nc" id="L118">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.incompleteSignal&quot;);</span>
<span class="nc bnc" id="L119" title="All 4 branches missed.">        } else if (tokenText.startsWith(&quot;$&quot;) &amp;&amp; tokenText.length() &gt; 1) {</span>
<span class="nc" id="L120">            String signalName = tokenText.substring(1);</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">            if (!isValidIdentifier(signalName)) {</span>
<span class="nc" id="L122">                message = I18n.l(&quot;analyzer.caplToken.message.invalidSignalName&quot;);</span>
<span class="nc" id="L123">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.invalidSignalName&quot;);</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">            } else if (signalName.contains(&quot;::&quot;)) {</span>
<span class="nc" id="L125">                message = I18n.l(&quot;analyzer.caplToken.message.signalFormat&quot;);</span>
<span class="nc" id="L126">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.signalDatabase&quot;);</span>
            } else {
                // Valid identifier but might be in wrong context
<span class="nc" id="L129">                message = I18n.l(&quot;analyzer.caplToken.message.signalFormat&quot;);</span>
<span class="nc" id="L130">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.signalContext&quot;);</span>
            }
<span class="nc" id="L132">        } else {</span>
<span class="nc" id="L133">            message = I18n.l(&quot;analyzer.caplToken.message.invalidSignalName&quot;);</span>
<span class="nc" id="L134">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.invalidSignalName&quot;);</span>
        }
        
<span class="nc" id="L137">        return ErrorResult.builder()</span>
<span class="nc" id="L138">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L139">            .enhancedMessage(message)</span>
<span class="nc" id="L140">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L141">            .suggestion(suggestion)</span>
<span class="nc" id="L142">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L143">            .addMetadata(&quot;tokenType&quot;, &quot;signal_reference&quot;)</span>
<span class="nc" id="L144">            .build();</span>
    }
    
    /**
     * Analyzes preprocessor directive errors.
     */
    private ErrorResult analyzePreprocessorError(String tokenText) {
<span class="nc" id="L151">        String message = I18n.l(&quot;analyzer.caplToken.message.unknownPreprocessor&quot;, tokenText);</span>
<span class="nc" id="L152">        String suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownPreprocessor&quot;);</span>

<span class="nc bnc" id="L154" title="All 2 branches missed.">        if (tokenText.startsWith(&quot;#&quot;)) {</span>
<span class="nc" id="L155">            String directive = tokenText.substring(1);</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">            if (directive.isEmpty()) {</span>
<span class="nc" id="L157">                message = I18n.l(&quot;analyzer.caplToken.message.incompletePreprocessor&quot;);</span>
<span class="nc" id="L158">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.incompletePreprocessor&quot;);</span>
            } else {
<span class="nc bnc" id="L160" title="All 4 branches missed.">                switch (directive.toLowerCase()) {</span>
                    case &quot;includ&quot;:
                    case &quot;incude&quot;:
<span class="nc" id="L163">                        message = I18n.l(&quot;analyzer.caplToken.message.misspelledInclude&quot;);</span>
<span class="nc" id="L164">                        suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledInclude&quot;);</span>
<span class="nc" id="L165">                        break;</span>
                    case &quot;defin&quot;:
                    case &quot;defne&quot;:
<span class="nc" id="L168">                        message = I18n.l(&quot;analyzer.caplToken.message.misspelledDefine&quot;);</span>
<span class="nc" id="L169">                        suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledDefine&quot;);</span>
<span class="nc" id="L170">                        break;</span>
                    case &quot;pragm&quot;:
                    case &quot;prgma&quot;:
<span class="nc" id="L173">                        message = I18n.l(&quot;analyzer.caplToken.message.misspelledPragma&quot;);</span>
<span class="nc" id="L174">                        suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledPragma&quot;);</span>
<span class="nc" id="L175">                        break;</span>
                    default:
<span class="nc" id="L177">                        message = I18n.l(&quot;analyzer.caplToken.message.unknownPreprocessor&quot;, directive);</span>
<span class="nc" id="L178">                        suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownPreprocessor&quot;);</span>
                }
            }
        }
        
<span class="nc" id="L183">        return ErrorResult.builder()</span>
<span class="nc" id="L184">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L185">            .enhancedMessage(message)</span>
<span class="nc" id="L186">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L187">            .suggestion(suggestion)</span>
<span class="nc" id="L188">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L189">            .addMetadata(&quot;tokenType&quot;, &quot;preprocessor&quot;)</span>
<span class="nc" id="L190">            .build();</span>
    }
    
    /**
     * Analyzes event keyword errors.
     */
    private ErrorResult analyzeEventKeywordError(String tokenText) {
<span class="nc" id="L197">        String message = I18n.l(&quot;analyzer.caplToken.message.unknownEvent&quot;, tokenText);</span>
<span class="nc" id="L198">        String suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownEvent&quot;);</span>

        // Common event keyword misspellings
<span class="nc bnc" id="L201" title="All 4 branches missed.">        switch (tokenText.toLowerCase()) {</span>
            case &quot;o&quot;:
<span class="nc" id="L203">                message = I18n.l(&quot;analyzer.caplToken.message.incompleteEvent&quot;);</span>
<span class="nc" id="L204">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.incompleteEvent&quot;);</span>
<span class="nc" id="L205">                break;</span>
            case &quot;sysva&quot;:
            case &quot;sysvr&quot;:
<span class="nc" id="L208">                message = I18n.l(&quot;analyzer.caplToken.message.misspelledSysvar&quot;);</span>
<span class="nc" id="L209">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledSysvar&quot;);</span>
<span class="nc" id="L210">                break;</span>
            case &quot;envva&quot;:
            case &quot;envvr&quot;:
<span class="nc" id="L213">                message = I18n.l(&quot;analyzer.caplToken.message.misspelledEnvvar&quot;);</span>
<span class="nc" id="L214">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledEnvvar&quot;);</span>
<span class="nc" id="L215">                break;</span>
            default:
<span class="nc" id="L217">                message = I18n.l(&quot;analyzer.caplToken.message.unknownEvent&quot;, tokenText);</span>
<span class="nc" id="L218">                suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownEvent&quot;);</span>
        }
        
<span class="nc" id="L221">        return ErrorResult.builder()</span>
<span class="nc" id="L222">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L223">            .enhancedMessage(message)</span>
<span class="nc" id="L224">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L225">            .suggestion(suggestion)</span>
<span class="nc" id="L226">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L227">            .addMetadata(&quot;tokenType&quot;, &quot;event_keyword&quot;)</span>
<span class="nc" id="L228">            .build();</span>
    }
    
    /**
     * Analyzes bus-specific keyword errors.
     */
    private ErrorResult analyzeBusSpecificError(String tokenText) {
<span class="nc" id="L235">        String message = I18n.l(&quot;analyzer.caplToken.message.unknownBusKeyword&quot;, tokenText);</span>
<span class="nc" id="L236">        String suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownBusKeyword&quot;);</span>

        // Common bus-specific keyword issues
<span class="nc bnc" id="L239" title="All 2 branches missed.">        if (tokenText.toLowerCase().contains(&quot;flexra&quot;)) {</span>
<span class="nc" id="L240">            message = I18n.l(&quot;analyzer.caplToken.message.misspelledFlexray&quot;);</span>
<span class="nc" id="L241">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledFlexray&quot;);</span>
<span class="nc bnc" id="L242" title="All 2 branches missed.">        } else if (tokenText.toLowerCase().contains(&quot;frfram&quot;)) {</span>
<span class="nc" id="L243">            message = I18n.l(&quot;analyzer.caplToken.message.misspelledFrFrame&quot;);</span>
<span class="nc" id="L244">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledFrFrame&quot;);</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">        } else if (tokenText.toLowerCase().contains(&quot;linfram&quot;)) {</span>
<span class="nc" id="L246">            message = I18n.l(&quot;analyzer.caplToken.message.misspelledLinFrame&quot;);</span>
<span class="nc" id="L247">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledLinFrame&quot;);</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">        } else if (tokenText.toLowerCase().contains(&quot;diagreq&quot;)) {</span>
<span class="nc" id="L249">            message = I18n.l(&quot;analyzer.caplToken.message.misspelledDiagRequest&quot;);</span>
<span class="nc" id="L250">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.misspelledDiagRequest&quot;);</span>
        } else {
<span class="nc" id="L252">            message = I18n.l(&quot;analyzer.caplToken.message.unknownBusKeyword&quot;, tokenText);</span>
<span class="nc" id="L253">            suggestion = I18n.l(&quot;analyzer.caplToken.suggestion.unknownBusKeyword&quot;);</span>
        }
        
<span class="nc" id="L256">        return ErrorResult.builder()</span>
<span class="nc" id="L257">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L258">            .enhancedMessage(message)</span>
<span class="nc" id="L259">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L260">            .suggestion(suggestion)</span>
<span class="nc" id="L261">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L262">            .addMetadata(&quot;tokenType&quot;, &quot;bus_specific&quot;)</span>
<span class="nc" id="L263">            .build();</span>
    }
    
    /**
     * Creates a generic CAPL token error result.
     */
    private ErrorResult createGenericCaplTokenError(String message) {
<span class="nc" id="L270">        return ErrorResult.builder()</span>
<span class="nc" id="L271">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L272">            .enhancedMessage(message)</span>
<span class="nc" id="L273">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L274">            .suggestion(I18n.l(&quot;analyzer.caplToken.suggestion.unknownCaplToken&quot;))</span>
<span class="nc" id="L275">            .addMetadata(&quot;errorType&quot;, &quot;generic_capl_token&quot;)</span>
<span class="nc" id="L276">            .build();</span>
    }
    
    /**
     * Checks if the token is CAPL-specific.
     */
    private boolean isCaplSpecificToken(String tokenText) {
<span class="nc bnc" id="L283" title="All 2 branches missed.">        return isSystemVariableReference(tokenText) ||</span>
<span class="nc bnc" id="L284" title="All 2 branches missed.">               isSignalReference(tokenText) ||</span>
<span class="nc bnc" id="L285" title="All 2 branches missed.">               isPreprocessorDirective(tokenText) ||</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">               isEventKeyword(tokenText) ||</span>
<span class="nc bnc" id="L287" title="All 2 branches missed.">               isBusSpecificKeyword(tokenText);</span>
    }
    
    /**
     * Checks if the token is a system variable reference.
     */
    private boolean isSystemVariableReference(String tokenText) {
<span class="nc" id="L294">        return tokenText.startsWith(&quot;@&quot;);</span>
    }
    
    /**
     * Checks if the token is a signal reference.
     */
    private boolean isSignalReference(String tokenText) {
<span class="nc" id="L301">        return tokenText.startsWith(&quot;$&quot;);</span>
    }
    
    /**
     * Checks if the token is a preprocessor directive.
     */
    private boolean isPreprocessorDirective(String tokenText) {
<span class="nc" id="L308">        return tokenText.startsWith(&quot;#&quot;);</span>
    }
    
    /**
     * Checks if the token is an event keyword.
     */
    private boolean isEventKeyword(String tokenText) {
<span class="nc" id="L315">        String lower = tokenText.toLowerCase();</span>
<span class="nc bnc" id="L316" title="All 6 branches missed.">        return lower.equals(&quot;o&quot;) || lower.contains(&quot;sysva&quot;) || lower.contains(&quot;sysvr&quot;) ||</span>
<span class="nc bnc" id="L317" title="All 4 branches missed.">               lower.contains(&quot;envva&quot;) || lower.contains(&quot;envvr&quot;) ||</span>
<span class="nc bnc" id="L318" title="All 6 branches missed.">               lower.equals(&quot;on&quot;) || lower.equals(&quot;sysvar&quot;) || lower.equals(&quot;envvar&quot;);</span>
    }
    
    /**
     * Checks if the token is a bus-specific keyword.
     */
    private boolean isBusSpecificKeyword(String tokenText) {
<span class="nc" id="L325">        String lower = tokenText.toLowerCase();</span>
<span class="nc bnc" id="L326" title="All 4 branches missed.">        return lower.contains(&quot;flexra&quot;) || lower.contains(&quot;frfram&quot;) || </span>
<span class="nc bnc" id="L327" title="All 4 branches missed.">               lower.contains(&quot;linfram&quot;) || lower.contains(&quot;diagreq&quot;) ||</span>
<span class="nc bnc" id="L328" title="All 4 branches missed.">               lower.contains(&quot;diagres&quot;) || lower.contains(&quot;someip&quot;) ||</span>
<span class="nc bnc" id="L329" title="All 4 branches missed.">               lower.contains(&quot;ethernet&quot;) || lower.contains(&quot;most&quot;);</span>
    }
    
    /**
     * Checks if the string is a valid identifier.
     */
    private boolean isValidIdentifier(String str) {
<span class="nc bnc" id="L336" title="All 4 branches missed.">        if (str == null || str.isEmpty()) {</span>
<span class="nc" id="L337">            return false;</span>
        }
        
        // Allow namespace separator
<span class="nc" id="L341">        String[] parts = str.split(&quot;::&quot;);</span>
<span class="nc bnc" id="L342" title="All 2 branches missed.">        for (String part : parts) {</span>
<span class="nc bnc" id="L343" title="All 2 branches missed.">            if (!isValidIdentifierPart(part)) {</span>
<span class="nc" id="L344">                return false;</span>
            }
        }
        
<span class="nc" id="L348">        return true;</span>
    }
    
    /**
     * Checks if a part of an identifier is valid.
     */
    private boolean isValidIdentifierPart(String part) {
<span class="nc bnc" id="L355" title="All 4 branches missed.">        if (part == null || part.isEmpty()) {</span>
<span class="nc" id="L356">            return false;</span>
        }
        
<span class="nc" id="L359">        char first = part.charAt(0);</span>
<span class="nc bnc" id="L360" title="All 4 branches missed.">        if (!Character.isLetter(first) &amp;&amp; first != '_') {</span>
<span class="nc" id="L361">            return false;</span>
        }
        
<span class="nc bnc" id="L364" title="All 2 branches missed.">        for (int i = 1; i &lt; part.length(); i++) {</span>
<span class="nc" id="L365">            char ch = part.charAt(i);</span>
<span class="nc bnc" id="L366" title="All 4 branches missed.">            if (!Character.isLetterOrDigit(ch) &amp;&amp; ch != '_') {</span>
<span class="nc" id="L367">                return false;</span>
            }
        }
        
<span class="nc" id="L371">        return true;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>