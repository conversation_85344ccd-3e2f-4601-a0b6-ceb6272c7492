<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StringLiteralAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_class">StringLiteralAnalyzer</span></div><h1>StringLiteralAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">611 of 611</td><td class="ctr2">0%</td><td class="bar">111 of 111</td><td class="ctr2">0%</td><td class="ctr1">81</td><td class="ctr2">81</td><td class="ctr1">141</td><td class="ctr2">141</td><td class="ctr1">23</td><td class="ctr2">23</td></tr></tfoot><tbody><tr><td id="a3"><a href="StringLiteralAnalyzer.java.html#L39" class="el_method">analyzeLexerError(RecognitionException, Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="68" alt="68"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h0">16</td><td class="ctr2" id="i0">16</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="StringLiteralAnalyzer.java.html#L78" class="el_method">analyzeUnclosedString(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="52" alt="52"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="StringLiteralAnalyzer.java.html#L324" class="el_method">generateEscapeSequenceSuggestion(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="50" alt="50"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="9" alt="9"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h6">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="StringLiteralAnalyzer.java.html#L178" class="el_method">analyzeInvalidCharacters(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="47" alt="47"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h2">14</td><td class="ctr2" id="i2">14</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="StringLiteralAnalyzer.java.html#L124" class="el_method">analyzeInvalidEscapeSequence(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="38" alt="38"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a11"><a href="StringLiteralAnalyzer.java.html#L307" class="el_method">hasControlCharacters(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="38" alt="38"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="14" alt="14"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="StringLiteralAnalyzer.java.html#L103" class="el_method">analyzeUnclosedCharacter(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="37" alt="37"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a21"><a href="StringLiteralAnalyzer.java.html#L272" class="el_method">isValidEscapeCharacter(char)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="37" alt="37"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="StringLiteralAnalyzer.java.html#L160" class="el_method">analyzeMultiCharacterLiteral(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="35" alt="35"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h5">11</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="StringLiteralAnalyzer.java.html#L257" class="el_method">findInvalidEscapeSequence(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="30" alt="30"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a17"><a href="StringLiteralAnalyzer.java.html#L288" class="el_method">isMultiCharacterLiteral(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="30" alt="30"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="StringLiteralAnalyzer.java.html#L143" class="el_method">analyzeEmptyCharacterLiteral(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="28" alt="28"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="StringLiteralAnalyzer.java.html#L26" class="el_method">canAnalyzeLexerError(ErrorContext)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="25" alt="25"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h11">5</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="StringLiteralAnalyzer.java.html#L201" class="el_method">createGenericStringError(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="18" alt="18"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a14"><a href="StringLiteralAnalyzer.java.html#L228" class="el_method">hasStringLiteralPattern(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="16" alt="16"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="6" alt="6"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f7">4</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a20"><a href="StringLiteralAnalyzer.java.html#L236" class="el_method">isUnclosedString(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a19"><a href="StringLiteralAnalyzer.java.html#L243" class="el_method">isUnclosedCharacter(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a12"><a href="StringLiteralAnalyzer.java.html#L300" class="el_method">hasInvalidCharacters(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f12">3</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a13"><a href="StringLiteralAnalyzer.java.html#L250" class="el_method">hasInvalidEscapeSequence(String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="8" alt="8"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a22"><a href="StringLiteralAnalyzer.java.html#L21" class="el_method">StringLiteralAnalyzer()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a18"><a href="StringLiteralAnalyzer.java.html#L214" class="el_method">isStringLiteral(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a15"><a href="StringLiteralAnalyzer.java.html#L221" class="el_method">isCharacterLiteral(String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a16"><a href="StringLiteralAnalyzer.java.html#L281" class="el_method">isEmptyCharacterLiteral(String)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>