<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.parser.error.analyzer.lexer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.parser.error.analyzer.lexer</span></div><h1>com.polelink.calint.parser.error.analyzer.lexer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,223 of 4,223</td><td class="ctr2">0%</td><td class="bar">652 of 652</td><td class="ctr2">0%</td><td class="ctr1">454</td><td class="ctr2">454</td><td class="ctr1">935</td><td class="ctr2">935</td><td class="ctr1">112</td><td class="ctr2">112</td><td class="ctr1">8</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a6"><a href="UnrecognizedTokenAnalyzer.java.html" class="el_source">UnrecognizedTokenAnalyzer.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="946" alt="946"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="80" alt="80"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">56</td><td class="ctr2" id="g5">56</td><td class="ctr1" id="h0">206</td><td class="ctr2" id="i0">206</td><td class="ctr1" id="j4">16</td><td class="ctr2" id="k4">16</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CaplTokenAnalyzer.java.html" class="el_source">CaplTokenAnalyzer.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="791" alt="791"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="118" height="10" title="118" alt="118"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">78</td><td class="ctr2" id="g2">78</td><td class="ctr1" id="h1">182</td><td class="ctr2" id="i1">182</td><td class="ctr1" id="j2">17</td><td class="ctr2" id="k2">17</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="NumericLiteralAnalyzer.java.html" class="el_source">NumericLiteralAnalyzer.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="632" alt="632"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="120" alt="120"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">79</td><td class="ctr2" id="g1">79</td><td class="ctr1" id="h4">128</td><td class="ctr2" id="i4">128</td><td class="ctr1" id="j1">19</td><td class="ctr2" id="k1">19</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="TokenErrorAnalyzer.java.html" class="el_source">TokenErrorAnalyzer.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="619" alt="619"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="83" alt="83"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">59</td><td class="ctr2" id="g4">59</td><td class="ctr1" id="h2">142</td><td class="ctr2" id="i2">142</td><td class="ctr1" id="j5">13</td><td class="ctr2" id="k5">13</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a4"><a href="StringLiteralAnalyzer.java.html" class="el_source">StringLiteralAnalyzer.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="611" alt="611"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="111" height="10" title="111" alt="111"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">81</td><td class="ctr2" id="g0">81</td><td class="ctr1" id="h3">141</td><td class="ctr2" id="i3">141</td><td class="ctr1" id="j0">23</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a2"><a href="LexerErrorAnalyzer.java.html" class="el_source">LexerErrorAnalyzer.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="324" alt="324"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="84" alt="84"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">61</td><td class="ctr2" id="g3">61</td><td class="ctr1" id="h5">84</td><td class="ctr2" id="i5">84</td><td class="ctr1" id="j3">17</td><td class="ctr2" id="k3">17</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a1"><a href="InvalidCharacterAnalyzer.java.html" class="el_source">InvalidCharacterAnalyzer.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="300" alt="300"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="56" alt="56"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">40</td><td class="ctr2" id="g6">40</td><td class="ctr1" id="h6">52</td><td class="ctr2" id="i6">52</td><td class="ctr1" id="j6">7</td><td class="ctr2" id="k6">7</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>