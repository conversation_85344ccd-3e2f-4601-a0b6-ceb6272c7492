<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StringLiteralAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">StringLiteralAnalyzer.java</span></div><h1>StringLiteralAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.RecognitionException;

import java.util.Set;

/**
 * Analyzer for string literal errors.
 * 
 * This analyzer handles errors related to string and character literals,
 * including unclosed quotes, invalid escape sequences, and malformed literals.
 */
public class StringLiteralAnalyzer extends LexerErrorAnalyzer {
    
    public StringLiteralAnalyzer() {
<span class="nc" id="L21">        super(&quot;StringLiteralAnalyzer&quot;, 75, Set.of()); // Medium-high priority</span>
<span class="nc" id="L22">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
<span class="nc" id="L26">        String tokenText = extractOffendingSymbolText(context.getOffendingSymbol());</span>

<span class="nc bnc" id="L28" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L29">            return false;</span>
        }

        // Check if this looks like a string or character literal
<span class="nc bnc" id="L33" title="All 4 branches missed.">        return isStringLiteral(tokenText) || isCharacterLiteral(tokenText) ||</span>
<span class="nc bnc" id="L34" title="All 2 branches missed.">               hasStringLiteralPattern(tokenText);</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L39">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
        
<span class="nc bnc" id="L41" title="All 2 branches missed.">        if (tokenText == null) {</span>
<span class="nc" id="L42">            return createGenericStringError(I18n.l(&quot;analyzer.string.message.genericString&quot;));</span>
        }
        
        // Analyze different types of string literal errors
<span class="nc bnc" id="L46" title="All 2 branches missed.">        if (isUnclosedString(tokenText)) {</span>
<span class="nc" id="L47">            return analyzeUnclosedString(tokenText);</span>
        }
        
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (isUnclosedCharacter(tokenText)) {</span>
<span class="nc" id="L51">            return analyzeUnclosedCharacter(tokenText);</span>
        }
        
<span class="nc bnc" id="L54" title="All 2 branches missed.">        if (hasInvalidEscapeSequence(tokenText)) {</span>
<span class="nc" id="L55">            return analyzeInvalidEscapeSequence(tokenText);</span>
        }
        
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (isEmptyCharacterLiteral(tokenText)) {</span>
<span class="nc" id="L59">            return analyzeEmptyCharacterLiteral(tokenText);</span>
        }
        
<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (isMultiCharacterLiteral(tokenText)) {</span>
<span class="nc" id="L63">            return analyzeMultiCharacterLiteral(tokenText);</span>
        }
        
<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (hasInvalidCharacters(tokenText)) {</span>
<span class="nc" id="L67">            return analyzeInvalidCharacters(tokenText);</span>
        }
        
        // Generic string literal error
<span class="nc" id="L71">        return createGenericStringError(I18n.l(&quot;analyzer.string.message.genericString&quot;));</span>
    }
    
    /**
     * Analyzes unclosed string literals.
     */
    private ErrorResult analyzeUnclosedString(String tokenText) {
<span class="nc" id="L78">        String message = I18n.l(&quot;analyzer.string.message.unclosedString&quot;);</span>
<span class="nc" id="L79">        String suggestion = I18n.l(&quot;analyzer.string.suggestion.unclosedString&quot;);</span>

        // Check for common issues
<span class="nc bnc" id="L82" title="All 2 branches missed.">        if (tokenText.contains(&quot;\n&quot;)) {</span>
<span class="nc" id="L83">            suggestion = I18n.l(&quot;analyzer.string.suggestion.multilineString&quot;);</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">        } else if (tokenText.endsWith(&quot;\\&quot;)) {</span>
<span class="nc" id="L85">            suggestion = I18n.l(&quot;analyzer.string.suggestion.backslashEnd&quot;);</span>
        }
        
<span class="nc" id="L88">        return ErrorResult.builder()</span>
<span class="nc" id="L89">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L90">            .enhancedMessage(message)</span>
<span class="nc" id="L91">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L92">            .suggestion(suggestion)</span>
<span class="nc" id="L93">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L94">            .addMetadata(&quot;errorType&quot;, &quot;unclosed_string&quot;)</span>
<span class="nc" id="L95">            .addMetadata(&quot;stringLength&quot;, tokenText.length())</span>
<span class="nc" id="L96">            .build();</span>
    }
    
    /**
     * Analyzes unclosed character literals.
     */
    private ErrorResult analyzeUnclosedCharacter(String tokenText) {
<span class="nc" id="L103">        String message = I18n.l(&quot;analyzer.string.message.unclosedChar&quot;);</span>
<span class="nc" id="L104">        String suggestion = I18n.l(&quot;analyzer.string.suggestion.unclosedChar&quot;);</span>

<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (tokenText.length() &gt; 2) {</span>
<span class="nc" id="L107">            suggestion = I18n.l(&quot;analyzer.string.suggestion.multiChar&quot;);</span>
        }
        
<span class="nc" id="L110">        return ErrorResult.builder()</span>
<span class="nc" id="L111">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L112">            .enhancedMessage(message)</span>
<span class="nc" id="L113">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L114">            .suggestion(suggestion)</span>
<span class="nc" id="L115">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L116">            .addMetadata(&quot;errorType&quot;, &quot;unclosed_character&quot;)</span>
<span class="nc" id="L117">            .build();</span>
    }
    
    /**
     * Analyzes invalid escape sequences.
     */
    private ErrorResult analyzeInvalidEscapeSequence(String tokenText) {
<span class="nc" id="L124">        String invalidSequence = findInvalidEscapeSequence(tokenText);</span>
<span class="nc" id="L125">        String message = I18n.l(&quot;analyzer.string.message.invalidEscape&quot;, invalidSequence);</span>
<span class="nc" id="L126">        String suggestion = generateEscapeSequenceSuggestion(invalidSequence);</span>
        
<span class="nc" id="L128">        return ErrorResult.builder()</span>
<span class="nc" id="L129">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L130">            .enhancedMessage(message)</span>
<span class="nc" id="L131">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L132">            .suggestion(suggestion)</span>
<span class="nc" id="L133">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L134">            .addMetadata(&quot;invalidEscapeSequence&quot;, invalidSequence)</span>
<span class="nc" id="L135">            .addMetadata(&quot;errorType&quot;, &quot;invalid_escape&quot;)</span>
<span class="nc" id="L136">            .build();</span>
    }
    
    /**
     * Analyzes empty character literals.
     */
    private ErrorResult analyzeEmptyCharacterLiteral(String tokenText) {
<span class="nc" id="L143">        String message = I18n.l(&quot;analyzer.string.message.emptyChar&quot;);</span>
<span class="nc" id="L144">        String suggestion = I18n.l(&quot;analyzer.string.suggestion.emptyChar&quot;);</span>
        
<span class="nc" id="L146">        return ErrorResult.builder()</span>
<span class="nc" id="L147">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L148">            .enhancedMessage(message)</span>
<span class="nc" id="L149">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L150">            .suggestion(suggestion)</span>
<span class="nc" id="L151">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L152">            .addMetadata(&quot;errorType&quot;, &quot;empty_character&quot;)</span>
<span class="nc" id="L153">            .build();</span>
    }
    
    /**
     * Analyzes multi-character literals.
     */
    private ErrorResult analyzeMultiCharacterLiteral(String tokenText) {
<span class="nc" id="L160">        String message = I18n.l(&quot;analyzer.string.message.multiChar&quot;);</span>
<span class="nc" id="L161">        String suggestion = I18n.l(&quot;analyzer.string.suggestion.multiChar&quot;);</span>
        
<span class="nc" id="L163">        return ErrorResult.builder()</span>
<span class="nc" id="L164">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L165">            .enhancedMessage(message)</span>
<span class="nc" id="L166">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L167">            .suggestion(suggestion)</span>
<span class="nc" id="L168">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L169">            .addMetadata(&quot;errorType&quot;, &quot;multi_character&quot;)</span>
<span class="nc" id="L170">            .addMetadata(&quot;characterCount&quot;, tokenText.length() - 2) // Exclude quotes</span>
<span class="nc" id="L171">            .build();</span>
    }
    
    /**
     * Analyzes strings with invalid characters.
     */
    private ErrorResult analyzeInvalidCharacters(String tokenText) {
<span class="nc" id="L178">        String message = I18n.l(&quot;analyzer.string.message.invalidChars&quot;);</span>
<span class="nc" id="L179">        String suggestion = I18n.l(&quot;analyzer.string.suggestion.invalidChars&quot;);</span>

<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (tokenText.contains(&quot;\0&quot;)) {</span>
<span class="nc" id="L182">            suggestion = I18n.l(&quot;analyzer.string.suggestion.nullChar&quot;);</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">        } else if (hasControlCharacters(tokenText)) {</span>
<span class="nc" id="L184">            suggestion = I18n.l(&quot;analyzer.string.suggestion.controlChars&quot;);</span>
        }
        
<span class="nc" id="L187">        return ErrorResult.builder()</span>
<span class="nc" id="L188">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L189">            .enhancedMessage(message)</span>
<span class="nc" id="L190">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L191">            .suggestion(suggestion)</span>
<span class="nc" id="L192">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L193">            .addMetadata(&quot;errorType&quot;, &quot;invalid_characters&quot;)</span>
<span class="nc" id="L194">            .build();</span>
    }
    
    /**
     * Creates a generic string error result.
     */
    private ErrorResult createGenericStringError(String message) {
<span class="nc" id="L201">        return ErrorResult.builder()</span>
<span class="nc" id="L202">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L203">            .enhancedMessage(message)</span>
<span class="nc" id="L204">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L205">            .suggestion(I18n.l(&quot;analyzer.string.suggestion.genericString&quot;))</span>
<span class="nc" id="L206">            .addMetadata(&quot;errorType&quot;, &quot;generic&quot;)</span>
<span class="nc" id="L207">            .build();</span>
    }
    
    /**
     * Checks if the token is a string literal.
     */
    private boolean isStringLiteral(String tokenText) {
<span class="nc" id="L214">        return tokenText.startsWith(&quot;\&quot;&quot;);</span>
    }
    
    /**
     * Checks if the token is a character literal.
     */
    private boolean isCharacterLiteral(String tokenText) {
<span class="nc" id="L221">        return tokenText.startsWith(&quot;'&quot;);</span>
    }
    
    /**
     * Checks if the token has string literal patterns.
     */
    private boolean hasStringLiteralPattern(String tokenText) {
<span class="nc bnc" id="L228" title="All 4 branches missed.">        return tokenText.contains(&quot;\&quot;&quot;) || tokenText.contains(&quot;'&quot;) || </span>
<span class="nc bnc" id="L229" title="All 2 branches missed.">               tokenText.contains(&quot;\\&quot;);</span>
    }
    
    /**
     * Checks if the string is unclosed.
     */
    private boolean isUnclosedString(String tokenText) {
<span class="nc bnc" id="L236" title="All 4 branches missed.">        return tokenText.startsWith(&quot;\&quot;&quot;) &amp;&amp; !tokenText.endsWith(&quot;\&quot;&quot;);</span>
    }
    
    /**
     * Checks if the character literal is unclosed.
     */
    private boolean isUnclosedCharacter(String tokenText) {
<span class="nc bnc" id="L243" title="All 4 branches missed.">        return tokenText.startsWith(&quot;'&quot;) &amp;&amp; !tokenText.endsWith(&quot;'&quot;);</span>
    }
    
    /**
     * Checks if the string has invalid escape sequences.
     */
    private boolean hasInvalidEscapeSequence(String tokenText) {
<span class="nc bnc" id="L250" title="All 2 branches missed.">        return findInvalidEscapeSequence(tokenText) != null;</span>
    }
    
    /**
     * Finds invalid escape sequences in the token.
     */
    private String findInvalidEscapeSequence(String tokenText) {
<span class="nc bnc" id="L257" title="All 2 branches missed.">        for (int i = 0; i &lt; tokenText.length() - 1; i++) {</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">            if (tokenText.charAt(i) == '\\') {</span>
<span class="nc" id="L259">                char nextChar = tokenText.charAt(i + 1);</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">                if (!isValidEscapeCharacter(nextChar)) {</span>
<span class="nc" id="L261">                    return &quot;\\&quot; + nextChar;</span>
                }
            }
        }
<span class="nc" id="L265">        return null;</span>
    }
    
    /**
     * Checks if the character is a valid escape character.
     */
    private boolean isValidEscapeCharacter(char ch) {
<span class="nc bnc" id="L272" title="All 22 branches missed.">        return ch == 'n' || ch == 't' || ch == 'r' || ch == '\\' || </span>
               ch == '&quot;' || ch == '\'' || ch == '0' || ch == 'b' || 
               ch == 'f' || ch == 'v' || ch == 'a';
    }
    
    /**
     * Checks if the character literal is empty.
     */
    private boolean isEmptyCharacterLiteral(String tokenText) {
<span class="nc" id="L281">        return tokenText.equals(&quot;''&quot;);</span>
    }
    
    /**
     * Checks if the character literal contains multiple characters.
     */
    private boolean isMultiCharacterLiteral(String tokenText) {
<span class="nc bnc" id="L288" title="All 4 branches missed.">        if (!tokenText.startsWith(&quot;'&quot;) || !tokenText.endsWith(&quot;'&quot;)) {</span>
<span class="nc" id="L289">            return false;</span>
        }
        
<span class="nc" id="L292">        String content = tokenText.substring(1, tokenText.length() - 1);</span>
<span class="nc bnc" id="L293" title="All 4 branches missed.">        return content.length() &gt; 1 &amp;&amp; !content.startsWith(&quot;\\&quot;);</span>
    }
    
    /**
     * Checks if the string has invalid characters.
     */
    private boolean hasInvalidCharacters(String tokenText) {
<span class="nc bnc" id="L300" title="All 4 branches missed.">        return tokenText.contains(&quot;\0&quot;) || hasControlCharacters(tokenText);</span>
    }
    
    /**
     * Checks if the string has unescaped control characters.
     */
    private boolean hasControlCharacters(String tokenText) {
<span class="nc bnc" id="L307" title="All 2 branches missed.">        for (int i = 0; i &lt; tokenText.length(); i++) {</span>
<span class="nc" id="L308">            char ch = tokenText.charAt(i);</span>
<span class="nc bnc" id="L309" title="All 8 branches missed.">            if (Character.isISOControl(ch) &amp;&amp; ch != '\t' &amp;&amp; ch != '\n' &amp;&amp; ch != '\r') {</span>
                // Check if it's escaped
<span class="nc bnc" id="L311" title="All 4 branches missed.">                if (i &gt; 0 &amp;&amp; tokenText.charAt(i - 1) == '\\') {</span>
<span class="nc" id="L312">                    continue;</span>
                }
<span class="nc" id="L314">                return true;</span>
            }
        }
<span class="nc" id="L317">        return false;</span>
    }
    
    /**
     * Generates suggestions for escape sequence errors.
     */
    private String generateEscapeSequenceSuggestion(String invalidSequence) {
<span class="nc bnc" id="L324" title="All 2 branches missed.">        if (invalidSequence == null) {</span>
<span class="nc" id="L325">            return I18n.l(&quot;analyzer.string.suggestion.escapeValid&quot;);</span>
        }

<span class="nc bnc" id="L328" title="All 7 branches missed.">        switch (invalidSequence) {</span>
            case &quot;\\s&quot;:
<span class="nc" id="L330">                return I18n.l(&quot;analyzer.string.suggestion.escapeTab&quot;);</span>
            case &quot;\\d&quot;:
<span class="nc" id="L332">                return I18n.l(&quot;analyzer.string.suggestion.escapeDigit&quot;);</span>
            case &quot;\\w&quot;:
<span class="nc" id="L334">                return I18n.l(&quot;analyzer.string.suggestion.escapeWord&quot;);</span>
            case &quot;\\c&quot;:
<span class="nc" id="L336">                return I18n.l(&quot;analyzer.string.suggestion.escapeChar&quot;);</span>
            case &quot;\\x&quot;:
<span class="nc" id="L338">                return I18n.l(&quot;analyzer.string.suggestion.escapeHex&quot;);</span>
            case &quot;\\u&quot;:
<span class="nc" id="L340">                return I18n.l(&quot;analyzer.string.suggestion.escapeUnicode&quot;);</span>
            default:
<span class="nc" id="L342">                return I18n.l(&quot;analyzer.string.suggestion.escapeValid&quot;);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>