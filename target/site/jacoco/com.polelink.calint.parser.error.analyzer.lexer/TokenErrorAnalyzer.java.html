<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TokenErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_source">TokenErrorAnalyzer.java</span></div><h1>TokenErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.lexer;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.LexerNoViableAltException;
import org.antlr.v4.runtime.RecognitionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * Main coordinator for lexer error analysis.
 * 
 * This class serves as the central coordinator for all lexer error analysis,
 * dispatching to specific analyzers based on the error type and context.
 */
public class TokenErrorAnalyzer extends LexerErrorAnalyzer {
    
<span class="nc" id="L23">    private static final Logger LOGGER = LoggerFactory.getLogger(TokenErrorAnalyzer.class);</span>
    
    public TokenErrorAnalyzer() {
<span class="nc" id="L26">        super(&quot;TokenErrorAnalyzer&quot;, Set.of()); // Supports all contexts</span>
<span class="nc" id="L27">    }</span>
    
    @Override
    protected boolean canAnalyzeLexerError(ErrorContext context) {
        // This analyzer can handle any lexer error as a fallback
<span class="nc" id="L32">        return true;</span>
    }
    
    @Override
    protected ErrorResult analyzeLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc bnc" id="L37" title="All 2 branches missed.">        LOGGER.debug(&quot;Analyzing lexer error: {}&quot;, exception != null ? exception.getClass().getSimpleName() : &quot;null&quot;);</span>

<span class="nc" id="L39">        LexerErrorCategory category = getErrorCategory(exception, offendingSymbol);</span>
        
<span class="nc bnc" id="L41" title="All 6 branches missed.">        switch (category) {</span>
            case INVALID_CHARACTER:
<span class="nc" id="L43">                return analyzeInvalidCharacter(exception, offendingSymbol);</span>

            case UNRECOGNIZED_TOKEN:
<span class="nc" id="L46">                return analyzeUnrecognizedToken(exception, offendingSymbol);</span>

            case STRING_LITERAL_ERROR:
<span class="nc" id="L49">                return analyzeStringLiteralError(exception, offendingSymbol);</span>

            case NUMERIC_LITERAL_ERROR:
<span class="nc" id="L52">                return analyzeNumericLiteralError(exception, offendingSymbol);</span>

            case CAPL_TOKEN_ERROR:
<span class="nc" id="L55">                return analyzeCaplTokenError(exception, offendingSymbol);</span>

            case GENERIC_ERROR:
            default:
<span class="nc" id="L59">                return analyzeGenericLexerError(exception, offendingSymbol);</span>
        }
    }
    
    /**
     * Analyzes invalid character errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeInvalidCharacter(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L71">        char badChar = '\0';</span>
<span class="nc" id="L72">        String suggestion = I18n.l(&quot;analyzer.token.suggestion.removeInvalidChar&quot;);</span>
        
<span class="nc bnc" id="L74" title="All 2 branches missed.">        if (exception instanceof LexerNoViableAltException) {</span>
<span class="nc" id="L75">            badChar = extractBadCharacter((LexerNoViableAltException) exception);</span>
<span class="nc" id="L76">            suggestion = generateInvalidCharacterSuggestion(badChar);</span>
        }
        
<span class="nc" id="L79">        String formattedChar = formatCharacterForDisplay(badChar);</span>
<span class="nc" id="L80">        String message = I18n.l(&quot;analyzer.token.message.invalidCharacter&quot;, formattedChar);</span>
        
<span class="nc" id="L82">        return ErrorResult.builder()</span>
<span class="nc" id="L83">            .ruleId(CaplErrorType.LEXER_INVALID_CHAR.getErrorCode())</span>
<span class="nc" id="L84">            .enhancedMessage(message)</span>
<span class="nc" id="L85">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L86">            .suggestion(suggestion)</span>
<span class="nc" id="L87">            .addMetadata(&quot;invalidCharacter&quot;, formattedChar)</span>
<span class="nc" id="L88">            .addMetadata(&quot;characterCode&quot;, (int) badChar)</span>
<span class="nc" id="L89">            .build();</span>
    }
    
    /**
     * Analyzes unrecognized token errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeUnrecognizedToken(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L100">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">        String message = I18n.l(&quot;analyzer.token.message.unrecognizedToken&quot;, tokenText != null ? tokenText : &quot;&lt;unknown&gt;&quot;);</span>
<span class="nc" id="L102">        String suggestion = generateUnrecognizedTokenSuggestion(tokenText);</span>
        
<span class="nc" id="L104">        return ErrorResult.builder()</span>
<span class="nc" id="L105">            .ruleId(CaplErrorType.LEXER_UNRECOGNIZED_TOKEN.getErrorCode())</span>
<span class="nc" id="L106">            .enhancedMessage(message)</span>
<span class="nc" id="L107">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L108">            .suggestion(suggestion)</span>
<span class="nc" id="L109">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L110">            .build();</span>
    }
    
    /**
     * Analyzes string literal errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeStringLiteralError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L121">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc" id="L122">        String message = I18n.l(&quot;analyzer.token.message.stringLiteralError&quot;);</span>
<span class="nc" id="L123">        String suggestion = I18n.l(&quot;analyzer.token.suggestion.checkStringLiteralSyntax&quot;);</span>

<span class="nc bnc" id="L125" title="All 2 branches missed.">        if (tokenText != null) {</span>
<span class="nc bnc" id="L126" title="All 4 branches missed.">            if (tokenText.startsWith(&quot;\&quot;&quot;) &amp;&amp; !tokenText.endsWith(&quot;\&quot;&quot;)) {</span>
<span class="nc" id="L127">                message = I18n.l(&quot;analyzer.token.message.unclosedStringLiteral&quot;);</span>
<span class="nc" id="L128">                suggestion = I18n.l(&quot;analyzer.token.suggestion.addClosingDoubleQuote&quot;);</span>
<span class="nc bnc" id="L129" title="All 4 branches missed.">            } else if (tokenText.startsWith(&quot;'&quot;) &amp;&amp; !tokenText.endsWith(&quot;'&quot;)) {</span>
<span class="nc" id="L130">                message = I18n.l(&quot;analyzer.token.message.unclosedCharLiteral&quot;);</span>
<span class="nc" id="L131">                suggestion = I18n.l(&quot;analyzer.token.suggestion.addClosingSingleQuote&quot;);</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">            } else if (tokenText.contains(&quot;\\&quot;)) {</span>
<span class="nc" id="L133">                message = I18n.l(&quot;analyzer.token.message.invalidEscapeSequence&quot;);</span>
<span class="nc" id="L134">                suggestion = I18n.l(&quot;analyzer.token.suggestion.checkEscapeSequences&quot;);</span>
            }
        }
        
<span class="nc" id="L138">        return ErrorResult.builder()</span>
<span class="nc" id="L139">            .ruleId(CaplErrorType.LEXER_STRING_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L140">            .enhancedMessage(message)</span>
<span class="nc" id="L141">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L142">            .suggestion(suggestion)</span>
<span class="nc" id="L143">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L144">            .build();</span>
    }
    
    /**
     * Analyzes numeric literal errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeNumericLiteralError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L155">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc" id="L156">        String message = I18n.l(&quot;analyzer.token.message.numericLiteralError&quot;);</span>
<span class="nc" id="L157">        String suggestion = I18n.l(&quot;analyzer.token.suggestion.checkNumericLiteralFormat&quot;);</span>
        
<span class="nc bnc" id="L159" title="All 2 branches missed.">        if (tokenText != null) {</span>
<span class="nc bnc" id="L160" title="All 4 branches missed.">            if (tokenText.equals(&quot;0x&quot;) || tokenText.equals(&quot;0X&quot;)) {</span>
<span class="nc" id="L161">                message = I18n.l(&quot;analyzer.token.message.incompleteHex&quot;);</span>
<span class="nc" id="L162">                suggestion = I18n.l(&quot;analyzer.token.suggestion.incompleteHex&quot;);</span>
<span class="nc bnc" id="L163" title="All 4 branches missed.">            } else if (tokenText.startsWith(&quot;0x&quot;) || tokenText.startsWith(&quot;0X&quot;)) {</span>
<span class="nc" id="L164">                message = I18n.l(&quot;analyzer.token.message.invalidHex&quot;);</span>
<span class="nc" id="L165">                suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidHex&quot;);</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">            } else if (tokenText.contains(&quot;.&quot;)) {</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">                long decimalCount = tokenText.chars().filter(ch -&gt; ch == '.').count();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">                if (decimalCount &gt; 1) {</span>
<span class="nc" id="L169">                    message = I18n.l(&quot;analyzer.token.message.invalidFloat&quot;);</span>
<span class="nc" id="L170">                    suggestion = I18n.l(&quot;analyzer.token.suggestion.multipleDecimal&quot;);</span>
                } else {
<span class="nc" id="L172">                    message = I18n.l(&quot;analyzer.token.message.invalidFloat&quot;);</span>
<span class="nc" id="L173">                    suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidFloat&quot;);</span>
                }
<span class="nc bnc" id="L175" title="All 2 branches missed.">            } else if (tokenText.matches(&quot;\\d+[a-zA-Z]+.*&quot;)) {</span>
<span class="nc" id="L176">                message = I18n.l(&quot;analyzer.token.message.invalidInteger&quot;);</span>
<span class="nc" id="L177">                suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidInteger&quot;);</span>
            } else {
<span class="nc" id="L179">                message = I18n.l(&quot;analyzer.token.message.invalidNumeric&quot;);</span>
<span class="nc" id="L180">                suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidNumeric&quot;);</span>
            }
        }
        
<span class="nc" id="L184">        return ErrorResult.builder()</span>
<span class="nc" id="L185">            .ruleId(CaplErrorType.LEXER_NUMERIC_LITERAL_ERROR.getErrorCode())</span>
<span class="nc" id="L186">            .enhancedMessage(message)</span>
<span class="nc" id="L187">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L188">            .suggestion(suggestion)</span>
<span class="nc" id="L189">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L190">            .build();</span>
    }
    
    /**
     * Analyzes CAPL-specific token errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeCaplTokenError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L201">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc" id="L202">        String message = I18n.l(&quot;analyzer.token.message.caplTokenError&quot;);</span>
<span class="nc" id="L203">        String suggestion = I18n.l(&quot;analyzer.token.suggestion.checkCaplSpecificSyntax&quot;);</span>
        
<span class="nc bnc" id="L205" title="All 2 branches missed.">        if (tokenText != null) {</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">            if (tokenText.equals(&quot;@&quot;)) {</span>
<span class="nc" id="L207">                message = I18n.l(&quot;analyzer.token.message.incompleteSystemVar&quot;);</span>
<span class="nc" id="L208">                suggestion = I18n.l(&quot;analyzer.token.suggestion.incompleteSystemVar&quot;);</span>
<span class="nc bnc" id="L209" title="All 2 branches missed.">            } else if (tokenText.startsWith(&quot;@&quot;)) {</span>
<span class="nc" id="L210">                message = I18n.l(&quot;analyzer.token.message.invalidSystemVar&quot;);</span>
<span class="nc" id="L211">                suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidSystemVar&quot;);</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">            } else if (tokenText.equals(&quot;$&quot;)) {</span>
<span class="nc" id="L213">                message = I18n.l(&quot;analyzer.token.message.incompleteSignal&quot;);</span>
<span class="nc" id="L214">                suggestion = I18n.l(&quot;analyzer.token.suggestion.incompleteSignal&quot;);</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">            } else if (tokenText.startsWith(&quot;$&quot;)) {</span>
<span class="nc" id="L216">                message = I18n.l(&quot;analyzer.token.message.invalidSignal&quot;);</span>
<span class="nc" id="L217">                suggestion = I18n.l(&quot;analyzer.token.suggestion.invalidSignal&quot;);</span>
            }
        }
        
<span class="nc" id="L221">        return ErrorResult.builder()</span>
<span class="nc" id="L222">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L223">            .enhancedMessage(message)</span>
<span class="nc" id="L224">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L225">            .suggestion(suggestion)</span>
<span class="nc" id="L226">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L227">            .build();</span>
    }
    
    /**
     * Analyzes generic lexer errors.
     * 
     * @param exception the recognition exception
     * @param offendingSymbol the offending symbol
     * @return the analysis result
     */
    private ErrorResult analyzeGenericLexerError(RecognitionException exception, Object offendingSymbol) {
<span class="nc" id="L238">        String tokenText = extractOffendingSymbolText(offendingSymbol);</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">        String exceptionType = exception != null ? exception.getClass().getSimpleName() : &quot;Unknown&quot;;</span>
        String message;

<span class="nc bnc" id="L242" title="All 2 branches missed.">        if (tokenText != null) {</span>
<span class="nc" id="L243">            message = I18n.l(&quot;analyzer.token.message.lexerErrorAtToken&quot;, exceptionType, tokenText);</span>
        } else {
<span class="nc" id="L245">            message = I18n.l(&quot;analyzer.token.message.lexerError&quot;, exceptionType);</span>
        }
        
<span class="nc" id="L248">        return ErrorResult.builder()</span>
<span class="nc" id="L249">            .ruleId(CaplErrorType.LEXER_ERROR.getErrorCode())</span>
<span class="nc" id="L250">            .enhancedMessage(message)</span>
<span class="nc" id="L251">            .severity(SeverityLevel.ERROR)</span>
<span class="nc" id="L252">            .suggestion(I18n.l(&quot;analyzer.token.suggestion.checkSyntaxLocation&quot;))</span>
<span class="nc" id="L253">            .addMetadata(&quot;exceptionType&quot;, exceptionType)</span>
<span class="nc" id="L254">            .addMetadata(&quot;tokenText&quot;, tokenText)</span>
<span class="nc" id="L255">            .build();</span>
    }
    
    /**
     * Generates a suggestion for invalid character errors.
     * 
     * @param badChar the invalid character
     * @return a suggestion string
     */
    private String generateInvalidCharacterSuggestion(char badChar) {
<span class="nc bnc" id="L265" title="All 4 branches missed.">        switch (badChar) {</span>
            case '\u201C':
            case '\u201D':
<span class="nc" id="L268">                return I18n.l(&quot;analyzer.token.suggestion.useDoubleQuotes&quot;);</span>
            case '\u2018':
            case '\u2019':
<span class="nc" id="L271">                return I18n.l(&quot;analyzer.token.suggestion.useStraightQuotes&quot;);</span>
            case '\u2013':
            case '\u2014':
<span class="nc" id="L274">                return I18n.l(&quot;analyzer.token.suggestion.useRegularMinus&quot;);</span>
            default:
<span class="nc bnc" id="L276" title="All 10 branches missed.">                if (Character.isWhitespace(badChar) &amp;&amp; badChar != ' ' &amp;&amp; badChar != '\t' &amp;&amp; badChar != '\n' &amp;&amp; badChar != '\r') {</span>
<span class="nc" id="L277">                    return I18n.l(&quot;analyzer.token.suggestion.replaceUnusualWhitespace&quot;);</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">                } else if (badChar &gt; 127) {</span>
<span class="nc" id="L279">                    return I18n.l(&quot;analyzer.token.suggestion.onlyAsciiCharacters&quot;);</span>
                } else {
<span class="nc" id="L281">                    return I18n.l(&quot;analyzer.token.suggestion.removeOrReplaceInvalidChar&quot;);</span>
                }
        }
    }
    
    /**
     * Generates a suggestion for unrecognized token errors.
     * 
     * @param tokenText the unrecognized token text
     * @return a suggestion string
     */
    private String generateUnrecognizedTokenSuggestion(String tokenText) {
<span class="nc bnc" id="L293" title="All 4 branches missed.">        if (tokenText == null || tokenText.isEmpty()) {</span>
<span class="nc" id="L294">            return I18n.l(&quot;analyzer.token.suggestion.checkSyntaxAtLocation&quot;);</span>
        }

        // Common misspellings and suggestions
<span class="nc bnc" id="L298" title="All 5 branches missed.">        switch (tokenText.toLowerCase()) {</span>
            case &quot;mesage&quot;:
<span class="nc" id="L300">                return I18n.l(&quot;analyzer.token.suggestion.misspelledMessage&quot;);</span>
            case &quot;singal&quot;:
<span class="nc" id="L302">                return I18n.l(&quot;analyzer.token.suggestion.misspelledSignal&quot;);</span>
            case &quot;timer&quot;:
<span class="nc" id="L304">                return I18n.l(&quot;analyzer.token.suggestion.useMsTimer&quot;);</span>
            case &quot;include&quot;:
<span class="nc" id="L306">                return I18n.l(&quot;analyzer.token.suggestion.useIncludeDirective&quot;);</span>
            default:
<span class="nc bnc" id="L308" title="All 2 branches missed.">                if (tokenText.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;)) {</span>
<span class="nc" id="L309">                    return I18n.l(&quot;analyzer.token.suggestion.checkIdentifierSpelling&quot;);</span>
                } else {
<span class="nc" id="L311">                    return I18n.l(&quot;analyzer.token.suggestion.checkTokenSyntax&quot;);</span>
                }
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>