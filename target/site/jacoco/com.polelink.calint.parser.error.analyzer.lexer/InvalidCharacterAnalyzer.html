<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InvalidCharacterAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.lexer</a> &gt; <span class="el_class">InvalidCharacterAnalyzer</span></div><h1>InvalidCharacterAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">300 of 300</td><td class="ctr2">0%</td><td class="bar">56 of 56</td><td class="ctr2">0%</td><td class="ctr1">40</td><td class="ctr2">40</td><td class="ctr1">52</td><td class="ctr2">52</td><td class="ctr1">7</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a4"><a href="InvalidCharacterAnalyzer.java.html#L149" class="el_method">getSimilarSymbolSuggestion(char)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="85" alt="85"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="InvalidCharacterAnalyzer.java.html#L33" class="el_method">analyzeLexerError(RecognitionException, Object)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="59" alt="59"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h0">16</td><td class="ctr2" id="i0">16</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="InvalidCharacterAnalyzer.java.html#L102" class="el_method">generateCategoryBasedSuggestion(char)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="74" height="10" title="53" alt="53"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="InvalidCharacterAnalyzer.java.html#L137" class="el_method">isPossibleSymbolConfusion(char)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="46" alt="46"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="28" alt="28"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">15</td><td class="ctr2" id="g0">15</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="InvalidCharacterAnalyzer.java.html#L61" class="el_method">generateSuggestion(char)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="45" alt="45"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="9" alt="9"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="InvalidCharacterAnalyzer.java.html#L22" class="el_method">InvalidCharacterAnalyzer()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="InvalidCharacterAnalyzer.java.html#L27" class="el_method">canAnalyzeLexerError(ErrorContext)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>