<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReportFormat.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.report</a> &gt; <span class="el_source">ReportFormat.java</span></div><h1>ReportFormat.java</h1><pre class="source lang-java linenums">package com.polelink.calint.report;

/**
 * Specifies the output format for the analysis report.
 */
<span class="nc" id="L6">public enum ReportFormat {</span>
    /**
     * Plain text output directly to the console, typically human-readable.
     */
<span class="nc" id="L10">    CONSOLE,</span>
    /**
     * JSON format, facilitating machine parsing and integration.
     */
<span class="nc" id="L14">    JSON,</span>
    /**
     * HTML format, providing a rich web page report.
     */
<span class="nc" id="L18">    HTML,</span>
    /**
     * XML format (optional, similar to Checkstyle or PMD output).
     */
<span class="nc" id="L22">    XML,</span>
    /**
     * Static Analysis Results Interchange Format (a standard format for static analysis results).
     */
<span class="nc" id="L26">    SARIF</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>