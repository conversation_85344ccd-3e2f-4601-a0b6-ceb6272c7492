<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConsoleReportGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.report</a> &gt; <span class="el_source">ConsoleReportGenerator.java</span></div><h1>ConsoleReportGenerator.java</h1><pre class="source lang-java linenums">package com.polelink.calint.report;

import java.io.IOException;
import java.io.PrintStream;
import java.util.List;
import java.util.Optional;

import com.polelink.calint.config.AppConfiguration;
import com.polelink.calint.util.SourceLocation;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.issue.IssueObject;

public class ConsoleReportGenerator implements ReportGenerator {
    private final PrintStream out;

    // ANSI escape codes for colors
    public static final String ANSI_RESET = &quot;\u001B[0m&quot;;
    public static final String ANSI_RED = &quot;\u001B[31m&quot;;
    public static final String ANSI_YELLOW = &quot;\u001B[33m&quot;;
    public static final String ANSI_BLUE = &quot;\u001B[34m&quot;;
    // INFO will use default terminal color (implicitly via RESET)

    public ConsoleReportGenerator() {
<span class="nc" id="L24">        this(System.out); // This will call the constructor below, initializing both 'out' and 'messageProvider'</span>
<span class="nc" id="L25">    }</span>

<span class="nc" id="L27">    public ConsoleReportGenerator(PrintStream out) {</span>
<span class="nc" id="L28">        this.out = out;</span>
<span class="nc" id="L29">    }</span>

    @Override
    public void generateReport(List&lt;IssueObject&gt; issues, AppConfiguration configuration) throws IOException {
<span class="nc bnc" id="L33" title="All 4 branches missed.">        if (issues == null || issues.isEmpty()) {</span>
<span class="nc" id="L34">            out.println(I18n.l(&quot;general.noIssuesFound&quot;));</span>
<span class="nc" id="L35">            return;</span>
        }

<span class="nc bnc" id="L38" title="All 2 branches missed.">        for (IssueObject issue : issues) {</span>
<span class="nc" id="L39">            SourceLocation location = issue.getLocation();</span>
<span class="nc" id="L40">            String severityString = issue.getSeverity().toString().toLowerCase();</span>
            String coloredSeverity;
<span class="nc bnc" id="L42" title="All 5 branches missed.">            switch (issue.getSeverity()) {</span>
                case ERROR:
<span class="nc" id="L44">                    coloredSeverity = ANSI_RED + severityString + ANSI_RESET;</span>
<span class="nc" id="L45">                    break;</span>
                case WARNING:
<span class="nc" id="L47">                    coloredSeverity = ANSI_YELLOW + severityString + ANSI_RESET;</span>
<span class="nc" id="L48">                    break;</span>
                case INFO:
<span class="nc" id="L50">                    coloredSeverity = severityString; // No specific color, uses default</span>
<span class="nc" id="L51">                    break;</span>
                case STYLE:
<span class="nc" id="L53">                    coloredSeverity = ANSI_BLUE + severityString + ANSI_RESET;</span>
<span class="nc" id="L54">                    break;</span>
                default:
<span class="nc" id="L56">                    coloredSeverity = severityString;</span>
                    break;
            }

<span class="nc" id="L60">            System.out.printf(&quot;%s:%d:%d: %s: %s [%s]%n&quot;,</span>
<span class="nc" id="L61">                    location.getFilePath(),</span>
<span class="nc" id="L62">                    location.getStartLine(),</span>
<span class="nc" id="L63">                    location.getStartColumn(),</span>
                    coloredSeverity,
<span class="nc" id="L65">                    issue.getMessage(),</span>
<span class="nc" id="L66">                    issue.getRuleId());</span>

            // Display suggestion should be improved later
            // Optional&lt;String&gt; suggestion = issue.getSuggestion();
            // if (suggestion.isPresent() &amp;&amp; !suggestion.get().isEmpty()) {
            //     System.out.printf(&quot;  Suggestion: %s%n&quot;, suggestion.get());
            // }

<span class="nc" id="L74">            Optional&lt;String&gt; codeContextOpt = issue.getCodeContext();</span>
<span class="nc bnc" id="L75" title="All 4 branches missed.">            if (codeContextOpt.isPresent() &amp;&amp; !codeContextOpt.get().isEmpty()) {</span>
<span class="nc" id="L76">                String[] contextLines = codeContextOpt.get().split(&quot;\\r?\\n&quot;);</span>
<span class="nc" id="L77">                int actualErrorSourceLineNum = issue.getLocation().getStartLine();</span>
<span class="nc" id="L78">                int startColumn = issue.getLocation().getStartColumn();</span>
<span class="nc" id="L79">                final int tabWidth = 8; // Standard tab width, can be made configurable</span>

                // Determine the index of the actual error line within the contextLines array
<span class="nc" id="L82">                int errorLineInContextIndex = 0; // Default to 0</span>
<span class="nc bnc" id="L83" title="All 4 branches missed.">                if (contextLines.length &gt; 1 &amp;&amp; actualErrorSourceLineNum &gt; 1) {</span>
                    // If context has more than one line and error is not on the first line of the file,
                    // assume error line is the second line in context (index 1).
                    // This relies on CaplAntlrErrorListener providing prev, current, next.
<span class="nc" id="L87">                    errorLineInContextIndex = 1;</span>
                    // A more robust way would be for IssueObject to carry this index directly.
                }
                // Ensure index is valid
<span class="nc bnc" id="L91" title="All 2 branches missed.">                if (errorLineInContextIndex &gt;= contextLines.length) {</span>
<span class="nc" id="L92">                    errorLineInContextIndex = contextLines.length - 1;</span>
                }
<span class="nc bnc" id="L94" title="All 2 branches missed.">                if (errorLineInContextIndex &lt; 0) { // Should not happen with above logic</span>
<span class="nc" id="L95">                    errorLineInContextIndex = 0;</span>
                }

<span class="nc bnc" id="L98" title="All 2 branches missed.">                for (int i = 0; i &lt; contextLines.length; i++) {</span>
<span class="nc" id="L99">                    String currentContextLineContent = contextLines[i];</span>
                    // Calculate the original source line number for this context line
<span class="nc" id="L101">                    int sourceLineNumForThisContextLine = actualErrorSourceLineNum - errorLineInContextIndex + i;</span>
<span class="nc" id="L102">                    String lineNumberFormatted = String.format(&quot;%5d&quot;, sourceLineNumForThisContextLine);</span>
<span class="nc" id="L103">                    System.out.printf(&quot;  %s | %s%n&quot;, lineNumberFormatted, currentContextLineContent);</span>

                    // If this is the actual error line, print the caret pointer
<span class="nc bnc" id="L106" title="All 2 branches missed.">                    if (i == errorLineInContextIndex) {</span>
<span class="nc" id="L107">                        StringBuilder pointerPrefix = new StringBuilder(&quot;  &quot;);</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">                        for (int j = 0; j &lt; lineNumberFormatted.length(); j++) {</span>
<span class="nc" id="L109">                            pointerPrefix.append(' ');</span>
                        }
<span class="nc" id="L111">                        pointerPrefix.append(&quot; | &quot;);</span>

<span class="nc" id="L113">                        StringBuilder pointer = new StringBuilder(pointerPrefix.toString());</span>
<span class="nc" id="L114">                        int currentVisualColumn = 0;</span>

                        // Use currentContextLineContent (the actual single error line) for caret calculation
<span class="nc bnc" id="L117" title="All 2 branches missed.">                        for (int k = 0; k &lt; startColumn - 1; k++) {</span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">                            if (k &gt;= currentContextLineContent.length()) {</span>
<span class="nc" id="L119">                                pointer.append(' ');</span>
<span class="nc" id="L120">                                currentVisualColumn++;</span>
<span class="nc" id="L121">                                continue;</span>
                            }
<span class="nc" id="L123">                            char ch = currentContextLineContent.charAt(k);</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">                            if (ch == '\t') {</span>
<span class="nc" id="L125">                                int spacesForThisTab = tabWidth - (currentVisualColumn % tabWidth);</span>
<span class="nc bnc" id="L126" title="All 2 branches missed.">                                for (int l = 0; l &lt; spacesForThisTab; l++) {</span>
<span class="nc" id="L127">                                    pointer.append(' ');</span>
                                }
<span class="nc" id="L129">                                currentVisualColumn += spacesForThisTab;</span>
<span class="nc" id="L130">                            } else {</span>
<span class="nc" id="L131">                                pointer.append(' ');</span>
<span class="nc" id="L132">                                currentVisualColumn++;</span>
                            }
                        }
<span class="nc" id="L135">                        pointer.append('^');</span>
<span class="nc" id="L136">                        System.out.printf(&quot;%s%n&quot;, pointer.toString());</span>
                    }
                }
            }
<span class="nc" id="L140">        }</span>
<span class="nc" id="L141">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>