<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.ast.node.declarations</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.ast.node.declarations</span></div><h1>com.polelink.calint.ast.node.declarations</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,047 of 3,047</td><td class="ctr2">0%</td><td class="bar">420 of 420</td><td class="ctr2">0%</td><td class="ctr1">429</td><td class="ctr2">429</td><td class="ctr1">615</td><td class="ctr2">615</td><td class="ctr1">218</td><td class="ctr2">218</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a8"><a href="VariableDeclaratorNode.java.html" class="el_source">VariableDeclaratorNode.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="522" alt="522"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="90" alt="90"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">72</td><td class="ctr2" id="g0">72</td><td class="ctr1" id="h1">93</td><td class="ctr2" id="i1">93</td><td class="ctr1" id="j3">27</td><td class="ctr2" id="k3">27</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="ConstantDeclarationNode.java.html" class="el_source">ConstantDeclarationNode.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="437" alt="437"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="68" alt="68"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">56</td><td class="ctr2" id="g1">56</td><td class="ctr1" id="h0">95</td><td class="ctr2" id="i0">95</td><td class="ctr1" id="j6">22</td><td class="ctr2" id="k6">22</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a3"><a href="ParameterDeclarationNode.java.html" class="el_source">ParameterDeclarationNode.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="407" alt="407"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="50" alt="50"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f5">48</td><td class="ctr2" id="g5">48</td><td class="ctr1" id="h4">71</td><td class="ctr2" id="i4">71</td><td class="ctr1" id="j5">23</td><td class="ctr2" id="k5">23</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a4"><a href="ParameterListNode.java.html" class="el_source">ParameterListNode.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="350" alt="350"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="38" alt="38"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">53</td><td class="ctr2" id="g2">53</td><td class="ctr1" id="h2">82</td><td class="ctr2" id="i2">82</td><td class="ctr1" id="j0">34</td><td class="ctr2" id="k0">34</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a2"><a href="FunctionDefinitionNode.java.html" class="el_source">FunctionDefinitionNode.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="350" alt="350"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="46" alt="46"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">48</td><td class="ctr2" id="g6">48</td><td class="ctr1" id="h3">75</td><td class="ctr2" id="i3">75</td><td class="ctr1" id="j4">25</td><td class="ctr2" id="k4">25</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a5"><a href="TypedefDefinitionNode.java.html" class="el_source">TypedefDefinitionNode.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="345" alt="345"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="42" alt="42"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">50</td><td class="ctr2" id="g3">50</td><td class="ctr1" id="h5">71</td><td class="ctr2" id="i5">71</td><td class="ctr1" id="j2">29</td><td class="ctr2" id="k2">29</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="VariableDeclarationStatementNode.java.html" class="el_source">VariableDeclarationStatementNode.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="328" alt="328"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="38" alt="38"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">49</td><td class="ctr2" id="g4">49</td><td class="ctr1" id="h6">69</td><td class="ctr2" id="i6">69</td><td class="ctr1" id="j1">30</td><td class="ctr2" id="k1">30</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a0"><a href="CaplVarDeclarationNode.java.html" class="el_source">CaplVarDeclarationNode.java</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="263" alt="263"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="46" alt="46"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">46</td><td class="ctr2" id="g7">46</td><td class="ctr1" id="h7">47</td><td class="ctr2" id="i7">47</td><td class="ctr1" id="j7">22</td><td class="ctr2" id="k7">22</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a6"><a href="TypeDefinitionNode.java.html" class="el_source">TypeDefinitionNode.java</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="45" alt="45"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">7</td><td class="ctr2" id="g8">7</td><td class="ctr1" id="h8">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j8">6</td><td class="ctr2" id="k8">6</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>