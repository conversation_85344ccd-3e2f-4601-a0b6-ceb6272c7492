<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConstantDeclarationNode</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.ast.node.declarations</a> &gt; <span class="el_class">ConstantDeclarationNode</span></div><h1>ConstantDeclarationNode</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">422 of 422</td><td class="ctr2">0%</td><td class="bar">68 of 68</td><td class="ctr2">0%</td><td class="ctr1">55</td><td class="ctr2">55</td><td class="ctr1">92</td><td class="ctr2">92</td><td class="ctr1">21</td><td class="ctr2">21</td></tr></tfoot><tbody><tr><td id="a6"><a href="ConstantDeclarationNode.java.html#L326" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="56" alt="56"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="ConstantDeclarationNode.java.html#L72" class="el_method">ConstantDeclarationNode(SourceLocation, TypeSpecifierNode, List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="48" alt="48"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="ConstantDeclarationNode.java.html#L101" class="el_method">ConstantDeclarationNode(SourceLocation, TypeSpecifierNode, InitDeclaratorNode)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="42" alt="42"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="ConstantDeclarationNode.java.html#L130" class="el_method">ConstantDeclarationNode(SourceLocation, IdentifierNode, ExpressionNode)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="41" alt="41"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="ConstantDeclarationNode.java.html#L290" class="el_method">getChildren()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="40" alt="40"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="ConstantDeclarationNode.java.html#L246" class="el_method">getConstantNames()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="35" alt="35"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a20"><a href="ConstantDeclarationNode.java.html#L315" class="el_method">toString()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="35" alt="35"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a16"><a href="ConstantDeclarationNode.java.html#L340" class="el_method">hashCode()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="35" alt="35"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a2"><a href="ConstantDeclarationNode.java.html#L279" class="el_method">areAllValuesConstant()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="15" alt="15"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="ConstantDeclarationNode.java.html#L237" class="el_method">getFirstInitDeclarator()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="11" alt="11"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="ConstantDeclarationNode.java.html#L266" class="el_method">areAllConstantsInitialized()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="11" alt="11"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a8"><a href="ConstantDeclarationNode.java.html#L218" class="el_method">getConstantCount()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a18"><a href="ConstantDeclarationNode.java.html#L200" class="el_method">isTypedConstant()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="8" alt="8"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a19"><a href="ConstantDeclarationNode.java.html#L209" class="el_method">isUntypedConstant()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="8" alt="8"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a17"><a href="ConstantDeclarationNode.java.html#L227" class="el_method">isMultipleConstants()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="8" alt="8"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a0"><a href="ConstantDeclarationNode.java.html#L310" class="el_method">accept(AstVisitor, Object)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a12"><a href="ConstantDeclarationNode.java.html#L155" class="el_method">getDeclarationType()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a15"><a href="ConstantDeclarationNode.java.html#L164" class="el_method">getTypeSpecifier()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a14"><a href="ConstantDeclarationNode.java.html#L173" class="el_method">getInitDeclarators()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a9"><a href="ConstantDeclarationNode.java.html#L182" class="el_method">getConstantName()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a11"><a href="ConstantDeclarationNode.java.html#L191" class="el_method">getConstantValue()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>