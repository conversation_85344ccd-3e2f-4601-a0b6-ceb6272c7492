<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VariableSemanticHelper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.helper</a> &gt; <span class="el_source">VariableSemanticHelper.java</span></div><h1>VariableSemanticHelper.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.helper;

import com.polelink.calint.ast.node.declarations.VariableDeclarationStatementNode;
import com.polelink.calint.ast.node.program.VariablesSectionNode;
import com.polelink.calint.semantic.SemanticAnalyzer;
import com.polelink.calint.semantic.symbol.SymbolKind;
import com.polelink.calint.semantic.symbol.SymbolTable;
import com.polelink.calint.ast.node.declarations.VariableDeclaratorNode;

/**
 * Helper class for semantic analysis of variable-related nodes.
 * Handles variable declarations and variables sections.
 */
<span class="nc" id="L14">public class VariableSemanticHelper {</span>

    /**
     * Visits a variable declaration statement node.
     * Defines all variables in the current scope and analyzes their initializers if present.
     */
    public static Void visitVariableDeclarationStatementNode(VariableDeclarationStatementNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
        // Process each declarator in the statement
<span class="nc bnc" id="L22" title="All 2 branches missed.">        for (VariableDeclaratorNode declarator : node.getDeclarators()) {</span>
<span class="nc" id="L23">            String varName = declarator.getIdentifier().getName();</span>
<span class="nc" id="L24">            analyzer.defineSymbol(varName, SymbolKind.VARIABLE, declarator, symTab);</span>
<span class="nc" id="L25">        }</span>

        // Continue traversal to process initializers
<span class="nc" id="L28">        analyzer.visitChildrenPublic(node, symTab);</span>
<span class="nc" id="L29">        return null;</span>
    }

    /**
     * Visits a variables section node.
     * Processes all variable declarations within the section.
     */
    public static Void visitVariablesSection(VariablesSectionNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L37">        return analyzer.visitChildrenPublic(node, symTab);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>