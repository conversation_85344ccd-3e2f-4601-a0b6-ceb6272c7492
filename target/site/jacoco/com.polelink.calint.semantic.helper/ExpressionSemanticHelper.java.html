<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ExpressionSemanticHelper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.helper</a> &gt; <span class="el_source">ExpressionSemanticHelper.java</span></div><h1>ExpressionSemanticHelper.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.helper;

import com.polelink.calint.ast.node.expressions.primary.IdentifierReferenceNode;
import com.polelink.calint.semantic.SemanticAnalyzer;
import com.polelink.calint.semantic.symbol.SymbolTable;

/**
 * Helper class for semantic analysis of expression-related nodes.
 * Handles identifier references and expression validation.
 */
<span class="nc" id="L11">public class ExpressionSemanticHelper {</span>

    /**
     * Visits an identifier reference node.
     * Checks if the identifier is declared in the current scope chain.
     */
    public static Void visitIdentifierReference(IdentifierReferenceNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L18">        String name = node.getName();</span>

        // Try to resolve the identifier in the symbol table
<span class="nc bnc" id="L21" title="All 2 branches missed.">        if (!symTab.resolve(name).isPresent()) {</span>
<span class="nc" id="L22">            analyzer.reportUndeclaredVariable(name, node.getSourceLocation());</span>
        }

<span class="nc" id="L25">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>