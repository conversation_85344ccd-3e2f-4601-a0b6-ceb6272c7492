<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StructureSemanticHelper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.helper</a> &gt; <span class="el_source">StructureSemanticHelper.java</span></div><h1>StructureSemanticHelper.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.preprocessor.CaplIncludeDirectiveNode;
import com.polelink.calint.ast.node.program.IncludesSectionNode;
import com.polelink.calint.ast.node.program.VariablesSectionNode;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.semantic.SemanticAnalyzer;
import com.polelink.calint.semantic.symbol.ScopeKind;
import com.polelink.calint.semantic.symbol.SymbolTable;

/**
 * Helper class for semantic analysis of CAPL structure-related nodes.
 * Handles includes sections, variables sections, and event handlers.
 */
<span class="nc" id="L19">public class StructureSemanticHelper {</span>

<span class="nc" id="L21">    private static final Logger LOGGER = LoggerFactory.getLogger(StructureSemanticHelper.class);</span>

    /**
     * Visits an includes section node.
     * Processes all include directives within the section.
     */
    public static Void visitIncludesSection(IncludesSectionNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L28">        return analyzer.visitChildrenPublic(node, symTab);</span>
    }

    /**
     * Visits a CAPL include directive node.
     * Include directives don't define symbols in the current scope.
     */
    public static Void visitCaplIncludeDirective(CaplIncludeDirectiveNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
        // Include directives don't define symbols in the current scope
<span class="nc" id="L37">        return null;</span>
    }

    /**
     * Visits a variables section node.
     * Processes all variable declarations within the section.
     */
    public static Void visitVariablesSection(VariablesSectionNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L45">        return analyzer.visitChildrenPublic(node, symTab);</span>
    }

    /**
     * Visits an event handler node (e.g., on key 'a', on timer t1, on message 0x100).
     * Event handlers have their own scope but can access global variables.
     */
    public static Void visitEventHandler(AstNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L53">        LOGGER.debug(I18n.l(&quot;semantic.debug.enter.event.scope&quot;));</span>

<span class="nc" id="L55">        symTab.enterScope(ScopeKind.EVENT_HANDLER, node);</span>
<span class="nc" id="L56">        analyzer.visitChildrenPublic(node, symTab);</span>
<span class="nc" id="L57">        symTab.exitScope();</span>

<span class="nc" id="L59">        LOGGER.debug(I18n.l(&quot;semantic.debug.exit.event.scope&quot;));</span>

<span class="nc" id="L61">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>