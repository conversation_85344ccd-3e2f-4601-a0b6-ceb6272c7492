<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StatementSemanticHelper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.helper</a> &gt; <span class="el_source">StatementSemanticHelper.java</span></div><h1>StatementSemanticHelper.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.ast.node.statements.BlockStatementNode;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.semantic.SemanticAnalyzer;
import com.polelink.calint.semantic.symbol.ScopeKind;
import com.polelink.calint.semantic.symbol.SymbolTable;

/**
 * Helper class for semantic analysis of statement-related nodes.
 * Handles compound statements, control flow statements, and block scopes.
 */
<span class="nc" id="L16">public class StatementSemanticHelper {</span>

<span class="nc" id="L18">    private static final Logger LOGGER = LoggerFactory.getLogger(StatementSemanticHelper.class);</span>

    /**
     * Visits a block statement node.
     * Creates a new block scope and analyzes all statements within it.
     */
    public static Void visitBlockStatementNode(BlockStatementNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L25">        LOGGER.debug(I18n.l(&quot;semantic.debug.enter.block.scope&quot;));</span>

        // Enter block scope
<span class="nc" id="L28">        symTab.enterScope(ScopeKind.BLOCK, node);</span>

        // Visit all child statements
<span class="nc" id="L31">        analyzer.visitChildrenPublic(node, symTab);</span>

        // Exit block scope
<span class="nc" id="L34">        symTab.exitScope();</span>

<span class="nc" id="L36">        LOGGER.debug(I18n.l(&quot;semantic.debug.exit.block.scope&quot;));</span>

<span class="nc" id="L38">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>