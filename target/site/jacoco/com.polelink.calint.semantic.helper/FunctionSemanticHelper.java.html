<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FunctionSemanticHelper.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.helper</a> &gt; <span class="el_source">FunctionSemanticHelper.java</span></div><h1>FunctionSemanticHelper.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.declarations.FunctionDefinitionNode;
import com.polelink.calint.ast.node.declarations.ParameterDeclarationNode;
import com.polelink.calint.i18n.I18n;
import com.polelink.calint.semantic.SemanticAnalyzer;
import com.polelink.calint.semantic.symbol.ScopeKind;
import com.polelink.calint.semantic.symbol.SymbolKind;
import com.polelink.calint.semantic.symbol.SymbolTable;

/**
 * Helper class for semantic analysis of function-related nodes.
 * Handles function definitions, parameter declarations, and function scopes.
 */
<span class="nc" id="L19">public class FunctionSemanticHelper {</span>

<span class="nc" id="L21">    private static final Logger LOGGER = LoggerFactory.getLogger(FunctionSemanticHelper.class);</span>

    /**
     * Visits a function definition node.
     * Defines the function in the current scope and analyzes its body in a new function scope.
     */
    public static Void visitFunctionDefinitionNode(FunctionDefinitionNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
        // Define function in the current scope first
<span class="nc" id="L29">        String functionName = node.getName().getName();</span>
<span class="nc" id="L30">        analyzer.defineSymbol(functionName, SymbolKind.FUNCTION, node, symTab);</span>

<span class="nc" id="L32">        LOGGER.debug(I18n.l(&quot;semantic.debug.enter.function.scope&quot;, functionName));</span>

        // Enter function scope
<span class="nc" id="L35">        symTab.enterScope(ScopeKind.FUNCTION, node);</span>

        // Visit parameters first to add them to the function scope
<span class="nc bnc" id="L38" title="All 2 branches missed.">        if (node.getParameters() != null) {</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">            for (AstNode param : node.getParameters()) {</span>
<span class="nc" id="L40">                analyzer.visit(param, symTab);</span>
<span class="nc" id="L41">            }</span>
        }

        // Visit function body
<span class="nc bnc" id="L45" title="All 2 branches missed.">        if (node.getBody() != null) {</span>
<span class="nc" id="L46">            analyzer.visit(node.getBody(), symTab);</span>
        }

        // Exit function scope
<span class="nc" id="L50">        symTab.exitScope();</span>

<span class="nc" id="L52">        LOGGER.debug(I18n.l(&quot;semantic.debug.exit.function.scope&quot;, functionName));</span>

<span class="nc" id="L54">        return null;</span>
    }

    /**
     * Visits a parameter declaration node.
     * Defines the parameter in the current function scope.
     */
    public static Void visitParameterDeclarationNode(ParameterDeclarationNode node, SymbolTable symTab, SemanticAnalyzer analyzer) {
<span class="nc" id="L62">        String paramName = node.getIdentifier().getName();</span>
<span class="nc" id="L63">        analyzer.defineSymbol(paramName, SymbolKind.PARAMETER, node, symTab);</span>
<span class="nc" id="L64">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>