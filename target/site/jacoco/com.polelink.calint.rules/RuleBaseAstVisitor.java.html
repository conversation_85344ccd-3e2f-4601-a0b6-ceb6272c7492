<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RuleBaseAstVisitor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules</a> &gt; <span class="el_source">RuleBaseAstVisitor.java</span></div><h1>RuleBaseAstVisitor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AstNode; // Import all node types
import com.polelink.calint.ast.node.declarations.FunctionDefinitionNode;
import com.polelink.calint.ast.node.declarations.ParameterDeclarationNode;
import com.polelink.calint.ast.node.declarations.ParameterListNode;
import com.polelink.calint.ast.node.declarations.TypeDefinitionNode;
import com.polelink.calint.ast.node.declarations.*;
import com.polelink.calint.ast.node.events.EventHandlerNode;
import com.polelink.calint.ast.node.events.EventParameterNode;
import com.polelink.calint.ast.node.events.EventTypeNode;
import com.polelink.calint.ast.node.events.QualifiedEventTypeNode;
import com.polelink.calint.ast.node.expressions.binary.AssignmentExpressionNode;
import com.polelink.calint.ast.node.expressions.binary.BinaryExpressionNode;
import com.polelink.calint.ast.node.expressions.binary.CommaExpressionNode;
import com.polelink.calint.ast.node.expressions.binary.ConditionalExpressionNode;
import com.polelink.calint.ast.node.expressions.capl.DelegateExpressionNode;
import com.polelink.calint.ast.node.expressions.capl.SelectorNode;
import com.polelink.calint.ast.node.expressions.capl.SignalReferenceNode;
import com.polelink.calint.ast.node.expressions.capl.SystemVariableNode;
import com.polelink.calint.ast.node.expressions.literals.ArrayLiteralNode;
import com.polelink.calint.ast.node.expressions.literals.*;
import com.polelink.calint.ast.node.expressions.literals.IdentifierNode;
import com.polelink.calint.ast.node.expressions.literals.StringLiteralNode;
import com.polelink.calint.ast.node.expressions.postfix.ArgumentExpressionListNode;
import com.polelink.calint.ast.node.expressions.postfix.ArrayAccessExpressionNode;
import com.polelink.calint.ast.node.expressions.postfix.FunctionCallExpressionNode;
import com.polelink.calint.ast.node.expressions.postfix.MemberAccessExpressionNode;
import com.polelink.calint.ast.node.expressions.primary.IdentifierReferenceNode;
import com.polelink.calint.ast.node.expressions.primary.ParenthesizedExpressionNode;
import com.polelink.calint.ast.node.expressions.primary.ThisExpressionNode;
import com.polelink.calint.ast.node.preprocessor.*;
import com.polelink.calint.ast.node.program.IncludesSectionNode;
import com.polelink.calint.ast.node.program.ProgramUnitNode;
import com.polelink.calint.ast.node.program.VariablesSectionNode;
import com.polelink.calint.ast.node.statements.BlockStatementNode;
import com.polelink.calint.ast.node.types.*;
import com.polelink.calint.ast.node.declarations.declarators.*;
import com.polelink.calint.ast.node.expressions.postfix.PostfixExpressionNode;
import com.polelink.calint.ast.node.expressions.unary.CastExpressionNode;
import com.polelink.calint.ast.node.expressions.unary.UnaryExpressionNode;
import com.polelink.calint.ast.node.initializers.*;
import com.polelink.calint.ast.node.statements.ExpressionStatementNode;
import com.polelink.calint.ast.node.statements.compound.BlockItemNode;
import com.polelink.calint.ast.node.statements.compound.CompoundStatementNode;
import com.polelink.calint.ast.node.statements.control.*;
import com.polelink.calint.ast.node.types.capl.*;

/**
 * A base implementation of {@link AstVisitor} that provides default behavior
 * for visiting nodes. Concrete visitors can extend this class and override
 * only the methods for the node types they are interested in.
 * &lt;p&gt;
 * The default behavior for each visit method is to recursively visit the children
 * of the current node.
 *
 * @param &lt;R&gt; The return type of the visit methods.
 * @param &lt;C&gt; The type of the context object passed to visit methods.
 */
<span class="nc" id="L61">public abstract class RuleBaseAstVisitor&lt;R, C&gt; implements AstVisitor&lt;R, C&gt; {</span>

    /**
     * Helper method to visit all children of a given node.
     * This method should be called by default implementations of specific
     * visit methods if the visitor wishes to continue traversal.
     *
     * @param node    The parent node whose children are to be visited.
     * @param context The context object.
     * @return Typically null, or a combined result if R is a collection.
     *         The exact return semantics depend on the concrete visitor's needs.
     */
    protected R visitChildren(AstNode node, C context) {
<span class="nc bnc" id="L74" title="All 4 branches missed.">        if (node != null &amp;&amp; node.getChildren() != null) { // Added null check for getChildren()</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">            for (AstNode child : node.getChildren()) { // Assuming AstNode has getChildren()</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">                if (child != null) {</span>
<span class="nc" id="L77">                    child.accept(this, context); // AstNode needs accept(AstVisitor&lt;R,C&gt; visitor, C context)</span>
                }
<span class="nc" id="L79">            }</span>
        }
<span class="nc" id="L81">        return null; // Default return for visitors not accumulating results</span>
    }

    @Override
    public R visit(AstNode node, C context) {
<span class="nc bnc" id="L86" title="All 2 branches missed.">        if (node == null) {</span>
<span class="nc" id="L87">            return null;</span>
        }
        // Call the node's accept method to dispatch to the correct visitor method
<span class="nc" id="L90">        return node.accept(this, context);</span>
    }

    @Override
    public R visitParameterList(ParameterListNode node, C context) {
<span class="nc" id="L95">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitParameterDeclaration(ParameterDeclarationNode node, C context) {
<span class="nc" id="L100">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCharacterLiteral(CharacterLiteralNode node, C context) {
<span class="nc" id="L105">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitFloatingPointLiteral(FloatingPointLiteralNode node, C context) {
<span class="nc" id="L110">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIntegerLiteral(IntegerLiteralNode node, C context) {
<span class="nc" id="L115">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitParenthesizedExpression(ParenthesizedExpressionNode node, C context) {
<span class="nc" id="L120">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitStringLiteral(StringLiteralNode node, C context) {
<span class="nc" id="L125">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitThisExpression(ThisExpressionNode node, C context) {
<span class="nc" id="L130">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIdentifier(IdentifierNode node, C context) {
<span class="nc" id="L135">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIdentifierReference(IdentifierReferenceNode node, C context) {
<span class="nc" id="L140">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitProgramUnit(ProgramUnitNode node, C context) {
<span class="nc" id="L145">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitArrayLiteral(ArrayLiteralNode node, C context) {
<span class="nc" id="L150">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitAssignmentExpression(AssignmentExpressionNode node, C context) {
<span class="nc" id="L155">        return visitChildren(node, context);</span>
    }



    @Override
    public R visitFunctionCallExpression(FunctionCallExpressionNode node, C context) {
<span class="nc" id="L162">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitArrayAccessExpression(ArrayAccessExpressionNode node, C context) {
<span class="nc" id="L167">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitMemberAccessExpression(MemberAccessExpressionNode node, C context) {
<span class="nc" id="L172">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitConditionalExpression(ConditionalExpressionNode node, C context) {
<span class="nc" id="L177">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCommaExpression(CommaExpressionNode node, C context) {
<span class="nc" id="L182">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitUserDefinedTypeSpecifier(UserDefinedTypeSpecifierNode node, C context) {
<span class="nc" id="L187">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitVariableDeclarator(VariableDeclaratorNode node, C context) {
<span class="nc" id="L192">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitVariableDeclarationStatement(VariableDeclarationStatementNode node, C context) {
<span class="nc" id="L197">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitTypeDefinition(TypeDefinitionNode node, C context) {
<span class="nc" id="L202">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitBlockStatement(BlockStatementNode node, C context) {
<span class="nc" id="L207">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitFunctionDefinitionNode(FunctionDefinitionNode node, C context) {
<span class="nc" id="L212">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIncludesSection(IncludesSectionNode node, C context) {
<span class="nc" id="L217">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCaplIncludeDirective(CaplIncludeDirectiveNode node, C context) {
<span class="nc" id="L222">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitVariablesSection(VariablesSectionNode node, C context) {
<span class="nc" id="L227">        return visitChildren(node, context);</span>
    }

    // Default implementations for preprocessor directive nodes
    @Override
    public R visitDefineDirective(DefineDirectiveNode node, C context) {
<span class="nc" id="L233">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIfDirective(IfDirectiveNode node, C context) {
<span class="nc" id="L238">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitElifDirective(ElifDirectiveNode node, C context) {
<span class="nc" id="L243">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitElseDirective(ElseDirectiveNode node, C context) {
<span class="nc" id="L248">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEndifDirective(EndifDirectiveNode node, C context) {
<span class="nc" id="L253">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitPragmaDirective(PragmaDirectiveNode node, C context) {
<span class="nc" id="L258">        return visitChildren(node, context);</span>
    }

    // Default implementation for CAPL-specific expression nodes
    @Override
    public R visitSelector(SelectorNode node, C context) {
<span class="nc" id="L264">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSystemVariable(SystemVariableNode node, C context) {
<span class="nc" id="L269">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSignalReference(SignalReferenceNode node, C context) {
<span class="nc" id="L274">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDelegateExpression(DelegateExpressionNode node, C context) {
<span class="nc" id="L279">        return visitChildren(node, context);</span>
    }



    @Override
    public R visitIpEndpointLiteral(IpEndpointLiteralNode node, C context) {
<span class="nc" id="L286">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIpAddressLiteral(IpAddressLiteralNode node, C context) {
<span class="nc" id="L291">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitArgumentExpressionList(ArgumentExpressionListNode node, C context) {
<span class="nc" id="L296">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitUnaryExpression(UnaryExpressionNode node, C context) {
<span class="nc" id="L301">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitBinaryExpression(BinaryExpressionNode node, C context) {
<span class="nc" id="L306">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitBuildInTypeSpecifier(BuildInTypeSpecifierNode node, C context) {
<span class="nc" id="L311">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitTimerTypeSpecifier(TimerTypeSpecifierNode node, C context) {
<span class="nc" id="L316">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitVoidTypeSpecifier(VoidTypeSpecifierNode node, C context) {
<span class="nc" id="L321">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitStructOrUnionUsage(StructOrUnionUsageNode node, C context) {
<span class="nc" id="L326">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEnumUsage(EnumUsageNode node, C context) {
<span class="nc" id="L331">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitTypedefNameUsage(TypedefNameUsageNode node, C context) {
<span class="nc" id="L336">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitExpressionStatement(ExpressionStatementNode node, C context) {
<span class="nc" id="L341">        return visitChildren(node, context);</span>
    }

    // Default implementations for control flow statement nodes
    @Override
    public R visitIfStatement(IfStatementNode node, C context) {
<span class="nc" id="L347">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitWhileStatement(WhileStatementNode node, C context) {
<span class="nc" id="L352">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitForStatement(ForStatementNode node, C context) {
<span class="nc" id="L357">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDoWhileStatement(DoWhileStatementNode node, C context) {
<span class="nc" id="L362">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSwitchStatement(SwitchStatementNode node, C context) {
<span class="nc" id="L367">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitBreakStatement(BreakStatementNode node, C context) {
<span class="nc" id="L372">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitContinueStatement(ContinueStatementNode node, C context) {
<span class="nc" id="L377">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitReturnStatement(ReturnStatementNode node, C context) {
<span class="nc" id="L382">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCaseStatement(CaseStatementNode node, C context) {
<span class="nc" id="L387">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDefaultStatement(DefaultStatementNode node, C context) {
<span class="nc" id="L392">        return visitChildren(node, context);</span>
    }

    // Default implementations for compound statement nodes
    @Override
    public R visitCompoundStatement(CompoundStatementNode node, C context) {
<span class="nc" id="L398">        return visitChildren(node, context);</span>
    }

    // Default implementations for event handling nodes
    @Override
    public R visitEventHandler(EventHandlerNode node, C context) {
<span class="nc" id="L404">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEventType(EventTypeNode node, C context) {
<span class="nc" id="L409">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEventParameter(EventParameterNode node, C context) {
<span class="nc" id="L414">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitQualifiedEventType(QualifiedEventTypeNode node, C context) {
<span class="nc" id="L419">        return visitChildren(node, context);</span>
    }

    // Default implementations for new expression nodes (first priority)
    @Override
    public R visitPostfixExpression(PostfixExpressionNode node, C context) {
<span class="nc" id="L425">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCastExpression(CastExpressionNode node, C context) {
<span class="nc" id="L430">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitTypeName(TypeNameNode node, C context) {
<span class="nc" id="L435">        return visitChildren(node, context);</span>
    }

    // Default implementations for initializer nodes (second priority)
    @Override
    public R visitInitializer(InitializerNode node, C context) {
<span class="nc" id="L441">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitInitializerList(InitializerListNode node, C context) {
<span class="nc" id="L446">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitInitializerElement(InitializerElementNode node, C context) {
<span class="nc" id="L451">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDesignation(DesignationNode node, C context) {
<span class="nc" id="L456">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDesignator(DesignatorNode node, C context) {
<span class="nc" id="L461">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDesignatorList(DesignatorListNode node, C context) {
<span class="nc" id="L466">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitArrayInitializer(ArrayInitializerNode node, C context) {
<span class="nc" id="L471">        return visitChildren(node, context);</span>
    }

    // Default implementations for declarator nodes (stage 6)
    @Override
    public R visitAbstractDeclarator(AbstractDeclaratorNode node, C context) {
<span class="nc" id="L477">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitArraySpecifier(ArraySpecifierNode node, C context) {
<span class="nc" id="L482">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDeclarator(DeclaratorNode node, C context) {
<span class="nc" id="L487">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitParameterTypeList(ParameterTypeListNode node, C context) {
<span class="nc" id="L492">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitInitDeclarator(InitDeclaratorNode node, C context) {
<span class="nc" id="L497">        return visitChildren(node, context);</span>
    }

    // Default implementations for declaration nodes (stage 7)
    @Override
    public R visitConstantDeclaration(ConstantDeclarationNode node, C context) {
<span class="nc" id="L503">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitTypedefDefinition(TypedefDefinitionNode node, C context) {
<span class="nc" id="L508">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitCaplVarDeclaration(CaplVarDeclarationNode node, C context) {
<span class="nc" id="L513">        return visitChildren(node, context);</span>
    }

    // Default implementations for aggregate type definition nodes (stage 8)
    @Override
    public R visitStructMemberDeclaration(StructMemberDeclarationNode node, C context) {
<span class="nc" id="L519">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitStructMemberDeclarator(StructMemberDeclaratorNode node, C context) {
<span class="nc" id="L524">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitStructOrUnionDefinition(StructOrUnionDefinitionNode node, C context) {
<span class="nc" id="L529">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEnumerator(EnumeratorNode node, C context) {
<span class="nc" id="L534">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEnumDefinition(EnumDefinitionNode node, C context) {
<span class="nc" id="L539">        return visitChildren(node, context);</span>
    }

    // Default implementation for remaining statement nodes (stage 9)
    @Override
    public R visitBlockItem(BlockItemNode node, C context) {
<span class="nc" id="L545">        return visitChildren(node, context);</span>
    }

    // Default implementations for CAPL object type specifiers
    @Override
    public R visitMessageTypeSpecifier(MessageTypeSpecifierNode node, C context) {
<span class="nc" id="L551">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSignalTypeSpecifier(SignalTypeSpecifierNode node, C context) {
<span class="nc" id="L556">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitFrTypeSpecifier(FrTypeSpecifierNode node, C context) {
<span class="nc" id="L561">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitA429TypeSpecifier(A429TypeSpecifierNode node, C context) {
<span class="nc" id="L566">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitAfdxTypeSpecifier(AfdxTypeSpecifierNode node, C context) {
<span class="nc" id="L571">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitMostTypeSpecifier(MostTypeSpecifierNode node, C context) {
<span class="nc" id="L576">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitPgTypeSpecifier(PgTypeSpecifierNode node, C context) {
<span class="nc" id="L581">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDiagTypeSpecifier(DiagTypeSpecifierNode node, C context) {
<span class="nc" id="L586">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitPduTypeSpecifier(PduTypeSpecifierNode node, C context) {
<span class="nc" id="L591">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitEthernetTypeSpecifier(EthernetTypeSpecifierNode node, C context) {
<span class="nc" id="L596">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIpAddressTypeSpecifier(IpAddressTypeSpecifierNode node, C context) {
<span class="nc" id="L601">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitIpEndpointTypeSpecifier(IpEndpointTypeSpecifierNode node, C context) {
<span class="nc" id="L606">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSysvarTypeSpecifier(SysvarTypeSpecifierNode node, C context) {
<span class="nc" id="L611">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitDbtypeSpecifier(DbtypeSpecifierNode node, C context) {
<span class="nc" id="L616">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitSomeipTypeSpecifier(SomeipTypeSpecifierNode node, C context) {
<span class="nc" id="L621">        return visitChildren(node, context);</span>
    }

    @Override
    public R visitJ1587TypeSpecifier(J1587TypeSpecifierNode node, C context) {
<span class="nc" id="L626">        return visitChildren(node, context);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>