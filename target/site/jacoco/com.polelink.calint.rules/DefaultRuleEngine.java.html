<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultRuleEngine.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules</a> &gt; <span class="el_source">DefaultRuleEngine.java</span></div><h1>DefaultRuleEngine.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules;

import java.util.ArrayList;
import java.util.List; // Corrected package
import java.util.Map; // Added import

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.config.AppConfiguration;
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.rules.config.RuleConfig;
import com.polelink.calint.rules.config.RuleDefinition;
import com.polelink.calint.rules.config.RulesConfigManager;
import java.util.Optional;
import java.util.Objects;
import java.util.Set;
import java.util.HashSet;

import org.reflections.Reflections;
import org.reflections.scanners.Scanners;

/**
 * A basic implementation of the {@link RuleEngine} interface.
 */
public class DefaultRuleEngine implements RuleEngine {

    private static final String RULES_BASE_PACKAGE = &quot;com.polelink.calint.rules&quot;;

<span class="nc" id="L32">    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRuleEngine.class);</span>

    private final List&lt;Rule&gt; registeredRules;
    private final RulesConfigManager rulesConfigManager;

<span class="nc" id="L37">    public DefaultRuleEngine(RulesConfigManager rulesConfigManager) {</span>
<span class="nc" id="L38">        this.rulesConfigManager = Objects.requireNonNull(rulesConfigManager, &quot;rulesConfigManager cannot be null&quot;);</span>
<span class="nc" id="L39">        this.registeredRules = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L40">        autoDiscoverAndRegisterRules();</span>
<span class="nc" id="L41">    }</span>

    @Override
    public void registerRule(Rule rule) {
<span class="nc bnc" id="L45" title="All 2 branches missed.">        if (rule != null) {</span>
<span class="nc" id="L46">            this.registeredRules.add(rule);</span>
        }
<span class="nc" id="L48">    }</span>

    @Override
    public List&lt;IssueObject&gt; analyze(Map&lt;SourceFile, AstNode&gt; asts, AppConfiguration config) { // Signature changed
<span class="nc" id="L52">        List&lt;IssueObject&gt; allNewIssues = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L54" title="All 4 branches missed.">        if (asts == null || asts.isEmpty()) { // Changed to check asts map</span>
<span class="nc" id="L55">            return allNewIssues;</span>
        }

<span class="nc bnc" id="L58" title="All 2 branches missed.">        for (Map.Entry&lt;SourceFile, AstNode&gt; entry : asts.entrySet()) { // Iterate over asts map</span>
<span class="nc" id="L59">            SourceFile sourceFile = entry.getKey();</span>
<span class="nc" id="L60">            AstNode astRootNode = entry.getValue(); // Directly get AstNode</span>

            // astRootNode can be null if AST construction failed for this specific file.
<span class="nc bnc" id="L63" title="All 2 branches missed.">            if (astRootNode == null) {</span>
<span class="nc" id="L64">                LOGGER.warn(&quot;Skipping analysis for file {} as its AST root is null.&quot;, sourceFile.getAbsolutePath());</span>
<span class="nc" id="L65">                continue;</span>
            }

<span class="nc bnc" id="L68" title="All 2 branches missed.">            for (Rule rule : registeredRules) {</span>
<span class="nc" id="L69">                Optional&lt;RuleConfig&gt; effectiveRuleConfigOpt = this.rulesConfigManager.getEffectiveRuleConfig(rule.getRuleId());</span>

<span class="nc bnc" id="L71" title="All 2 branches missed.">                if (effectiveRuleConfigOpt.isPresent()) {</span>
<span class="nc" id="L72">                    RuleConfig effectiveRuleConfig = effectiveRuleConfigOpt.get();</span>
                    // The rule is enabled, and we have its effective configuration.
                    // Individual Rule implementations are responsible for using the severity from this effectiveRuleConfig
                    // when creating IssueObject instances, by accessing it via the AnalysisContext.
<span class="nc" id="L76">                    AnalysisContext context = new AnalysisContext(sourceFile, astRootNode, effectiveRuleConfig);</span>

                    try {
<span class="nc" id="L79">                        List&lt;IssueObject&gt; ruleIssues = rule.check(astRootNode, context);</span>
<span class="nc" id="L80">                        allNewIssues.addAll(ruleIssues);</span>
<span class="nc" id="L81">                    } catch (Exception e) {</span>
<span class="nc" id="L82">                        LOGGER.error(&quot;Error applying rule {} to file {}: {}&quot;, rule.getRuleId(), sourceFile.getAbsolutePath(), e.getMessage(), e);</span>
                        // Optionally, create an issue for the engine error itself
<span class="nc" id="L84">                    }</span>
<span class="nc" id="L85">                } else {</span>
                    // Rule is not defined in metadata, or is disabled by any configuration layer.
<span class="nc" id="L87">                    LOGGER.debug(&quot;Rule {} is disabled or not defined by effective configuration, skipping for file {}.&quot;, rule.getRuleId(), sourceFile.getAbsolutePath());</span>
                }
<span class="nc" id="L89">            }</span>
<span class="nc" id="L90">        }</span>
<span class="nc" id="L91">        return allNewIssues;</span>
    }

    private void autoDiscoverAndRegisterRules() {
<span class="nc" id="L95">        LOGGER.debug(&quot;Scanning for rules in package: {}&quot;, RULES_BASE_PACKAGE);</span>
<span class="nc" id="L96">        Reflections reflections = new Reflections(RULES_BASE_PACKAGE, Scanners.TypesAnnotated);</span>
<span class="nc" id="L97">        Set&lt;Class&lt;?&gt;&gt; annotatedClasses = reflections.getTypesAnnotatedWith(RuleIdentifier.class);</span>

<span class="nc bnc" id="L99" title="All 2 branches missed.">        if (annotatedClasses.isEmpty()) {</span>
<span class="nc" id="L100">            LOGGER.warn(&quot;No rule implementations found in package '{}' or its subpackages.&quot;, RULES_BASE_PACKAGE);</span>
<span class="nc" id="L101">            return; // No classes found, nothing to do</span>
        } else {
<span class="nc" id="L103">            LOGGER.debug(&quot;Found {} classes annotated with @RuleIdentifier in package '{}'.&quot;, annotatedClasses.size(), RULES_BASE_PACKAGE);</span>
        }

<span class="nc" id="L106">        Set&lt;String&gt; definedRuleIds = new HashSet&lt;&gt;();</span>
<span class="nc bnc" id="L107" title="All 4 branches missed.">        if (this.rulesConfigManager.getAllRuleDefinitions() != null &amp;&amp; !this.rulesConfigManager.getAllRuleDefinitions().isEmpty()) {</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">            for (RuleDefinition def : this.rulesConfigManager.getAllRuleDefinitions().values()) {</span>
<span class="nc" id="L109">                definedRuleIds.add(def.getId());</span>
<span class="nc" id="L110">            }</span>
        }

<span class="nc bnc" id="L113" title="All 2 branches missed.">        if (definedRuleIds.isEmpty()) {</span>
<span class="nc" id="L114">            LOGGER.warn(&quot;No rule definitions found in rule-metadata.yaml. No rules will be registered even if implementations exist.&quot;);</span>
            // We might still want to proceed if rules are defined but metadata is missing, 
            // but for now, strict checking is fine.
        } else {
<span class="nc" id="L118">            LOGGER.debug(&quot;Rule IDs defined in rule-metadata.yaml: {}&quot;, definedRuleIds);</span>
        }

<span class="nc bnc" id="L121" title="All 2 branches missed.">        for (Class&lt;?&gt; clazz : annotatedClasses) {</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">            if (Rule.class.isAssignableFrom(clazz)) {</span>
<span class="nc" id="L123">                RuleIdentifier ruleAnnotation = clazz.getAnnotation(RuleIdentifier.class);</span>
<span class="nc" id="L124">                String ruleIdFromAnnotation = ruleAnnotation.value();</span>

<span class="nc bnc" id="L126" title="All 2 branches missed.">                if (definedRuleIds.contains(ruleIdFromAnnotation)) {</span>
                    try {
<span class="nc" id="L128">                        Rule ruleInstance = (Rule) clazz.getDeclaredConstructor().newInstance();</span>
<span class="nc" id="L129">                        this.registerRule(ruleInstance); // Use internal registerRule method</span>
<span class="nc" id="L130">                        LOGGER.debug(&quot;Successfully instantiated and registered rule: {} (Class: {})&quot;, ruleIdFromAnnotation, clazz.getName());</span>
<span class="nc" id="L131">                    } catch (ReflectiveOperationException e) {</span>
<span class="nc" id="L132">                        LOGGER.error(&quot;Failed to instantiate rule class {}: {}&quot;, clazz.getName(), e.getMessage(), e);</span>
<span class="nc" id="L133">                    } catch (RuntimeException e) { // Catch any other unexpected runtime error during instantiation/registration</span>
<span class="nc" id="L134">                        LOGGER.error(&quot;Unexpected runtime error during instantiation/registration of rule class {}: {}&quot;, clazz.getName(), e.getMessage(), e);</span>
<span class="nc" id="L135">                    }</span>
                } else {
<span class="nc" id="L137">                    LOGGER.warn(&quot;Rule implementation {} (ID: {}) found but not defined in rule-metadata.yaml. It will not be registered.&quot;, clazz.getName(), ruleIdFromAnnotation);</span>
                }
<span class="nc" id="L139">            } else {</span>
<span class="nc" id="L140">                LOGGER.warn(&quot;Class {} is annotated with @RuleIdentifier but does not implement the Rule interface. Skipping.&quot;, clazz.getName());</span>
            }
<span class="nc" id="L142">        }</span>
<span class="nc" id="L143">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>