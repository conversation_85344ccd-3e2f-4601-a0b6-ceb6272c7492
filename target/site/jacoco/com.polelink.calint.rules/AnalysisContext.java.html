<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AnalysisContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules</a> &gt; <span class="el_source">AnalysisContext.java</span></div><h1>AnalysisContext.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules;

import com.polelink.calint.ast.node.base.AstNode; // Interface for AST nodes, moved to .node package
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.rules.config.RuleConfig;
// import com.polelink.calint.semantics.SymbolTable; // To be added when SymbolTable is implemented

/**
 * Provides contextual information to rules during the analysis process.
 * This class will be expanded as more contextual information (like symbol tables)
 * becomes available.
 */
public class AnalysisContext {

    private final SourceFile currentSourceFile;
    private final AstNode customAstRoot;
    private final RuleConfig ruleConfig; // Configuration for the specific rule being applied
    // private final SymbolTable symbolTable; // Example: To be added later

    /**
     * Constructs a new AnalysisContext.
     *
     * @param currentSourceFile The source file currently being analyzed.
     * @param customAstRoot The root of the custom Abstract Syntax Tree for the source file.
     * @param ruleConfig The configuration specific to the rule being executed. Can be null if no specific config.
     *                   // @param symbolTable The symbol table for the current scope (to be added).
     */
<span class="nc" id="L28">    public AnalysisContext(SourceFile currentSourceFile, AstNode customAstRoot, RuleConfig ruleConfig /*, SymbolTable symbolTable */) {</span>
<span class="nc bnc" id="L29" title="All 2 branches missed.">        if (currentSourceFile == null) {</span>
<span class="nc" id="L30">            throw new IllegalArgumentException(&quot;currentSourceFile cannot be null&quot;);</span>
        }
        // customAstRoot can be null if AST construction failed, rules should handle this.
        // ruleConfig can be null if the rule is using defaults or not configurable.
<span class="nc" id="L34">        this.currentSourceFile = currentSourceFile;</span>
<span class="nc" id="L35">        this.customAstRoot = customAstRoot;</span>
<span class="nc" id="L36">        this.ruleConfig = ruleConfig;</span>
        // this.symbolTable = symbolTable;
<span class="nc" id="L38">    }</span>

    /**
     * Gets the source file currently under analysis.
     *
     * @return The current {@link SourceFile}.
     */
    public SourceFile getCurrentSourceFile() {
<span class="nc" id="L46">        return currentSourceFile;</span>
    }

    /**
     * Gets the root of the custom Abstract Syntax Tree for the current source file.
     *
     * @return The {@link AstNode} representing the root of the AST, or null if not available.
     */
    public AstNode getCustomAstRoot() {
<span class="nc" id="L55">        return customAstRoot;</span>
    }

    /**
     * Gets the configuration for the specific rule currently being executed.
     *
     * @return The {@link RuleConfig} for the current rule, or null if not available or not applicable.
     */
    public RuleConfig getRuleConfig() {
<span class="nc" id="L64">        return ruleConfig;</span>
    }

    // /**
    //  * Gets the symbol table relevant to the current analysis scope.
    //  *
    //  * @return The {@link SymbolTable}.
    //  */
    // public SymbolTable getSymbolTable() {
    //     return symbolTable;
    // }

    // Additional context providers can be added here, e.g., project-wide settings, etc.
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>