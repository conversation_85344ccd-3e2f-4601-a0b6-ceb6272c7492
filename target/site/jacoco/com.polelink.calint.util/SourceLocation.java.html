<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SourceLocation.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.util</a> &gt; <span class="el_source">SourceLocation.java</span></div><h1>SourceLocation.java</h1><pre class="source lang-java linenums">package com.polelink.calint.util;

import java.util.Objects;

/**
 * Represents a precise location within a source code file.
 * This class is immutable.
 */
public final class SourceLocation {
    private final String filePath;
    private final int startLine;    // 1-based
    private final int startColumn;  // 1-based
    private final int endLine;      // 1-based
    private final int endColumn;    // 1-based

    /**
     * Constructs a new SourceLocation.
     *
     * @param filePath    The absolute path to the source file.
     * @param startLine   The starting line number (1-based).
     * @param startColumn The starting column number (1-based).
     * @param endLine     The ending line number (1-based).
     * @param endColumn   The ending column number (1-based).
     * @throws IllegalArgumentException if line/column numbers are not positive, or if the end position is before the start position.
     */
<span class="nc" id="L26">    public SourceLocation(String filePath, int startLine, int startColumn, int endLine, int endColumn) {</span>
<span class="nc" id="L27">        this.filePath = Objects.requireNonNull(filePath, &quot;filePath cannot be null&quot;);</span>

<span class="nc bnc" id="L29" title="All 8 branches missed.">        if (startLine &lt;= 0 || startColumn &lt;= 0 || endLine &lt;= 0 || endColumn &lt;= 0) {</span>
<span class="nc" id="L30">            throw new IllegalArgumentException(&quot;Line and column numbers must be positive.&quot;);</span>
        }
<span class="nc bnc" id="L32" title="All 6 branches missed.">        if (endLine &lt; startLine || (endLine == startLine &amp;&amp; endColumn &lt; startColumn)) {</span>
<span class="nc" id="L33">            throw new IllegalArgumentException(&quot;End position cannot be before start position.&quot;);</span>
        }

<span class="nc" id="L36">        this.startLine = startLine;</span>
<span class="nc" id="L37">        this.startColumn = startColumn;</span>
<span class="nc" id="L38">        this.endLine = endLine;</span>
<span class="nc" id="L39">        this.endColumn = endColumn;</span>
<span class="nc" id="L40">    }</span>

    public String getFilePath() {
<span class="nc" id="L43">        return filePath;</span>
    }

    public int getStartLine() {
<span class="nc" id="L47">        return startLine;</span>
    }

    public int getStartColumn() {
<span class="nc" id="L51">        return startColumn;</span>
    }

    public int getEndLine() {
<span class="nc" id="L55">        return endLine;</span>
    }

    public int getEndColumn() {
<span class="nc" id="L59">        return endColumn;</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L64" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L65" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc" id="L66">        SourceLocation that = (SourceLocation) o;</span>
<span class="nc bnc" id="L67" title="All 8 branches missed.">        return startLine == that.startLine &amp;&amp;</span>
               startColumn == that.startColumn &amp;&amp;
               endLine == that.endLine &amp;&amp;
               endColumn == that.endColumn &amp;&amp;
<span class="nc bnc" id="L71" title="All 2 branches missed.">               filePath.equals(that.filePath);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L76">        return Objects.hash(filePath, startLine, startColumn, endLine, endColumn);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L81">        return &quot;SourceLocation{&quot; +</span>
               &quot;filePath='&quot; + filePath + '\'' +
               &quot;, startLine=&quot; + startLine +
               &quot;, startColumn=&quot; + startColumn +
               &quot;, endLine=&quot; + endLine +
               &quot;, endColumn=&quot; + endColumn +
               '}';
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>