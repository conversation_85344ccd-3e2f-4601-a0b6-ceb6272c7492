<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PathDeserializer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.util</a> &gt; <span class="el_source">PathDeserializer.java</span></div><h1>PathDeserializer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Custom Jackson deserializer for {@link java.nio.file.Path} objects.
 */
<span class="nc" id="L13">public class PathDeserializer extends JsonDeserializer&lt;Path&gt; {</span>

    @Override
    public Path deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
<span class="nc" id="L17">        String pathString = p.getText();</span>
<span class="nc bnc" id="L18" title="All 4 branches missed.">        if (pathString == null || pathString.trim().isEmpty()) {</span>
<span class="nc" id="L19">            return null; // Or handle as an error, or return a default Path</span>
        }
<span class="nc" id="L21">        return Paths.get(pathString);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>