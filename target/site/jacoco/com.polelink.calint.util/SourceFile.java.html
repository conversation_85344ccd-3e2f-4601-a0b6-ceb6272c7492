<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SourceFile.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.util</a> &gt; <span class="el_source">SourceFile.java</span></div><h1>SourceFile.java</h1><pre class="source lang-java linenums">package com.polelink.calint.util;

import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.Objects;

/**
 * Represents a loaded CAPL source file and its metadata.
 * Generated by the InputProcessor.
 */
public final class SourceFile {
    private final Path absolutePath;    // The absolute path to the file
    private final String content;       // The textual content of the file
    private final Charset detectedCharset; // The detected charset of the file

    /**
     * Constructs a new SourceFile.
     *
     * @param absolutePath The absolute path to the file.
     * @param content The textual content of the file.
     * @param detectedCharset The detected charset of the file.
     */
<span class="nc" id="L23">    public SourceFile(Path absolutePath, String content, Charset detectedCharset) {</span>
<span class="nc" id="L24">        this.absolutePath = Objects.requireNonNull(absolutePath, &quot;absolutePath cannot be null&quot;);</span>
<span class="nc" id="L25">        this.content = Objects.requireNonNull(content, &quot;content cannot be null&quot;);</span>
<span class="nc" id="L26">        this.detectedCharset = Objects.requireNonNull(detectedCharset, &quot;detectedCharset cannot be null&quot;);</span>
<span class="nc" id="L27">    }</span>

    /**
     * Gets the absolute path to the file.
     *
     * @return The absolute path.
     */
    public Path getAbsolutePath() {
<span class="nc" id="L35">        return absolutePath;</span>
    }

    /**
     * Gets the textual content of the file.
     *
     * @return The file content.
     */
    public String getContent() {
<span class="nc" id="L44">        return content;</span>
    }

    /**
     * Gets the detected charset of the file.
     *
     * @return The detected charset.
     */
    public Charset getDetectedCharset() {
<span class="nc" id="L53">        return detectedCharset;</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L59" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc" id="L60">        SourceFile that = (SourceFile) o;</span>
<span class="nc" id="L61">        return absolutePath.equals(that.absolutePath);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L66">        return absolutePath.hashCode();</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L71">        return &quot;SourceFile{path=&quot; + absolutePath + &quot;, charset=&quot; + detectedCharset + &quot;}&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>