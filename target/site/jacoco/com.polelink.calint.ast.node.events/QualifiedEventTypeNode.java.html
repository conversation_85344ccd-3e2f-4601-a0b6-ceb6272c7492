<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>QualifiedEventTypeNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.events</a> &gt; <span class="el_source">QualifiedEventTypeNode.java</span></div><h1>QualifiedEventTypeNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.events;

import java.util.Optional;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.expressions.literals.IdentifierNode;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents a qualified event type in the AST.
 * 
 * A qualified event type includes an optional qualifier (namespace) and the event type.
 * 
 * Grammar: [qualifier.]eventType
 * 
 * Examples:
 * - CAN1.message
 * - LIN1.frame
 * - FlexRay1.flexrayFrame
 * - message (unqualified)
 */
public class QualifiedEventTypeNode extends AbstractAstNode {

    private final IdentifierNode qualifier; // Optional
    private final EventTypeNode eventType;

    /**
     * Constructs a QualifiedEventTypeNode with qualifier.
     *
     * @param sourceLocation The source location of this qualified event type.
     * @param qualifier The qualifier (namespace) for the event type.
     * @param eventType The event type.
     */
    public QualifiedEventTypeNode(SourceLocation sourceLocation, IdentifierNode qualifier, EventTypeNode eventType) {
<span class="nc" id="L37">        super(AstNodeType.EVENT_PROCEDURE, sourceLocation);</span>
        
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (eventType == null) {</span>
<span class="nc" id="L40">            throw new IllegalArgumentException(&quot;Event type cannot be null&quot;);</span>
        }
        
<span class="nc" id="L43">        this.qualifier = qualifier;</span>
<span class="nc" id="L44">        this.eventType = eventType;</span>
        
        // Add children
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (qualifier != null) {</span>
<span class="nc" id="L48">            addChild(qualifier);</span>
        }
<span class="nc" id="L50">        addChild(eventType);</span>
<span class="nc" id="L51">    }</span>

    /**
     * Constructs a QualifiedEventTypeNode without qualifier.
     *
     * @param sourceLocation The source location of this qualified event type.
     * @param eventType The event type.
     */
    public QualifiedEventTypeNode(SourceLocation sourceLocation, EventTypeNode eventType) {
<span class="nc" id="L60">        this(sourceLocation, null, eventType);</span>
<span class="nc" id="L61">    }</span>

    /**
     * Gets the qualifier.
     *
     * @return An Optional containing the qualifier, or empty if unqualified.
     */
    public Optional&lt;IdentifierNode&gt; getQualifier() {
<span class="nc" id="L69">        return Optional.ofNullable(qualifier);</span>
    }

    /**
     * Gets the event type.
     *
     * @return The event type.
     */
    public EventTypeNode getEventType() {
<span class="nc" id="L78">        return eventType;</span>
    }

    /**
     * Checks if this event type is qualified.
     *
     * @return true if there is a qualifier.
     */
    public boolean isQualified() {
<span class="nc bnc" id="L87" title="All 2 branches missed.">        return qualifier != null;</span>
    }

    /**
     * Gets the qualifier name.
     *
     * @return The qualifier name, or null if unqualified.
     */
    public String getQualifierName() {
<span class="nc bnc" id="L96" title="All 2 branches missed.">        return qualifier != null ? qualifier.getName() : null;</span>
    }

    /**
     * Gets the event type name.
     *
     * @return The event type name.
     */
    public String getEventTypeName() {
<span class="nc" id="L105">        return eventType.getEventTypeName();</span>
    }

    /**
     * Gets the fully qualified name.
     *
     * @return The fully qualified event type name.
     */
    public String getFullyQualifiedName() {
<span class="nc bnc" id="L114" title="All 2 branches missed.">        if (qualifier != null) {</span>
<span class="nc" id="L115">            return qualifier.getName() + &quot;.&quot; + eventType.getEventTypeName();</span>
        } else {
<span class="nc" id="L117">            return eventType.getEventTypeName();</span>
        }
    }

    /**
     * Checks if this is a CAN-related event.
     *
     * @return true if the qualifier indicates CAN.
     */
    public boolean isCanEvent() {
<span class="nc bnc" id="L127" title="All 4 branches missed.">        return qualifier != null &amp;&amp; qualifier.getName().toLowerCase().startsWith(&quot;can&quot;);</span>
    }

    /**
     * Checks if this is a LIN-related event.
     *
     * @return true if the qualifier indicates LIN.
     */
    public boolean isLinEvent() {
<span class="nc bnc" id="L136" title="All 4 branches missed.">        return qualifier != null &amp;&amp; qualifier.getName().toLowerCase().startsWith(&quot;lin&quot;);</span>
    }

    /**
     * Checks if this is a FlexRay-related event.
     *
     * @return true if the qualifier indicates FlexRay.
     */
    public boolean isFlexrayEvent() {
<span class="nc bnc" id="L145" title="All 4 branches missed.">        return qualifier != null &amp;&amp; qualifier.getName().toLowerCase().startsWith(&quot;flexray&quot;);</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L150">        return visitor.visitQualifiedEventType(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc bnc" id="L155" title="All 2 branches missed.">        if (qualifier != null) {</span>
<span class="nc" id="L156">            return &quot;QualifiedEventType{&quot; + qualifier.getName() + &quot;.&quot; + eventType.getEventTypeName() + '}';</span>
        } else {
<span class="nc" id="L158">            return &quot;QualifiedEventType{&quot; + eventType.getEventTypeName() + '}';</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>