<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventHandlerNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.events</a> &gt; <span class="el_source">EventHandlerNode.java</span></div><h1>EventHandlerNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.events;

import java.util.Optional;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.base.StatementNode;
import com.polelink.calint.ast.node.statements.compound.CompoundStatementNode;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents an event handler in the AST.
 * 
 * Grammar: on eventType [eventParameter] compoundStatement
 * 
 * Examples:
 * - on timer t1 { ... }
 * - on message 0x100 { ... }
 * - on key 'a' { ... }
 * - on sysvar MyVar { ... }
 */
public class EventHandlerNode extends StatementNode {

    private final EventTypeNode eventType;
    private final EventParameterNode eventParameter; // Optional
    private final CompoundStatementNode body;

    /**
     * Constructs an EventHandlerNode with event parameter.
     *
     * @param sourceLocation The source location of this event handler.
     * @param eventType The type of event this handler responds to.
     * @param eventParameter The parameter for the event (optional).
     * @param body The compound statement body of the event handler.
     */
    public EventHandlerNode(SourceLocation sourceLocation, EventTypeNode eventType, 
                           EventParameterNode eventParameter, CompoundStatementNode body) {
<span class="nc" id="L38">        super(AstNodeType.EVENT_PROCEDURE, sourceLocation);</span>
        
<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (eventType == null) {</span>
<span class="nc" id="L41">            throw new IllegalArgumentException(&quot;Event type cannot be null&quot;);</span>
        }
<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (body == null) {</span>
<span class="nc" id="L44">            throw new IllegalArgumentException(&quot;Body cannot be null&quot;);</span>
        }
        
<span class="nc" id="L47">        this.eventType = eventType;</span>
<span class="nc" id="L48">        this.eventParameter = eventParameter;</span>
<span class="nc" id="L49">        this.body = body;</span>
        
        // Add children
<span class="nc" id="L52">        addChild(eventType);</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (eventParameter != null) {</span>
<span class="nc" id="L54">            addChild(eventParameter);</span>
        }
<span class="nc" id="L56">        addChild(body);</span>
<span class="nc" id="L57">    }</span>

    /**
     * Constructs an EventHandlerNode without event parameter.
     *
     * @param sourceLocation The source location of this event handler.
     * @param eventType The type of event this handler responds to.
     * @param body The compound statement body of the event handler.
     */
    public EventHandlerNode(SourceLocation sourceLocation, EventTypeNode eventType, CompoundStatementNode body) {
<span class="nc" id="L67">        this(sourceLocation, eventType, null, body);</span>
<span class="nc" id="L68">    }</span>

    /**
     * Gets the event type.
     *
     * @return The event type this handler responds to.
     */
    public EventTypeNode getEventType() {
<span class="nc" id="L76">        return eventType;</span>
    }

    /**
     * Gets the event parameter.
     *
     * @return An Optional containing the event parameter, or empty if none.
     */
    public Optional&lt;EventParameterNode&gt; getEventParameter() {
<span class="nc" id="L85">        return Optional.ofNullable(eventParameter);</span>
    }

    /**
     * Gets the body of the event handler.
     *
     * @return The compound statement body.
     */
    public CompoundStatementNode getBody() {
<span class="nc" id="L94">        return body;</span>
    }

    /**
     * Checks if this event handler has a parameter.
     *
     * @return true if there is an event parameter.
     */
    public boolean hasEventParameter() {
<span class="nc bnc" id="L103" title="All 2 branches missed.">        return eventParameter != null;</span>
    }

    /**
     * Gets the event handler name for identification.
     * This is typically derived from the event type and parameter.
     *
     * @return A string identifying this event handler.
     */
    public String getEventHandlerName() {
<span class="nc" id="L113">        StringBuilder name = new StringBuilder();</span>
<span class="nc" id="L114">        name.append(&quot;on_&quot;).append(eventType.toString());</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">        if (eventParameter != null) {</span>
<span class="nc" id="L116">            name.append(&quot;_&quot;).append(eventParameter.toString());</span>
        }
<span class="nc" id="L118">        return name.toString();</span>
    }

    /**
     * Checks if this is a timer event handler.
     *
     * @return true if this handles timer events.
     */
    public boolean isTimerEventHandler() {
        // This would require checking the event type
        // For now, return false as a placeholder
<span class="nc" id="L129">        return false;</span>
    }

    /**
     * Checks if this is a message event handler.
     *
     * @return true if this handles message events.
     */
    public boolean isMessageEventHandler() {
        // This would require checking the event type
        // For now, return false as a placeholder
<span class="nc" id="L140">        return false;</span>
    }

    /**
     * Checks if this is a key event handler.
     *
     * @return true if this handles key events.
     */
    public boolean isKeyEventHandler() {
        // This would require checking the event type
        // For now, return false as a placeholder
<span class="nc" id="L151">        return false;</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L156">        return visitor.visitEventHandler(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L161">        StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L162">        sb.append(&quot;EventHandler{&quot;);</span>
<span class="nc" id="L163">        sb.append(&quot;eventType=&quot;).append(eventType);</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">        if (eventParameter != null) {</span>
<span class="nc" id="L165">            sb.append(&quot;, eventParameter=&quot;).append(eventParameter);</span>
        }
<span class="nc" id="L167">        sb.append(&quot;, body=&quot;).append(body);</span>
<span class="nc" id="L168">        sb.append('}');</span>
<span class="nc" id="L169">        return sb.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>