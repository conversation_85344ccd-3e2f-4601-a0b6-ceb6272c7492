<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventTypeNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.events</a> &gt; <span class="el_source">EventTypeNode.java</span></div><h1>EventTypeNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.events;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents an event type in the AST.
 * 
 * This is a base class for different types of events in CAPL:
 * - Timer events (timer, msTimer)
 * - Message events (message, frame)
 * - Key events (key)
 * - System variable events (sysvar)
 * - FlexRay events (flexrayFrame, etc.)
 * 
 * Examples:
 * - timer
 * - message
 * - key
 * - sysvar
 */
public class EventTypeNode extends AbstractAstNode {

    private final String eventTypeName;

    /**
     * Constructs an EventTypeNode.
     *
     * @param sourceLocation The source location of this event type.
     * @param eventTypeName The name of the event type.
     */
    public EventTypeNode(SourceLocation sourceLocation, String eventTypeName) {
<span class="nc" id="L35">        super(AstNodeType.EVENT_PROCEDURE, sourceLocation);</span>
        
<span class="nc bnc" id="L37" title="All 4 branches missed.">        if (eventTypeName == null || eventTypeName.trim().isEmpty()) {</span>
<span class="nc" id="L38">            throw new IllegalArgumentException(&quot;Event type name cannot be null or empty&quot;);</span>
        }
        
<span class="nc" id="L41">        this.eventTypeName = eventTypeName.trim();</span>
<span class="nc" id="L42">    }</span>

    /**
     * Gets the event type name.
     *
     * @return The name of the event type.
     */
    public String getEventTypeName() {
<span class="nc" id="L50">        return eventTypeName;</span>
    }

    /**
     * Checks if this is a timer event type.
     *
     * @return true if this is a timer event.
     */
    public boolean isTimerEvent() {
<span class="nc bnc" id="L59" title="All 4 branches missed.">        return &quot;timer&quot;.equalsIgnoreCase(eventTypeName) || &quot;msTimer&quot;.equalsIgnoreCase(eventTypeName);</span>
    }

    /**
     * Checks if this is a message event type.
     *
     * @return true if this is a message event.
     */
    public boolean isMessageEvent() {
<span class="nc bnc" id="L68" title="All 4 branches missed.">        return &quot;message&quot;.equalsIgnoreCase(eventTypeName) || &quot;frame&quot;.equalsIgnoreCase(eventTypeName);</span>
    }

    /**
     * Checks if this is a key event type.
     *
     * @return true if this is a key event.
     */
    public boolean isKeyEvent() {
<span class="nc" id="L77">        return &quot;key&quot;.equalsIgnoreCase(eventTypeName);</span>
    }

    /**
     * Checks if this is a system variable event type.
     *
     * @return true if this is a sysvar event.
     */
    public boolean isSysvarEvent() {
<span class="nc bnc" id="L86" title="All 4 branches missed.">        return &quot;sysvar&quot;.equalsIgnoreCase(eventTypeName) || &quot;envvar&quot;.equalsIgnoreCase(eventTypeName);</span>
    }

    /**
     * Checks if this is a FlexRay event type.
     *
     * @return true if this is a FlexRay event.
     */
    public boolean isFlexrayEvent() {
<span class="nc bnc" id="L95" title="All 2 branches missed.">        return eventTypeName.toLowerCase().startsWith(&quot;flexray&quot;) || </span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">               eventTypeName.toLowerCase().startsWith(&quot;fr&quot;);</span>
    }

    /**
     * Gets the event category for grouping similar events.
     *
     * @return The event category.
     */
    public EventCategory getEventCategory() {
<span class="nc bnc" id="L105" title="All 2 branches missed.">        if (isTimerEvent()) {</span>
<span class="nc" id="L106">            return EventCategory.TIMER;</span>
<span class="nc bnc" id="L107" title="All 2 branches missed.">        } else if (isMessageEvent()) {</span>
<span class="nc" id="L108">            return EventCategory.MESSAGE;</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">        } else if (isKeyEvent()) {</span>
<span class="nc" id="L110">            return EventCategory.KEY;</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">        } else if (isSysvarEvent()) {</span>
<span class="nc" id="L112">            return EventCategory.SYSVAR;</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">        } else if (isFlexrayEvent()) {</span>
<span class="nc" id="L114">            return EventCategory.FLEXRAY;</span>
        } else {
<span class="nc" id="L116">            return EventCategory.OTHER;</span>
        }
    }

    /**
     * Enumeration of event categories.
     */
<span class="nc" id="L123">    public enum EventCategory {</span>
<span class="nc" id="L124">        TIMER,</span>
<span class="nc" id="L125">        MESSAGE,</span>
<span class="nc" id="L126">        KEY,</span>
<span class="nc" id="L127">        SYSVAR,</span>
<span class="nc" id="L128">        FLEXRAY,</span>
<span class="nc" id="L129">        OTHER</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L134">        return visitor.visitEventType(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L139">        return &quot;EventType{&quot; + eventTypeName + '}';</span>
    }

    @Override
    public boolean equals(Object obj) {
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (this == obj) return true;</span>
<span class="nc bnc" id="L145" title="All 4 branches missed.">        if (obj == null || getClass() != obj.getClass()) return false;</span>
<span class="nc" id="L146">        EventTypeNode that = (EventTypeNode) obj;</span>
<span class="nc" id="L147">        return eventTypeName.equals(that.eventTypeName);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L152">        return eventTypeName.hashCode();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>