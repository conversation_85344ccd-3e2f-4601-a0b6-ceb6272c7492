<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventParameterNode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.ast.node.events</a> &gt; <span class="el_source">EventParameterNode.java</span></div><h1>EventParameterNode.java</h1><pre class="source lang-java linenums">package com.polelink.calint.ast.node.events;

import com.polelink.calint.ast.AstVisitor;
import com.polelink.calint.ast.node.base.AbstractAstNode;
import com.polelink.calint.ast.node.base.AstNodeType;
import com.polelink.calint.ast.node.base.ExpressionNode;
import com.polelink.calint.util.SourceLocation;

/**
 * Represents an event parameter in the AST.
 * 
 * Event parameters specify which specific instance of an event type to handle.
 * 
 * Examples:
 * - Timer name: t1, myTimer
 * - Message ID: 0x100, 0x200-0x2FF
 * - Key character: 'a', 'Enter'
 * - System variable name: MyVar
 */
public class EventParameterNode extends AbstractAstNode {

    private final ExpressionNode parameterExpression;

    /**
     * Constructs an EventParameterNode.
     *
     * @param sourceLocation The source location of this event parameter.
     * @param parameterExpression The expression representing the parameter.
     */
    public EventParameterNode(SourceLocation sourceLocation, ExpressionNode parameterExpression) {
<span class="nc" id="L31">        super(AstNodeType.EVENT_PROCEDURE, sourceLocation);</span>
        
<span class="nc bnc" id="L33" title="All 2 branches missed.">        if (parameterExpression == null) {</span>
<span class="nc" id="L34">            throw new IllegalArgumentException(&quot;Parameter expression cannot be null&quot;);</span>
        }
        
<span class="nc" id="L37">        this.parameterExpression = parameterExpression;</span>
        
        // Add child
<span class="nc" id="L40">        addChild(parameterExpression);</span>
<span class="nc" id="L41">    }</span>

    /**
     * Gets the parameter expression.
     *
     * @return The expression representing the event parameter.
     */
    public ExpressionNode getParameterExpression() {
<span class="nc" id="L49">        return parameterExpression;</span>
    }

    /**
     * Checks if this parameter is a constant value.
     *
     * @return true if the parameter appears to be a constant.
     */
    public boolean isConstantParameter() {
        // This would require expression analysis to determine if it's truly constant
        // For now, return true as a placeholder
<span class="nc" id="L60">        return true;</span>
    }

    /**
     * Checks if this parameter is an identifier.
     *
     * @return true if the parameter is an identifier.
     */
    public boolean isIdentifierParameter() {
        // This would require checking the expression type
        // For now, return false as a placeholder
<span class="nc" id="L71">        return false;</span>
    }

    /**
     * Checks if this parameter is a range (e.g., 0x100-0x1FF).
     *
     * @return true if the parameter represents a range.
     */
    public boolean isRangeParameter() {
        // This would require checking if the expression is a range expression
        // For now, return false as a placeholder
<span class="nc" id="L82">        return false;</span>
    }

    /**
     * Gets the parameter value as a string for display purposes.
     *
     * @return A string representation of the parameter value.
     */
    public String getParameterValueString() {
<span class="nc" id="L91">        return parameterExpression.toString();</span>
    }

    @Override
    public &lt;R, C&gt; R accept(AstVisitor&lt;R, C&gt; visitor, C context) {
<span class="nc" id="L96">        return visitor.visitEventParameter(this, context);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L101">        return &quot;EventParameter{&quot; + parameterExpression + '}';</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>