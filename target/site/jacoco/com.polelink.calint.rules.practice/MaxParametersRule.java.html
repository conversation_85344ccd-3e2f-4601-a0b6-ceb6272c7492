<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MaxParametersRule.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.rules.practice</a> &gt; <span class="el_source">MaxParametersRule.java</span></div><h1>MaxParametersRule.java</h1><pre class="source lang-java linenums">package com.polelink.calint.rules.practice;

import java.util.ArrayList;
import java.util.List;

import com.polelink.calint.ast.node.preprocessor.CaplIncludeDirectiveNode;
import com.polelink.calint.ast.node.program.IncludesSectionNode;
import com.polelink.calint.ast.node.program.VariablesSectionNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.ast.node.base.AstNode;
import com.polelink.calint.ast.node.declarations.FunctionDefinitionNode;
import com.polelink.calint.ast.node.expressions.literals.IdentifierNode;
import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.util.SourceLocation;
import com.polelink.calint.i18n.I18n; // Updated import
import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.rules.AnalysisContext;
import com.polelink.calint.rules.Rule;
import com.polelink.calint.rules.RuleBaseAstVisitor;
import com.polelink.calint.rules.RuleIdentifier;
import com.polelink.calint.rules.config.RuleConfig;

@RuleIdentifier(&quot;CALINT-BEST_PRACTICE-002&quot;)
public class MaxParametersRule implements Rule {
<span class="nc" id="L28">    private static final Logger LOGGER = LoggerFactory.getLogger(MaxParametersRule.class);</span>

    public static final String RULE_ID = &quot;CALINT-BEST_PRACTICE-002&quot;;
    private static final String RULE_NAME_KEY = &quot;rule.maxParameters.name&quot;;
    private static final String RULE_DESC_KEY = &quot;rule.maxParameters.desc&quot;;
    private static final String ISSUE_MESSAGE_KEY = &quot;issue.maxParameters.exceeded&quot;;
    private static final int DEFAULT_MAX_PARAMETERS = 10;
<span class="nc" id="L35">    private static final SeverityLevel DEFAULT_SEVERITY = SeverityLevel.INFO;</span>

<span class="nc" id="L37">    public MaxParametersRule() {</span>
        // Default constructor
<span class="nc" id="L39">    }</span>

    @Override
    public String getRuleId() {
<span class="nc" id="L43">        return RULE_ID;</span>
    }

    @Override
    public String getName() {
<span class="nc" id="L48">        return I18n.l(RULE_NAME_KEY);</span>
    }

    @Override
    public String getDescription() {
<span class="nc" id="L53">        return I18n.l(RULE_DESC_KEY);</span>
    }

    @Override
    public SeverityLevel getDefaultSeverity() {
<span class="nc" id="L58">        return DEFAULT_SEVERITY;</span>
    }

    @Override
    public List&lt;IssueObject&gt; check(AstNode astNode, AnalysisContext context) {
<span class="nc" id="L63">        List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L65">        RuleConfig ruleConfig = context.getRuleConfig();</span>
<span class="nc" id="L66">        SourceFile sourceFile = context.getCurrentSourceFile();</span>

<span class="nc bnc" id="L68" title="All 4 branches missed.">        if (astNode == null || sourceFile == null) {</span>
<span class="nc" id="L69">            LOGGER.warn(&quot;MaxParametersRule: Skipping check as AstNode or SourceFile is null for file: {}&quot;,</span>
<span class="nc bnc" id="L70" title="All 4 branches missed.">                        (sourceFile != null &amp;&amp; sourceFile.getAbsolutePath() != null ? sourceFile.getAbsolutePath().toString() : &quot;unknown_file_path&quot;));</span>
<span class="nc" id="L71">            return issues;</span>
        }

<span class="nc" id="L74">        SeverityLevel currentSeverity = getDefaultSeverity();</span>
<span class="nc" id="L75">        int maxParams = DEFAULT_MAX_PARAMETERS;</span>

<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (ruleConfig != null) {</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">            if (ruleConfig.getSeverity() != null) {</span>
<span class="nc" id="L79">                currentSeverity = ruleConfig.getSeverity();</span>
            }
<span class="nc" id="L81">            maxParams = ruleConfig.getOption(&quot;maxParameters&quot;, DEFAULT_MAX_PARAMETERS);</span>
        }

<span class="nc" id="L84">        MaxParametersVisitor visitor = new MaxParametersVisitor(RULE_ID, maxParams, currentSeverity, sourceFile);</span>
<span class="nc" id="L85">        astNode.accept(visitor, context);</span>
<span class="nc" id="L86">        issues.addAll(visitor.getIssues());</span>

<span class="nc" id="L88">        return issues;</span>
    }

    private static class MaxParametersVisitor extends RuleBaseAstVisitor&lt;Void, AnalysisContext&gt; {
<span class="nc" id="L92">        private final List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>
        private final String ruleId;
        private final int maxParametersConfig;
        private final SeverityLevel severity;
        private final SourceFile sourceFile;

<span class="nc" id="L98">        public MaxParametersVisitor(String ruleId, int maxParametersConfig, SeverityLevel severity, SourceFile sourceFile) {</span>
<span class="nc" id="L99">            this.ruleId = ruleId;</span>
<span class="nc" id="L100">            this.maxParametersConfig = maxParametersConfig;</span>
<span class="nc" id="L101">            this.severity = severity;</span>
<span class="nc" id="L102">            this.sourceFile = sourceFile;</span>
<span class="nc" id="L103">        }</span>

        public List&lt;IssueObject&gt; getIssues() {
<span class="nc" id="L106">            return issues;</span>
        }

        @Override
        public Void visitFunctionDefinitionNode(FunctionDefinitionNode funcNode, AnalysisContext context) {
<span class="nc bnc" id="L111" title="All 2 branches missed.">            if (funcNode.getParameters().size() &gt; this.maxParametersConfig) {</span>
<span class="nc" id="L112">                IdentifierNode funcNameNode = funcNode.getName();</span>
<span class="nc" id="L113">                SourceLocation location = null;</span>
<span class="nc" id="L114">                String funcNameStr = &quot;[unknown_function]&quot;;</span>

<span class="nc bnc" id="L116" title="All 2 branches missed.">                if (funcNameNode != null) {</span>
<span class="nc" id="L117">                    location = funcNameNode.getSourceLocation();</span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">                    if (funcNameNode.getName() != null) {</span>
<span class="nc" id="L119">                        funcNameStr = funcNameNode.getName();</span>
                    }
                }

<span class="nc bnc" id="L123" title="All 2 branches missed.">                if (location == null) {</span>
<span class="nc" id="L124">                    location = funcNode.getSourceLocation();</span>
                }

<span class="nc bnc" id="L127" title="All 4 branches missed.">                if (location == null || location.getStartLine() == 0) {</span>
<span class="nc bnc" id="L128" title="All 4 branches missed.">                    String path = (this.sourceFile != null &amp;&amp; this.sourceFile.getAbsolutePath() != null) ? this.sourceFile.getAbsolutePath().toString() : &quot;unknown_file_path&quot;;</span>
<span class="nc" id="L129">                    location = new SourceLocation(path, 1, 1, 1, 1);</span>
                }

<span class="nc" id="L132">                String message = I18n.l(MaxParametersRule.ISSUE_MESSAGE_KEY,</span>
                        funcNameStr,
<span class="nc" id="L134">                        funcNode.getParameters().size(),</span>
<span class="nc" id="L135">                        this.maxParametersConfig);</span>

<span class="nc" id="L137">                issues.add(new IssueObject(</span>
                        this.ruleId,
                        message,
                        this.severity,
                        location
                ));
            }
<span class="nc" id="L144">            return null;</span>
        }

        @Override
        public Void visitVariablesSection(VariablesSectionNode node, AnalysisContext context) {
            // This rule doesn't specifically analyze variable sections for parameter counts.
<span class="nc" id="L150">            return null; // Avoid calling super if it's considered abstract or inaccessible</span>
        }

        @Override
        public Void visitCaplIncludeDirective(CaplIncludeDirectiveNode node, AnalysisContext context) {
            // This rule doesn't specifically analyze include directives for parameter counts.
<span class="nc" id="L156">            return null;</span>
        }

        @Override
        public Void visitIncludesSection(IncludesSectionNode node, AnalysisContext context) {
            // This rule doesn't specifically analyze includes sections for parameter counts.
<span class="nc" id="L162">            return null;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>