<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ParserErrorAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_class">ParserErrorAnalyzer</span></div><h1>ParserErrorAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">231 of 231</td><td class="ctr2">0%</td><td class="bar">38 of 38</td><td class="ctr2">0%</td><td class="ctr1">32</td><td class="ctr2">32</td><td class="ctr1">53</td><td class="ctr2">53</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a7"><a href="ParserErrorAnalyzer.java.html#L191" class="el_method">extractExpectedTokens(InputMismatchException, Recognizer)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="51" alt="51"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="ParserErrorAnalyzer.java.html#L159" class="el_method">extractPrecedingTokens(Parser, Token)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="108" height="10" title="46" alt="46"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a9"><a href="ParserErrorAnalyzer.java.html#L218" class="el_method">hasTokenPattern(List, String[])</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="101" height="10" title="43" alt="43"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="ParserErrorAnalyzer.java.html#L73" class="el_method">analyzeParserError(RecognitionException, CaplParseContext)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="32" alt="32"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="ParserErrorAnalyzer.java.html#L119" class="el_method">analyzeInputMismatchException(InputMismatchException, CaplParseContext)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="13" alt="13"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="ParserErrorAnalyzer.java.html#L57" class="el_method">isParserError(ErrorContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="9" alt="9"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="ParserErrorAnalyzer.java.html#L133" class="el_method">analyzeFailedPredicateException(FailedPredicateException, CaplParseContext)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="8" alt="8"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a11"><a href="ParserErrorAnalyzer.java.html#L33" class="el_method">ParserErrorAnalyzer(String, AnalyzerType, int, Set)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="7" alt="7"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="ParserErrorAnalyzer.java.html#L45" class="el_method">ParserErrorAnalyzer(String, AnalyzerType, Set)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="ParserErrorAnalyzer.java.html#L107" class="el_method">analyzeNoViableAltException(NoViableAltException, CaplParseContext)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="ParserErrorAnalyzer.java.html#L146" class="el_method">analyzeGenericRecognitionException(RecognitionException, CaplParseContext)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="ParserErrorAnalyzer.java.html#L93" class="el_method">detectParseContext(ErrorContext)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="ParserErrorAnalyzer.java.html#L246" class="el_method">createGenericParserError(RecognitionException, CaplParseContext, String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>