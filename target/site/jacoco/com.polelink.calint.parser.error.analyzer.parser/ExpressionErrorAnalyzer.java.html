<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ExpressionErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">ExpressionErrorAnalyzer.java</span></div><h1>ExpressionErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for expression errors in CAPL programs.
 * Handles expression rule (CAPL.g4 line 1501) including:
 * - Primary expressions: identifiers, literals, parenthesized expressions
 * - Postfix expressions: array access, function calls, member access
 * - Assignment expressions: all assignment operators
 * - CAPL-specific expressions: @systemVariable, $signal, delegate expressions
 */
public class ExpressionErrorAnalyzer extends ParserErrorAnalyzer {

    // Expression keywords and operators
<span class="nc" id="L28">    private static final Set&lt;String&gt; ASSIGNMENT_OPERATORS = Set.of(</span>
        &quot;=&quot;, &quot;+=&quot;, &quot;-=&quot;, &quot;*=&quot;, &quot;/=&quot;, &quot;%=&quot;, &quot;&amp;=&quot;, &quot;|=&quot;, &quot;^=&quot;, &quot;&lt;&lt;=&quot;, &quot;&gt;&gt;=&quot;
    );
    
<span class="nc" id="L32">    private static final Set&lt;String&gt; UNARY_OPERATORS = Set.of(</span>
        &quot;+&quot;, &quot;-&quot;, &quot;!&quot;, &quot;~&quot;, &quot;++&quot;, &quot;--&quot;, &quot;&amp;&quot;, &quot;*&quot;
    );
    
<span class="nc" id="L36">    private static final Set&lt;String&gt; BINARY_OPERATORS = Set.of(</span>
        &quot;+&quot;, &quot;-&quot;, &quot;*&quot;, &quot;/&quot;, &quot;%&quot;, &quot;&lt;&lt;&quot;, &quot;&gt;&gt;&quot;, &quot;&lt;&quot;, &quot;&gt;&quot;, &quot;&lt;=&quot;, &quot;&gt;=&quot;, 
        &quot;==&quot;, &quot;!=&quot;, &quot;&amp;&quot;, &quot;|&quot;, &quot;^&quot;, &quot;&amp;&amp;&quot;, &quot;||&quot;
    );

    // CAPL-specific expression prefixes
<span class="nc" id="L42">    private static final Set&lt;String&gt; CAPL_EXPRESSION_PREFIXES = Set.of(</span>
        &quot;@&quot;, &quot;$&quot;, &quot;delegate&quot;, &quot;this&quot;, &quot;sizeof&quot;
    );

    // Specific selectors for CAPL objects
<span class="nc" id="L47">    private static final Set&lt;String&gt; SPECIFIC_SELECTORS = Set.of(</span>
        &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;, &quot;int64&quot;, &quot;char&quot;, &quot;int&quot;, &quot;long&quot;
    );

    // Patterns for validation
<span class="nc" id="L52">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L53">    private static final Pattern HEX_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L54">    private static final Pattern DECIMAL_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>

    public ExpressionErrorAnalyzer() {
<span class="nc" id="L57">        super(&quot;ExpressionErrorAnalyzer&quot;, AnalyzerType.PARSER_EXPRESSION, 200,</span>
<span class="nc" id="L58">              Set.of(CaplParseContext.EXPRESSION, CaplParseContext.PRIMARY_EXPRESSION,</span>
                     CaplParseContext.POSTFIX_EXPRESSION, CaplParseContext.ASSIGNMENT_EXPRESSION));
<span class="nc" id="L60">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L64">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L66">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L69" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">               isExpressionError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L75">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L80">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L86">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L87">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L88">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L91">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific expression analyzers
<span class="nc bnc" id="L94" title="All 2 branches missed.">            if (isPrimaryExpressionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L95">                return analyzePrimaryExpressionError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">            } else if (isPostfixExpressionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L97">                return analyzePostfixExpressionError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">            } else if (isAssignmentExpressionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L99">                return analyzeAssignmentExpressionError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">            } else if (isSystemVariableAccessError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L101">                return analyzeSystemVariableAccessError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">            } else if (isSignalReferenceError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L103">                return analyzeSignalReferenceError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">            } else if (isDelegateExpressionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L105">                return analyzeDelegateExpressionError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L108">            return createGenericExpressionError(context);</span>

<span class="nc" id="L110">        } catch (Exception e) {</span>
<span class="nc" id="L111">            return createGenericExpressionError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is an expression error.
     */
    private boolean isExpressionError(ErrorContext context) {
<span class="nc" id="L121">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L122">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L123">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L126" title="All 2 branches missed.">        return hasExpressionOperator(precedingTokens) || </span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">               hasCaplExpressionPrefix(precedingTokens) ||</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">               hasSpecificSelector(precedingTokens);</span>
    }

    /**
     * Analyzes primary expression errors.
     */
    private ErrorResult analyzePrimaryExpressionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing identifier
<span class="nc bnc" id="L136" title="All 2 branches missed.">        if (isMissingIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L137">            return createExpressionError(</span>
                CaplErrorType.PARSER_PRIMARY_EXPRESSION_ERROR,
                &quot;analyzer.expression.primary.missingIdentifier&quot;,
                &quot;analyzer.expression.primary.missingIdentifierSuggestion&quot;,
                context
            );
        }

        // Check for missing closing parenthesis
<span class="nc bnc" id="L146" title="All 2 branches missed.">        if (isMissingClosingParenthesis(tokens, offendingText)) {</span>
<span class="nc" id="L147">            return createExpressionError(</span>
                CaplErrorType.PARSER_PRIMARY_EXPRESSION_ERROR,
                &quot;analyzer.expression.primary.missingClosingParenthesis&quot;,
                &quot;analyzer.expression.primary.missingClosingParenthesisSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L155">        return createGenericExpressionError(context);</span>
    }

    /**
     * Analyzes postfix expression errors.
     */
    private ErrorResult analyzePostfixExpressionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing array index
<span class="nc bnc" id="L163" title="All 2 branches missed.">        if (isMissingArrayIndex(tokens, offendingText)) {</span>
<span class="nc" id="L164">            return createExpressionError(</span>
                CaplErrorType.PARSER_POSTFIX_EXPRESSION_ERROR,
                &quot;analyzer.expression.postfix.missingArrayIndex&quot;,
                &quot;analyzer.expression.postfix.missingArrayIndexSuggestion&quot;,
                context
            );
        }

        // Check for missing function arguments
<span class="nc bnc" id="L173" title="All 2 branches missed.">        if (isMissingFunctionArguments(tokens, offendingText)) {</span>
<span class="nc" id="L174">            return createExpressionError(</span>
                CaplErrorType.PARSER_POSTFIX_EXPRESSION_ERROR,
                &quot;analyzer.expression.postfix.missingFunctionArguments&quot;,
                &quot;analyzer.expression.postfix.missingFunctionArgumentsSuggestion&quot;,
                context
            );
        }

        // Check for missing member name
<span class="nc bnc" id="L183" title="All 2 branches missed.">        if (isMissingMemberName(tokens, offendingText)) {</span>
<span class="nc" id="L184">            return createExpressionError(</span>
                CaplErrorType.PARSER_POSTFIX_EXPRESSION_ERROR,
                &quot;analyzer.expression.postfix.missingMemberName&quot;,
                &quot;analyzer.expression.postfix.missingMemberNameSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L192">        return createGenericExpressionError(context);</span>
    }

    /**
     * Analyzes assignment expression errors.
     */
    private ErrorResult analyzeAssignmentExpressionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing right-hand side
<span class="nc bnc" id="L200" title="All 2 branches missed.">        if (isMissingRightHandSide(tokens, offendingText)) {</span>
<span class="nc" id="L201">            return createExpressionError(</span>
                CaplErrorType.PARSER_ASSIGNMENT_EXPRESSION_ERROR,
                &quot;analyzer.expression.assignment.missingRightHandSide&quot;,
                &quot;analyzer.expression.assignment.missingRightHandSideSuggestion&quot;,
                context
            );
        }

        // Check for invalid left-hand side
<span class="nc bnc" id="L210" title="All 2 branches missed.">        if (isInvalidLeftHandSide(tokens, offendingText)) {</span>
<span class="nc" id="L211">            return createExpressionError(</span>
                CaplErrorType.PARSER_ASSIGNMENT_EXPRESSION_ERROR,
                &quot;analyzer.expression.assignment.invalidLeftHandSide&quot;,
                &quot;analyzer.expression.assignment.invalidLeftHandSideSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L219">        return createGenericExpressionError(context);</span>
    }

    /**
     * Analyzes system variable access errors.
     */
    private ErrorResult analyzeSystemVariableAccessError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing system variable name
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (isMissingSystemVariableName(tokens, offendingText)) {</span>
<span class="nc" id="L228">            return createExpressionError(</span>
                CaplErrorType.PARSER_SYSTEM_VARIABLE_ACCESS_ERROR,
                &quot;analyzer.expression.systemVariable.missingName&quot;,
                &quot;analyzer.expression.systemVariable.missingNameSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L236">        return createGenericExpressionError(context);</span>
    }

    /**
     * Analyzes signal reference errors.
     */
    private ErrorResult analyzeSignalReferenceError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing signal name
<span class="nc bnc" id="L244" title="All 2 branches missed.">        if (isMissingSignalName(tokens, offendingText)) {</span>
<span class="nc" id="L245">            return createExpressionError(</span>
                CaplErrorType.PARSER_SIGNAL_REFERENCE_ERROR,
                &quot;analyzer.expression.signal.missingName&quot;,
                &quot;analyzer.expression.signal.missingNameSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L253">        return createGenericExpressionError(context);</span>
    }

    /**
     * Analyzes delegate expression errors.
     */
    private ErrorResult analyzeDelegateExpressionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing delegate body
<span class="nc bnc" id="L261" title="All 2 branches missed.">        if (isMissingDelegateBody(tokens, offendingText)) {</span>
<span class="nc" id="L262">            return createExpressionError(</span>
                CaplErrorType.PARSER_DELEGATE_EXPRESSION_ERROR,
                &quot;analyzer.expression.delegate.missingBody&quot;,
                &quot;analyzer.expression.delegate.missingBodySuggestion&quot;,
                context
            );
        }

<span class="nc" id="L270">        return createGenericExpressionError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isPrimaryExpressionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L276" title="All 2 branches missed.">        return hasOpeningParenthesis(tokens) || </span>
<span class="nc bnc" id="L277" title="All 4 branches missed.">               offendingText.equals(&quot;(&quot;) || offendingText.equals(&quot;)&quot;);</span>
    }

    private boolean isPostfixExpressionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L281" title="All 6 branches missed.">        return hasArrayAccess(tokens) || hasFunctionCall(tokens) || hasMemberAccess(tokens);</span>
    }

    private boolean isAssignmentExpressionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L285">        return hasAssignmentOperator(tokens);</span>
    }

    private boolean isSystemVariableAccessError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L289">        return hasSystemVariablePrefix(tokens);</span>
    }

    private boolean isSignalReferenceError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L293">        return hasSignalPrefix(tokens);</span>
    }

    private boolean isDelegateExpressionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L297">        return hasKeyword(tokens, &quot;delegate&quot;);</span>
    }

    private boolean isMissingIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L301" title="All 2 branches missed.">        return hasOpeningParenthesis(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L302" title="All 4 branches missed.">               (offendingText.equals(&quot;)&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingClosingParenthesis(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L306" title="All 4 branches missed.">        return hasOpeningParenthesis(tokens) &amp;&amp; offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isMissingArrayIndex(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L310" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;[&quot;) &amp;&amp; offendingText.equals(&quot;]&quot;);</span>
    }

    private boolean isMissingFunctionArguments(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L314" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;(&quot;) &amp;&amp; offendingText.equals(&quot;)&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L315" title="All 2 branches missed.">               hasIdentifierBeforeParenthesis(tokens);</span>
    }

    private boolean isMissingMemberName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L319" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;.&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L320" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingRightHandSide(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L324" title="All 2 branches missed.">        return hasAssignmentOperator(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L325" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidLeftHandSide(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L329" title="All 2 branches missed.">        return hasAssignmentOperator(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L330" title="All 2 branches missed.">               !hasValidLeftHandSide(tokens);</span>
    }

    private boolean isMissingSystemVariableName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L334" title="All 2 branches missed.">        return hasSystemVariablePrefix(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L335" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingSignalName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L339" title="All 2 branches missed.">        return hasSignalPrefix(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L340" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingDelegateBody(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L344" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;delegate&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L345" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    // ========== Utility Methods ==========

    private boolean hasExpressionOperator(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L351" title="All 2 branches missed.">        return hasAnyKeyword(tokens, ASSIGNMENT_OPERATORS) ||</span>
<span class="nc bnc" id="L352" title="All 2 branches missed.">               hasAnyKeyword(tokens, UNARY_OPERATORS) ||</span>
<span class="nc bnc" id="L353" title="All 2 branches missed.">               hasAnyKeyword(tokens, BINARY_OPERATORS);</span>
    }

    private boolean hasCaplExpressionPrefix(List&lt;Token&gt; tokens) {
<span class="nc" id="L357">        return hasAnyKeyword(tokens, CAPL_EXPRESSION_PREFIXES);</span>
    }

    private boolean hasSpecificSelector(List&lt;Token&gt; tokens) {
<span class="nc" id="L361">        return hasAnyKeyword(tokens, SPECIFIC_SELECTORS);</span>
    }

    private boolean hasAssignmentOperator(List&lt;Token&gt; tokens) {
<span class="nc" id="L365">        return hasAnyKeyword(tokens, ASSIGNMENT_OPERATORS);</span>
    }

    private boolean hasSystemVariablePrefix(List&lt;Token&gt; tokens) {
<span class="nc" id="L369">        return hasKeyword(tokens, &quot;@&quot;);</span>
    }

    private boolean hasSignalPrefix(List&lt;Token&gt; tokens) {
<span class="nc" id="L373">        return hasKeyword(tokens, &quot;$&quot;);</span>
    }

    private boolean hasOpeningParenthesis(List&lt;Token&gt; tokens) {
<span class="nc" id="L377">        return hasKeyword(tokens, &quot;(&quot;);</span>
    }

    private boolean hasArrayAccess(List&lt;Token&gt; tokens) {
<span class="nc" id="L381">        return hasKeyword(tokens, &quot;[&quot;);</span>
    }

    private boolean hasFunctionCall(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L385" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;(&quot;) &amp;&amp; hasIdentifierBeforeParenthesis(tokens);</span>
    }

    private boolean hasMemberAccess(List&lt;Token&gt; tokens) {
<span class="nc" id="L389">        return hasKeyword(tokens, &quot;.&quot;);</span>
    }

    private boolean hasIdentifierBeforeParenthesis(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L393" title="All 4 branches missed.">        if (tokens == null || tokens.size() &lt; 2) {</span>
<span class="nc" id="L394">            return false;</span>
        }

<span class="nc bnc" id="L397" title="All 2 branches missed.">        for (int i = 0; i &lt; tokens.size() - 1; i++) {</span>
<span class="nc bnc" id="L398" title="All 2 branches missed.">            if (&quot;(&quot;.equals(tokens.get(i + 1).getText()) &amp;&amp;</span>
<span class="nc bnc" id="L399" title="All 2 branches missed.">                IDENTIFIER_PATTERN.matcher(tokens.get(i).getText()).matches()) {</span>
<span class="nc" id="L400">                return true;</span>
            }
        }
<span class="nc" id="L403">        return false;</span>
    }

    private boolean hasValidLeftHandSide(List&lt;Token&gt; tokens) {
        // Simple check for valid left-hand side (identifier or member access)
<span class="nc bnc" id="L408" title="All 4 branches missed.">        return tokens != null &amp;&amp; tokens.size() &gt; 0;</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L412" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L413">            return false;</span>
        }

<span class="nc bnc" id="L416" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L417" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L418">                return true;</span>
            }
<span class="nc" id="L420">        }</span>
<span class="nc" id="L421">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L425" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L426">            return false;</span>
        }

<span class="nc bnc" id="L429" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L430" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L431">                return true;</span>
            }
<span class="nc" id="L433">        }</span>
<span class="nc" id="L434">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L438" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L439">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L441" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L442">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createExpressionError(CaplErrorType errorType, String messageKey,
                                            String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L447">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L448">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L450">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L451">        metadata.put(&quot;errorType&quot;, &quot;expression&quot;);</span>
<span class="nc" id="L452">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L453">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L455">        return ErrorResult.builder()</span>
<span class="nc" id="L456">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L457">            .enhancedMessage(message)</span>
<span class="nc" id="L458">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L459">            .suggestion(suggestion)</span>
<span class="nc" id="L460">            .metadata(metadata)</span>
<span class="nc" id="L461">            .build();</span>
    }

    private ErrorResult createGenericExpressionError(ErrorContext context) {
<span class="nc" id="L465">        String message = I18n.l(&quot;analyzer.expression.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L466">        String suggestion = I18n.l(&quot;analyzer.expression.genericSuggestion&quot;);</span>

<span class="nc" id="L468">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L469">        metadata.put(&quot;errorType&quot;, &quot;expression_generic&quot;);</span>
<span class="nc" id="L470">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L472">        return ErrorResult.builder()</span>
<span class="nc" id="L473">            .ruleId(CaplErrorType.PARSER_EXPRESSION_ERROR.getErrorCode())</span>
<span class="nc" id="L474">            .enhancedMessage(message)</span>
<span class="nc" id="L475">            .severity(CaplErrorType.PARSER_EXPRESSION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L476">            .suggestion(suggestion)</span>
<span class="nc" id="L477">            .metadata(metadata)</span>
<span class="nc" id="L478">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>