<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BuildInTypeErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">BuildInTypeErrorAnalyzer.java</span></div><h1>BuildInTypeErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for built-in type specifier errors in CAPL programs.
 * Handles buildInTypeSpecifier rule (CAPL.g4 line 1751) including:
 * - Standard C types: char, short, int, long, float, double, signed, unsigned
 * - CAPL integral types: byte, word, dword, qword, int64
 * - Struct and enum specifiers
 */
public class BuildInTypeErrorAnalyzer extends ParserErrorAnalyzer {

    // Standard C type keywords
<span class="nc" id="L26">    private static final Set&lt;String&gt; STANDARD_C_TYPES = Set.of(</span>
        &quot;char&quot;, &quot;short&quot;, &quot;int&quot;, &quot;long&quot;, &quot;float&quot;, &quot;double&quot;, &quot;signed&quot;, &quot;unsigned&quot;
    );
    
    // CAPL integral type keywords
<span class="nc" id="L31">    private static final Set&lt;String&gt; CAPL_INTEGRAL_TYPES = Set.of(</span>
        &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;, &quot;int64&quot;
    );
    
    // Struct and enum keywords
<span class="nc" id="L36">    private static final Set&lt;String&gt; COMPOSITE_TYPE_KEYWORDS = Set.of(</span>
        &quot;struct&quot;, &quot;enum&quot;
    );

    public BuildInTypeErrorAnalyzer() {
<span class="nc" id="L41">        super(&quot;BuildInTypeErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 230,</span>
<span class="nc" id="L42">              Set.of(CaplParseContext.BUILTIN_TYPE_SPECIFIER));</span>
<span class="nc" id="L43">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L47">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L49">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L52" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">               isBuiltInTypeError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L58">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L63">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L69">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L70">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L71">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L74">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific built-in type analyzers
<span class="nc bnc" id="L77" title="All 2 branches missed.">            if (isStandardCTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L78">                return analyzeStandardCTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">            } else if (isCaplIntegralTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L80">                return analyzeCaplIntegralTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">            } else if (isCompositeTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L82">                return analyzeCompositeTypeError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L85">            return createGenericBuiltInTypeError(context);</span>

<span class="nc" id="L87">        } catch (Exception e) {</span>
<span class="nc" id="L88">            return createGenericBuiltInTypeError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a built-in type error.
     */
    private boolean isBuiltInTypeError(ErrorContext context) {
<span class="nc" id="L98">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L99">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L100">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L103" title="All 2 branches missed.">        return hasStandardCType(precedingTokens) || </span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">               hasCaplIntegralType(precedingTokens) ||</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">               hasCompositeType(precedingTokens);</span>
    }

    /**
     * Analyzes standard C type errors.
     */
    private ErrorResult analyzeStandardCTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L113" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L114">            String cType = getStandardCType(tokens);</span>
<span class="nc" id="L115">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_STANDARD_C_TYPE_ERROR,
                &quot;analyzer.builtInType.standardC.missingDeclarator&quot;,
                &quot;analyzer.builtInType.standardC.missingDeclaratorSuggestion&quot;,
                context, cType
            );
        }

        // Check for invalid type combination
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (isInvalidTypeCombination(tokens, offendingText)) {</span>
<span class="nc" id="L125">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_STANDARD_C_TYPE_ERROR,
                &quot;analyzer.builtInType.standardC.invalidCombination&quot;,
                &quot;analyzer.builtInType.standardC.invalidCombinationSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L133">        return createGenericBuiltInTypeError(context);</span>
    }

    /**
     * Analyzes CAPL integral type errors.
     */
    private ErrorResult analyzeCaplIntegralTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L141" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L142">            String integralType = getCaplIntegralType(tokens);</span>
<span class="nc" id="L143">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_CAPL_INTEGRAL_TYPE_ERROR,
                &quot;analyzer.builtInType.caplIntegral.missingDeclarator&quot;,
                &quot;analyzer.builtInType.caplIntegral.missingDeclaratorSuggestion&quot;,
                context, integralType
            );
        }

        // Check for invalid modifier
<span class="nc bnc" id="L152" title="All 2 branches missed.">        if (isInvalidModifier(tokens, offendingText)) {</span>
<span class="nc" id="L153">            String integralType = getCaplIntegralType(tokens);</span>
<span class="nc" id="L154">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_CAPL_INTEGRAL_TYPE_ERROR,
                &quot;analyzer.builtInType.caplIntegral.invalidModifier&quot;,
                &quot;analyzer.builtInType.caplIntegral.invalidModifierSuggestion&quot;,
                context, integralType
            );
        }

<span class="nc" id="L162">        return createGenericBuiltInTypeError(context);</span>
    }

    /**
     * Analyzes composite type errors (struct/enum).
     */
    private ErrorResult analyzeCompositeTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing type name
<span class="nc bnc" id="L170" title="All 2 branches missed.">        if (isMissingTypeName(tokens, offendingText)) {</span>
<span class="nc" id="L171">            String compositeType = getCompositeType(tokens);</span>
<span class="nc" id="L172">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_COMPOSITE_TYPE_ERROR,
                &quot;analyzer.builtInType.composite.missingTypeName&quot;,
                &quot;analyzer.builtInType.composite.missingTypeNameSuggestion&quot;,
                context, compositeType
            );
        }

        // Check for missing type body
<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (isMissingTypeBody(tokens, offendingText)) {</span>
<span class="nc" id="L182">            String compositeType = getCompositeType(tokens);</span>
<span class="nc" id="L183">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_COMPOSITE_TYPE_ERROR,
                &quot;analyzer.builtInType.composite.missingTypeBody&quot;,
                &quot;analyzer.builtInType.composite.missingTypeBodySuggestion&quot;,
                context, compositeType
            );
        }

        // Check for missing closing brace
<span class="nc bnc" id="L192" title="All 2 branches missed.">        if (isMissingClosingBrace(tokens, offendingText)) {</span>
<span class="nc" id="L193">            String compositeType = getCompositeType(tokens);</span>
<span class="nc" id="L194">            return createBuiltInTypeError(</span>
                CaplErrorType.PARSER_COMPOSITE_TYPE_ERROR,
                &quot;analyzer.builtInType.composite.missingClosingBrace&quot;,
                &quot;analyzer.builtInType.composite.missingClosingBraceSuggestion&quot;,
                context, compositeType
            );
        }

<span class="nc" id="L202">        return createGenericBuiltInTypeError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isStandardCTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L208">        return hasStandardCType(tokens);</span>
    }

    private boolean isCaplIntegralTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L212">        return hasCaplIntegralType(tokens);</span>
    }

    private boolean isCompositeTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L216">        return hasCompositeType(tokens);</span>
    }

    private boolean isMissingDeclarator(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L220" title="All 2 branches missed.">        return hasBuiltInType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L221" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;,&quot;));</span>
    }

    private boolean isInvalidTypeCombination(List&lt;Token&gt; tokens, String offendingText) {
        // Check for invalid combinations like &quot;float unsigned&quot;
<span class="nc bnc" id="L226" title="All 2 branches missed.">        return hasStandardCType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">               hasConflictingModifiers(tokens);</span>
    }

    private boolean isInvalidModifier(List&lt;Token&gt; tokens, String offendingText) {
        // CAPL integral types don't accept signed/unsigned modifiers
<span class="nc bnc" id="L232" title="All 2 branches missed.">        return hasCaplIntegralType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L233" title="All 4 branches missed.">               (hasKeyword(tokens, &quot;signed&quot;) || hasKeyword(tokens, &quot;unsigned&quot;));</span>
    }

    private boolean isMissingTypeName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L237" title="All 2 branches missed.">        return hasCompositeType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L238" title="All 4 branches missed.">               (offendingText.equals(&quot;{&quot;) || offendingText.equals(&quot;;&quot;));</span>
    }

    private boolean isMissingTypeBody(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L242" title="All 2 branches missed.">        return hasCompositeType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L243" title="All 2 branches missed.">               hasIdentifier(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L244" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;{&quot;);</span>
    }

    private boolean isMissingClosingBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L249" title="All 2 branches missed.">        return hasCompositeType(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">               hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">               offendingText.equals(&quot;EOF&quot;);</span>
    }

    // ========== Utility Methods ==========

    private boolean hasStandardCType(List&lt;Token&gt; tokens) {
<span class="nc" id="L258">        return hasAnyKeyword(tokens, STANDARD_C_TYPES);</span>
    }

    private boolean hasCaplIntegralType(List&lt;Token&gt; tokens) {
<span class="nc" id="L262">        return hasAnyKeyword(tokens, CAPL_INTEGRAL_TYPES);</span>
    }

    private boolean hasCompositeType(List&lt;Token&gt; tokens) {
<span class="nc" id="L266">        return hasAnyKeyword(tokens, COMPOSITE_TYPE_KEYWORDS);</span>
    }

    private boolean hasBuiltInType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L270" title="All 6 branches missed.">        return hasStandardCType(tokens) || hasCaplIntegralType(tokens) || hasCompositeType(tokens);</span>
    }

    private boolean hasConflictingModifiers(List&lt;Token&gt; tokens) {
        // Simple check for conflicting type modifiers
<span class="nc bnc" id="L275" title="All 4 branches missed.">        boolean hasFloat = hasKeyword(tokens, &quot;float&quot;) || hasKeyword(tokens, &quot;double&quot;);</span>
<span class="nc bnc" id="L276" title="All 4 branches missed.">        boolean hasSignedness = hasKeyword(tokens, &quot;signed&quot;) || hasKeyword(tokens, &quot;unsigned&quot;);</span>
<span class="nc bnc" id="L277" title="All 4 branches missed.">        return hasFloat &amp;&amp; hasSignedness;</span>
    }

    private boolean hasIdentifier(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L281" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L282">            return false;</span>
        }

<span class="nc bnc" id="L285" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">            if (token.getText().matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L287" title="All 2 branches missed.">                !STANDARD_C_TYPES.contains(token.getText()) &amp;&amp;</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">                !CAPL_INTEGRAL_TYPES.contains(token.getText()) &amp;&amp;</span>
<span class="nc bnc" id="L289" title="All 2 branches missed.">                !COMPOSITE_TYPE_KEYWORDS.contains(token.getText())) {</span>
<span class="nc" id="L290">                return true;</span>
            }
<span class="nc" id="L292">        }</span>
<span class="nc" id="L293">        return false;</span>
    }

    private String getStandardCType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L297" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L298">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L301" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L302">            String text = token.getText();</span>
<span class="nc bnc" id="L303" title="All 2 branches missed.">            if (STANDARD_C_TYPES.contains(text)) {</span>
<span class="nc" id="L304">                return text;</span>
            }
<span class="nc" id="L306">        }</span>
<span class="nc" id="L307">        return &quot;unknown&quot;;</span>
    }

    private String getCaplIntegralType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L311" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L312">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L315" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L316">            String text = token.getText();</span>
<span class="nc bnc" id="L317" title="All 2 branches missed.">            if (CAPL_INTEGRAL_TYPES.contains(text)) {</span>
<span class="nc" id="L318">                return text;</span>
            }
<span class="nc" id="L320">        }</span>
<span class="nc" id="L321">        return &quot;unknown&quot;;</span>
    }

    private String getCompositeType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L325" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L326">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L329" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L330">            String text = token.getText();</span>
<span class="nc bnc" id="L331" title="All 2 branches missed.">            if (COMPOSITE_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L332">                return text;</span>
            }
<span class="nc" id="L334">        }</span>
<span class="nc" id="L335">        return &quot;unknown&quot;;</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L339" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L340">            return false;</span>
        }

<span class="nc bnc" id="L343" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L344" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L345">                return true;</span>
            }
<span class="nc" id="L347">        }</span>
<span class="nc" id="L348">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L352" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L353">            return false;</span>
        }

<span class="nc bnc" id="L356" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L357" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L358">                return true;</span>
            }
<span class="nc" id="L360">        }</span>
<span class="nc" id="L361">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L365" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L366">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L368" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L369">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createBuiltInTypeError(CaplErrorType errorType, String messageKey,
                                             String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L374">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L375">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L377">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L378">        metadata.put(&quot;errorType&quot;, &quot;builtInType&quot;);</span>
<span class="nc" id="L379">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L380">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L382">        return ErrorResult.builder()</span>
<span class="nc" id="L383">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L384">            .enhancedMessage(message)</span>
<span class="nc" id="L385">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L386">            .suggestion(suggestion)</span>
<span class="nc" id="L387">            .metadata(metadata)</span>
<span class="nc" id="L388">            .build();</span>
    }

    private ErrorResult createGenericBuiltInTypeError(ErrorContext context) {
<span class="nc" id="L392">        String message = I18n.l(&quot;analyzer.builtInType.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L393">        String suggestion = I18n.l(&quot;analyzer.builtInType.genericSuggestion&quot;);</span>

<span class="nc" id="L395">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L396">        metadata.put(&quot;errorType&quot;, &quot;builtInType_generic&quot;);</span>
<span class="nc" id="L397">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L399">        return ErrorResult.builder()</span>
<span class="nc" id="L400">            .ruleId(CaplErrorType.PARSER_BUILTIN_TYPE_ERROR.getErrorCode())</span>
<span class="nc" id="L401">            .enhancedMessage(message)</span>
<span class="nc" id="L402">            .severity(CaplErrorType.PARSER_BUILTIN_TYPE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L403">            .suggestion(suggestion)</span>
<span class="nc" id="L404">            .metadata(metadata)</span>
<span class="nc" id="L405">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>