<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SignalTypeErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">SignalTypeErrorAnalyzer.java</span></div><h1>SignalTypeErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for signal type declaration errors in CAPL programs.
 * Handles signalTypeSpecifier rule (CAPL.g4 line 1118) including:
 * - signal keyword validation
 * - Wildcard (*) and signalVariable formats
 * - Namespace qualified signal names
 */
public class SignalTypeErrorAnalyzer extends ParserErrorAnalyzer {

    // Signal type keyword
    private static final String SIGNAL_KEYWORD = &quot;signal&quot;;

    // Patterns for signal identifier validation
<span class="nc" id="L30">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L31">    private static final Pattern QUALIFIED_SIGNAL_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*::[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L32">    private static final Pattern MULTI_LEVEL_QUALIFIED_PATTERN = Pattern.compile(&quot;([a-zA-Z_][a-zA-Z0-9_]*::)+[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public SignalTypeErrorAnalyzer() {
<span class="nc" id="L35">        super(&quot;SignalTypeErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 176,</span>
<span class="nc" id="L36">              Set.of(CaplParseContext.SIGNAL_TYPE_SPECIFIER));</span>
<span class="nc" id="L37">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L41">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L42" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L43">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L46" title="All 2 branches missed.">        return parseContext == CaplParseContext.SIGNAL_TYPE_SPECIFIER ||</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">               isSignalTypeError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L52">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L57">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L63">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L64">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L65">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L68">            String offendingText = getOffendingText(context);</span>

            // Check for missing signal keyword
<span class="nc bnc" id="L71" title="All 2 branches missed.">            if (isMissingSignalKeyword(precedingTokens, offendingText)) {</span>
<span class="nc" id="L72">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.missingKeyword&quot;,
                    &quot;analyzer.signalType.signal.missingKeywordSuggestion&quot;,
                    context
                );
            }

            // Check for missing signal identifier
<span class="nc bnc" id="L81" title="All 2 branches missed.">            if (isMissingSignalIdentifier(precedingTokens, offendingText)) {</span>
<span class="nc" id="L82">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.missingIdentifier&quot;,
                    &quot;analyzer.signalType.signal.missingIdentifierSuggestion&quot;,
                    context
                );
            }

            // Check for invalid wildcard usage
<span class="nc bnc" id="L91" title="All 2 branches missed.">            if (isInvalidWildcardUsage(precedingTokens, offendingText)) {</span>
<span class="nc" id="L92">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.invalidWildcard&quot;,
                    &quot;analyzer.signalType.signal.invalidWildcardSuggestion&quot;,
                    context
                );
            }

            // Check for invalid signal name
<span class="nc bnc" id="L101" title="All 2 branches missed.">            if (isInvalidSignalName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L102">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.invalidName&quot;,
                    &quot;analyzer.signalType.signal.invalidNameSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for invalid namespace qualifier
<span class="nc bnc" id="L112" title="All 2 branches missed.">            if (isInvalidNamespaceQualifier(precedingTokens, offendingText)) {</span>
<span class="nc" id="L113">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.invalidNamespace&quot;,
                    &quot;analyzer.signalType.signal.invalidNamespaceSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for incomplete qualified signal name
<span class="nc bnc" id="L123" title="All 2 branches missed.">            if (isIncompleteQualifiedSignalName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L124">                return createSignalTypeError(</span>
                    CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                    &quot;analyzer.signalType.signal.incompleteQualified&quot;,
                    &quot;analyzer.signalType.signal.incompleteQualifiedSuggestion&quot;,
                    context,
                    offendingText
                );
            }

<span class="nc" id="L133">            return createGenericSignalTypeError(context);</span>

<span class="nc" id="L135">        } catch (Exception e) {</span>
<span class="nc" id="L136">            return createGenericSignalTypeError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a signal type error.
     */
    private boolean isSignalTypeError(ErrorContext context) {
<span class="nc" id="L146">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L147">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L148">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L151">        return hasSignalKeyword(precedingTokens);</span>
    }

    /**
     * Checks if signal keyword is missing.
     */
    private boolean isMissingSignalKeyword(List&lt;Token&gt; tokens, String offendingText) {
        // Look for identifier that could be a signal name without keyword
<span class="nc bnc" id="L159" title="All 2 branches missed.">        return !hasSignalKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">               (IDENTIFIER_PATTERN.matcher(offendingText).matches() ||</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">                offendingText.equals(&quot;*&quot;) ||</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">                QUALIFIED_SIGNAL_PATTERN.matcher(offendingText).matches());</span>
    }

    /**
     * Checks if signal identifier is missing.
     */
    private boolean isMissingSignalIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L169" title="All 2 branches missed.">        return hasSignalKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L170" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if wildcard usage is invalid.
     */
    private boolean isInvalidWildcardUsage(List&lt;Token&gt; tokens, String offendingText) {
        // Wildcard should only be used after signal keyword
<span class="nc bnc" id="L178" title="All 4 branches missed.">        return offendingText.equals(&quot;*&quot;) &amp;&amp; !hasSignalKeyword(tokens);</span>
    }

    /**
     * Checks if signal name is invalid.
     */
    private boolean isInvalidSignalName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L185" title="All 2 branches missed.">        return hasSignalKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">               !offendingText.equals(&quot;*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">               !QUALIFIED_SIGNAL_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">               !MULTI_LEVEL_QUALIFIED_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">               !offendingText.equals(&quot;;&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;);</span>
    }

    /**
     * Checks if namespace qualifier is invalid.
     */
    private boolean isInvalidNamespaceQualifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L199" title="All 2 branches missed.">        return hasSignalKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">               offendingText.contains(&quot;::&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">               !QUALIFIED_SIGNAL_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L202" title="All 2 branches missed.">               !MULTI_LEVEL_QUALIFIED_PATTERN.matcher(offendingText).matches();</span>
    }

    /**
     * Checks if qualified signal name is incomplete.
     */
    private boolean isIncompleteQualifiedSignalName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L209" title="All 2 branches missed.">        return hasSignalKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">               (offendingText.endsWith(&quot;::&quot;) || </span>
<span class="nc bnc" id="L211" title="All 2 branches missed.">                offendingText.startsWith(&quot;::&quot;) ||</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">                offendingText.contains(&quot;:::&quot;));</span>
    }

    /**
     * Checks if tokens contain the signal keyword.
     */
    private boolean hasSignalKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L219" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L220">            return false;</span>
        }

<span class="nc bnc" id="L223" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">            if (SIGNAL_KEYWORD.equals(token.getText())) {</span>
<span class="nc" id="L225">                return true;</span>
            }
<span class="nc" id="L227">        }</span>
<span class="nc" id="L228">        return false;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L235" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L236">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L238" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L239">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a signal type error result.
     */
    private ErrorResult createSignalTypeError(CaplErrorType errorType, String messageKey,
                                            String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L247">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L248">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L250">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L251">        metadata.put(&quot;errorType&quot;, &quot;signalType&quot;);</span>
<span class="nc" id="L252">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L253">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L255">        return ErrorResult.builder()</span>
<span class="nc" id="L256">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L257">            .enhancedMessage(message)</span>
<span class="nc" id="L258">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L259">            .suggestion(suggestion)</span>
<span class="nc" id="L260">            .metadata(metadata)</span>
<span class="nc" id="L261">            .build();</span>
    }

    /**
     * Creates a generic signal type error result.
     */
    private ErrorResult createGenericSignalTypeError(ErrorContext context) {
<span class="nc" id="L268">        String message = I18n.l(&quot;analyzer.signalType.signal.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L269">        String suggestion = I18n.l(&quot;analyzer.signalType.signal.genericSuggestion&quot;);</span>

<span class="nc" id="L271">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L272">        metadata.put(&quot;errorType&quot;, &quot;signalType_generic&quot;);</span>
<span class="nc" id="L273">        metadata.put(&quot;signalType&quot;, &quot;signal&quot;);</span>
<span class="nc" id="L274">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L276">        return ErrorResult.builder()</span>
<span class="nc" id="L277">            .ruleId(CaplErrorType.PARSER_SIGNAL_TYPE_ERROR.getErrorCode())</span>
<span class="nc" id="L278">            .enhancedMessage(message)</span>
<span class="nc" id="L279">            .severity(CaplErrorType.PARSER_SIGNAL_TYPE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L280">            .suggestion(suggestion)</span>
<span class="nc" id="L281">            .metadata(metadata)</span>
<span class="nc" id="L282">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>