<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TypeSpecifierErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">TypeSpecifierErrorAnalyzer.java</span></div><h1>TypeSpecifierErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for type specifier errors in CAPL programs.
 * Handles typeSpecifier rule (CAPL.g4 line 1722) including:
 * - buildInTypeSpecifier: char, int, float, double, byte, word, etc.
 * - Void type
 * - Timer and MsTimer types
 * - A429Settings and FrConfiguration types
 * - caplObjectType: message, signal, etc.
 * - typedefName: user-defined types
 */
public class TypeSpecifierErrorAnalyzer extends ParserErrorAnalyzer {

    // Built-in type keywords
<span class="nc" id="L29">    private static final Set&lt;String&gt; BUILTIN_TYPE_KEYWORDS = Set.of(</span>
        &quot;char&quot;, &quot;short&quot;, &quot;int&quot;, &quot;long&quot;, &quot;float&quot;, &quot;double&quot;, &quot;signed&quot;, &quot;unsigned&quot;,
        &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;, &quot;int64&quot;
    );
    
    // Special type keywords
<span class="nc" id="L35">    private static final Set&lt;String&gt; SPECIAL_TYPE_KEYWORDS = Set.of(</span>
        &quot;void&quot;, &quot;timer&quot;, &quot;msTimer&quot;
    );
    
    // Configuration type keywords
<span class="nc" id="L40">    private static final Set&lt;String&gt; CONFIG_TYPE_KEYWORDS = Set.of(</span>
        &quot;A429Settings&quot;, &quot;FrConfiguration&quot;
    );
    
    // CAPL object type keywords
<span class="nc" id="L45">    private static final Set&lt;String&gt; CAPL_OBJECT_TYPE_KEYWORDS = Set.of(</span>
        &quot;message&quot;, &quot;signal&quot;, &quot;frFrame&quot;, &quot;frPDU&quot;, &quot;diagRequest&quot;, &quot;diagResponse&quot;,
        &quot;ethernetPacket&quot;, &quot;ethernetFrame&quot;, &quot;mostMessage&quot;, &quot;mostAmsMessage&quot;,
        &quot;mostRawMessage&quot;, &quot;j1587Message&quot;, &quot;a429Word&quot;, &quot;serviceSignal&quot;
    );

    public TypeSpecifierErrorAnalyzer() {
<span class="nc" id="L52">        super(&quot;TypeSpecifierErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 220,</span>
<span class="nc" id="L53">              Set.of(CaplParseContext.TYPE_SPECIFIER));</span>
<span class="nc" id="L54">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L58">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L60">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L63" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">               isTypeSpecifierError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L69">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L74">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L80">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L81">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L82">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L85">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific type specifier analyzers
<span class="nc bnc" id="L88" title="All 2 branches missed.">            if (isBuiltInTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L89">                return analyzeBuiltInTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">            } else if (isSpecialTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L91">                return analyzeSpecialTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">            } else if (isConfigTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L93">                return analyzeConfigTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">            } else if (isCaplObjectTypeError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L95">                return analyzeCaplObjectTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">            } else if (isTypedefNameError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L97">                return analyzeTypedefNameError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L100">            return createGenericTypeSpecifierError(context);</span>

<span class="nc" id="L102">        } catch (Exception e) {</span>
<span class="nc" id="L103">            return createGenericTypeSpecifierError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a type specifier error.
     */
    private boolean isTypeSpecifierError(ErrorContext context) {
<span class="nc" id="L113">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L114">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L115">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L118" title="All 2 branches missed.">        return hasBuiltInTypeKeyword(precedingTokens) || </span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">               hasSpecialTypeKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">               hasConfigTypeKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">               hasCaplObjectTypeKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">               hasTypedefPattern(precedingTokens);</span>
    }

    /**
     * Analyzes built-in type errors.
     */
    private ErrorResult analyzeBuiltInTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L130" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L131">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_BUILTIN_TYPE_ERROR,
                &quot;analyzer.typeSpecifier.builtIn.missingDeclarator&quot;,
                &quot;analyzer.typeSpecifier.builtIn.missingDeclaratorSuggestion&quot;,
                context
            );
        }

        // Check for invalid reference operator
<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (isInvalidReferenceOperator(tokens, offendingText)) {</span>
<span class="nc" id="L141">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_BUILTIN_TYPE_ERROR,
                &quot;analyzer.typeSpecifier.builtIn.invalidReference&quot;,
                &quot;analyzer.typeSpecifier.builtIn.invalidReferenceSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L149">        return createGenericTypeSpecifierError(context);</span>
    }

    /**
     * Analyzes special type errors (void, timer, msTimer).
     */
    private ErrorResult analyzeSpecialTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L157" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L158">            String typeKeyword = getTypeKeyword(tokens);</span>
<span class="nc" id="L159">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_SPECIAL_TYPE_SPECIFIER_ERROR,
                &quot;analyzer.typeSpecifier.special.missingDeclarator&quot;,
                &quot;analyzer.typeSpecifier.special.missingDeclaratorSuggestion&quot;,
                context, typeKeyword
            );
        }

<span class="nc" id="L167">        return createGenericTypeSpecifierError(context);</span>
    }

    /**
     * Analyzes configuration type errors.
     */
    private ErrorResult analyzeConfigTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L175" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L176">            String configType = getConfigTypeKeyword(tokens);</span>
<span class="nc" id="L177">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_CONFIG_TYPE_SPECIFIER_ERROR,
                &quot;analyzer.typeSpecifier.config.missingDeclarator&quot;,
                &quot;analyzer.typeSpecifier.config.missingDeclaratorSuggestion&quot;,
                context, configType
            );
        }

<span class="nc" id="L185">        return createGenericTypeSpecifierError(context);</span>
    }

    /**
     * Analyzes CAPL object type errors.
     */
    private ErrorResult analyzeCaplObjectTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing type parameter
<span class="nc bnc" id="L193" title="All 2 branches missed.">        if (isMissingTypeParameter(tokens, offendingText)) {</span>
<span class="nc" id="L194">            String objectType = getCaplObjectTypeKeyword(tokens);</span>
<span class="nc" id="L195">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_CAPL_OBJECT_TYPE_SPECIFIER_ERROR,
                &quot;analyzer.typeSpecifier.caplObject.missingParameter&quot;,
                &quot;analyzer.typeSpecifier.caplObject.missingParameterSuggestion&quot;,
                context, objectType
            );
        }

        // Check for missing declarator
<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L205">            String objectType = getCaplObjectTypeKeyword(tokens);</span>
<span class="nc" id="L206">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_CAPL_OBJECT_TYPE_SPECIFIER_ERROR,
                &quot;analyzer.typeSpecifier.caplObject.missingDeclarator&quot;,
                &quot;analyzer.typeSpecifier.caplObject.missingDeclaratorSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L214">        return createGenericTypeSpecifierError(context);</span>
    }

    /**
     * Analyzes typedef name errors.
     */
    private ErrorResult analyzeTypedefNameError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing declarator
<span class="nc bnc" id="L222" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L223">            return createTypeSpecifierError(</span>
                CaplErrorType.PARSER_TYPEDEF_NAME_SPECIFIER_ERROR,
                &quot;analyzer.typeSpecifier.typedef.missingDeclarator&quot;,
                &quot;analyzer.typeSpecifier.typedef.missingDeclaratorSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L231">        return createGenericTypeSpecifierError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isBuiltInTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L237">        return hasBuiltInTypeKeyword(tokens);</span>
    }

    private boolean isSpecialTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L241">        return hasSpecialTypeKeyword(tokens);</span>
    }

    private boolean isConfigTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L245">        return hasConfigTypeKeyword(tokens);</span>
    }

    private boolean isCaplObjectTypeError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L249">        return hasCaplObjectTypeKeyword(tokens);</span>
    }

    private boolean isTypedefNameError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L253">        return hasTypedefPattern(tokens);</span>
    }

    private boolean isMissingDeclarator(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L257" title="All 2 branches missed.">        return hasTypeKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L258" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;,&quot;));</span>
    }

    private boolean isMissingTypeParameter(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L262" title="All 2 branches missed.">        return hasCaplObjectTypeKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L263" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">               !hasTypeParameter(tokens);</span>
    }

    private boolean isInvalidReferenceOperator(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L268" title="All 2 branches missed.">        return hasBuiltInTypeKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L269" title="All 2 branches missed.">               offendingText.equals(&quot;&amp;&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">               !isValidReferenceContext(tokens);</span>
    }

    // ========== Utility Methods ==========

    private boolean hasBuiltInTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L276">        return hasAnyKeyword(tokens, BUILTIN_TYPE_KEYWORDS);</span>
    }

    private boolean hasSpecialTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L280">        return hasAnyKeyword(tokens, SPECIAL_TYPE_KEYWORDS);</span>
    }

    private boolean hasConfigTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L284">        return hasAnyKeyword(tokens, CONFIG_TYPE_KEYWORDS);</span>
    }

    private boolean hasCaplObjectTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L288">        return hasAnyKeyword(tokens, CAPL_OBJECT_TYPE_KEYWORDS);</span>
    }

    private boolean hasTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L292" title="All 4 branches missed.">        return hasBuiltInTypeKeyword(tokens) || hasSpecialTypeKeyword(tokens) ||</span>
<span class="nc bnc" id="L293" title="All 4 branches missed.">               hasConfigTypeKeyword(tokens) || hasCaplObjectTypeKeyword(tokens);</span>
    }

    private boolean hasTypedefPattern(List&lt;Token&gt; tokens) {
        // Simple heuristic: identifier that's not a known keyword
<span class="nc bnc" id="L298" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L299">            return false;</span>
        }

<span class="nc bnc" id="L302" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L303">            String text = token.getText();</span>
<span class="nc bnc" id="L304" title="All 2 branches missed.">            if (text.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L305" title="All 2 branches missed.">                !BUILTIN_TYPE_KEYWORDS.contains(text) &amp;&amp;</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">                !SPECIAL_TYPE_KEYWORDS.contains(text) &amp;&amp;</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">                !CONFIG_TYPE_KEYWORDS.contains(text) &amp;&amp;</span>
<span class="nc bnc" id="L308" title="All 2 branches missed.">                !CAPL_OBJECT_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L309">                return true;</span>
            }
<span class="nc" id="L311">        }</span>
<span class="nc" id="L312">        return false;</span>
    }

    private boolean hasTypeParameter(List&lt;Token&gt; tokens) {
        // Simple check for type parameters (numbers, identifiers, etc.)
<span class="nc bnc" id="L317" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L318">            return false;</span>
        }

<span class="nc bnc" id="L321" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L322">            String text = token.getText();</span>
<span class="nc bnc" id="L323" title="All 4 branches missed.">            if (text.matches(&quot;\\d+&quot;) || text.matches(&quot;0[xX][0-9a-fA-F]+&quot;) ||</span>
<span class="nc bnc" id="L324" title="All 2 branches missed.">                text.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;)) {</span>
<span class="nc" id="L325">                return true;</span>
            }
<span class="nc" id="L327">        }</span>
<span class="nc" id="L328">        return false;</span>
    }

    private boolean isValidReferenceContext(List&lt;Token&gt; tokens) {
        // Reference operator is valid in function parameters
<span class="nc bnc" id="L333" title="All 4 branches missed.">        return tokens != null &amp;&amp; tokens.size() &gt; 1;</span>
    }

    private String getTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L337" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L338">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L341" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L342">            String text = token.getText();</span>
<span class="nc bnc" id="L343" title="All 2 branches missed.">            if (SPECIAL_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L344">                return text;</span>
            }
<span class="nc" id="L346">        }</span>
<span class="nc" id="L347">        return &quot;unknown&quot;;</span>
    }

    private String getConfigTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L351" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L352">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L355" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L356">            String text = token.getText();</span>
<span class="nc bnc" id="L357" title="All 2 branches missed.">            if (CONFIG_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L358">                return text;</span>
            }
<span class="nc" id="L360">        }</span>
<span class="nc" id="L361">        return &quot;unknown&quot;;</span>
    }

    private String getCaplObjectTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L365" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L366">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L369" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L370">            String text = token.getText();</span>
<span class="nc bnc" id="L371" title="All 2 branches missed.">            if (CAPL_OBJECT_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L372">                return text;</span>
            }
<span class="nc" id="L374">        }</span>
<span class="nc" id="L375">        return &quot;unknown&quot;;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L379" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L380">            return false;</span>
        }

<span class="nc bnc" id="L383" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L384" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L385">                return true;</span>
            }
<span class="nc" id="L387">        }</span>
<span class="nc" id="L388">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L392" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L393">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L395" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L396">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createTypeSpecifierError(CaplErrorType errorType, String messageKey,
                                               String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L401">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L402">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L404">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L405">        metadata.put(&quot;errorType&quot;, &quot;typeSpecifier&quot;);</span>
<span class="nc" id="L406">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L407">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L409">        return ErrorResult.builder()</span>
<span class="nc" id="L410">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L411">            .enhancedMessage(message)</span>
<span class="nc" id="L412">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L413">            .suggestion(suggestion)</span>
<span class="nc" id="L414">            .metadata(metadata)</span>
<span class="nc" id="L415">            .build();</span>
    }

    private ErrorResult createGenericTypeSpecifierError(ErrorContext context) {
<span class="nc" id="L419">        String message = I18n.l(&quot;analyzer.typeSpecifier.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L420">        String suggestion = I18n.l(&quot;analyzer.typeSpecifier.genericSuggestion&quot;);</span>

<span class="nc" id="L422">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L423">        metadata.put(&quot;errorType&quot;, &quot;typeSpecifier_generic&quot;);</span>
<span class="nc" id="L424">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L426">        return ErrorResult.builder()</span>
<span class="nc" id="L427">            .ruleId(CaplErrorType.PARSER_TYPE_SPECIFIER_ERROR.getErrorCode())</span>
<span class="nc" id="L428">            .enhancedMessage(message)</span>
<span class="nc" id="L429">            .severity(CaplErrorType.PARSER_TYPE_SPECIFIER_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L430">            .suggestion(suggestion)</span>
<span class="nc" id="L431">            .metadata(metadata)</span>
<span class="nc" id="L432">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>