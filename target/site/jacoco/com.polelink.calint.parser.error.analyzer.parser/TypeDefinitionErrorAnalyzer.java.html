<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TypeDefinitionErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">TypeDefinitionErrorAnalyzer.java</span></div><h1>TypeDefinitionErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Analyzer for type definition errors in CAPL programs.
 * Handles struct, enum, and union type definitions.
 */
public class TypeDefinitionErrorAnalyzer extends ParserErrorAnalyzer {

    // Type definition keywords
<span class="nc" id="L24">    private static final Set&lt;String&gt; TYPE_DEFINITION_KEYWORDS = Set.of(</span>
        &quot;struct&quot;, &quot;enum&quot;, &quot;union&quot;
    );

    // Pattern for identifier validation
<span class="nc" id="L29">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public TypeDefinitionErrorAnalyzer() {
<span class="nc" id="L32">        super(&quot;TypeDefinitionErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 170,</span>
<span class="nc" id="L33">              Set.of(CaplParseContext.TYPE_DEFINITION,</span>
                     CaplParseContext.STRUCT_SPECIFIER,
                     CaplParseContext.ENUM_SPECIFIER));
<span class="nc" id="L36">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L40">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L42">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L45" title="All 6 branches missed.">        return parseContext == CaplParseContext.TYPE_DEFINITION ||</span>
               parseContext == CaplParseContext.STRUCT_SPECIFIER ||
               parseContext == CaplParseContext.ENUM_SPECIFIER ||
<span class="nc bnc" id="L48" title="All 2 branches missed.">               isTypeDefinitionError(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L54">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L55">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L56">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L59">            String offendingText = getOffendingText(context);</span>

            // Determine the type definition kind
<span class="nc" id="L62">            String typeKind = determineTypeKind(precedingTokens);</span>

            // Check for missing type definition keyword
<span class="nc bnc" id="L65" title="All 2 branches missed.">            if (isMissingTypeKeyword(precedingTokens, offendingText)) {</span>
<span class="nc" id="L66">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.missingTypeKeyword&quot;,
                    &quot;analyzer.typeDefinition.suggestion.missingTypeKeyword&quot;,
                    context
                );
            }

            // Check for missing type name
<span class="nc bnc" id="L75" title="All 2 branches missed.">            if (isMissingTypeName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L76">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.missingTypeName&quot;,
                    &quot;analyzer.typeDefinition.suggestion.missingTypeName&quot;,
                    context,
                    typeKind
                );
            }

            // Check for invalid type name
<span class="nc bnc" id="L86" title="All 2 branches missed.">            if (isInvalidTypeName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L87">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.invalidTypeName&quot;,
                    &quot;analyzer.typeDefinition.suggestion.invalidTypeName&quot;,
                    context,
                    offendingText
                );
            }

            // Check for missing opening brace
<span class="nc bnc" id="L97" title="All 2 branches missed.">            if (isMissingOpeningBrace(precedingTokens, offendingText)) {</span>
<span class="nc" id="L98">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.missingOpeningBrace&quot;,
                    &quot;analyzer.typeDefinition.suggestion.missingOpeningBrace&quot;,
                    context,
                    typeKind
                );
            }

            // Check for missing closing brace
<span class="nc bnc" id="L108" title="All 2 branches missed.">            if (isMissingClosingBrace(precedingTokens, offendingText)) {</span>
<span class="nc" id="L109">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.missingClosingBrace&quot;,
                    &quot;analyzer.typeDefinition.suggestion.missingClosingBrace&quot;,
                    context,
                    typeKind
                );
            }

            // Check for missing semicolon
<span class="nc bnc" id="L119" title="All 2 branches missed.">            if (isMissingSemicolon(precedingTokens, offendingText)) {</span>
<span class="nc" id="L120">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.missingSemicolon&quot;,
                    &quot;analyzer.typeDefinition.suggestion.missingSemicolon&quot;,
                    context
                );
            }

            // Check for invalid member declaration (struct/union specific)
<span class="nc bnc" id="L129" title="All 2 branches missed.">            if (isInvalidMemberDeclaration(precedingTokens, offendingText, typeKind)) {</span>
<span class="nc" id="L130">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.invalidMemberDeclaration&quot;,
                    &quot;analyzer.typeDefinition.suggestion.invalidMemberDeclaration&quot;,
                    context,
                    typeKind
                );
            }

            // Check for invalid enum value (enum specific)
<span class="nc bnc" id="L140" title="All 2 branches missed.">            if (isInvalidEnumValue(precedingTokens, offendingText, typeKind)) {</span>
<span class="nc" id="L141">                return createTypeDefinitionError(</span>
                    CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                    &quot;analyzer.typeDefinition.message.invalidEnumValue&quot;,
                    &quot;analyzer.typeDefinition.suggestion.invalidEnumValue&quot;,
                    context,
                    offendingText
                );
            }

<span class="nc" id="L150">            return createGenericTypeDefinitionError(context, typeKind);</span>

<span class="nc" id="L152">        } catch (Exception e) {</span>
<span class="nc" id="L153">            return createGenericTypeDefinitionError(context, &quot;type&quot;);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a type definition error.
     */
    private boolean isTypeDefinitionError(ErrorContext context) {
<span class="nc" id="L163">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L164">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L165">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L168">        return hasTypeDefinitionKeyword(precedingTokens);</span>
    }

    /**
     * Determines the type definition kind (struct, enum, union).
     */
    private String determineTypeKind(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L175" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L176">            return &quot;type&quot;;</span>
        }

<span class="nc bnc" id="L179" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L180">            String text = token.getText();</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">            if (TYPE_DEFINITION_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L182">                return text;</span>
            }
<span class="nc" id="L184">        }</span>
<span class="nc" id="L185">        return &quot;type&quot;;</span>
    }

    /**
     * Checks if type definition keyword is missing.
     */
    private boolean isMissingTypeKeyword(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L192" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L193">            return false;</span>
        }

        // Look for identifier that could be a type name without keyword
<span class="nc bnc" id="L197" title="All 2 branches missed.">        return IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">               !hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">               !offendingText.equals(&quot;const&quot;);</span>
    }

    /**
     * Checks if type name is missing.
     */
    private boolean isMissingTypeName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L206" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L207">            return false;</span>
        }

        // Look for type keyword followed by opening brace without name
<span class="nc bnc" id="L211" title="All 4 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp; offendingText.equals(&quot;{&quot;);</span>
    }

    /**
     * Checks if type name is invalid.
     */
    private boolean isInvalidTypeName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L218" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L219">            return false;</span>
        }

        // Check if it follows a type keyword but isn't a valid identifier
<span class="nc bnc" id="L223" title="All 2 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L225" title="All 2 branches missed.">               !offendingText.equals(&quot;{&quot;);</span>
    }

    /**
     * Checks if opening brace is missing.
     */
    private boolean isMissingOpeningBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L232" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L233">            return false;</span>
        }

        // Look for type keyword + name without opening brace
<span class="nc bnc" id="L237" title="All 2 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">               hasIdentifierAfterTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L239" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if closing brace is missing.
     */
    private boolean isMissingClosingBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L246" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L247">            return false;</span>
        }

        // Look for type definition with opening brace but no closing brace
<span class="nc bnc" id="L251" title="All 2 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L253" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;;&quot;));</span>
    }

    /**
     * Checks if semicolon is missing.
     */
    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L260" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L261">            return false;</span>
        }

        // Look for complete type definition without semicolon
<span class="nc bnc" id="L265" title="All 2 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L266" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L268" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if member declaration is invalid (for struct/union).
     */
    private boolean isInvalidMemberDeclaration(List&lt;Token&gt; tokens, String offendingText, String typeKind) {
<span class="nc bnc" id="L275" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L276">            return false;</span>
        }

        // Only apply to struct and union
<span class="nc bnc" id="L280" title="All 4 branches missed.">        if (!typeKind.equals(&quot;struct&quot;) &amp;&amp; !typeKind.equals(&quot;union&quot;)) {</span>
<span class="nc" id="L281">            return false;</span>
        }

        // Look for invalid member syntax inside braces
<span class="nc bnc" id="L285" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">               !hasTokenPattern(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L287" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;);</span>
    }

    /**
     * Checks if enum value is invalid (for enum).
     */
    private boolean isInvalidEnumValue(List&lt;Token&gt; tokens, String offendingText, String typeKind) {
<span class="nc bnc" id="L295" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L296">            return false;</span>
        }

        // Only apply to enum
<span class="nc bnc" id="L300" title="All 2 branches missed.">        if (!typeKind.equals(&quot;enum&quot;)) {</span>
<span class="nc" id="L301">            return false;</span>
        }

        // Look for invalid enum value syntax
<span class="nc bnc" id="L305" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">               !hasTokenPattern(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L308" title="All 2 branches missed.">               !offendingText.equals(&quot;,&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L309" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;);</span>
    }

    /**
     * Checks if tokens contain a type definition keyword.
     */
    private boolean hasTypeDefinitionKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L316" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L317">            return false;</span>
        }

<span class="nc bnc" id="L320" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L321" title="All 2 branches missed.">            if (TYPE_DEFINITION_KEYWORDS.contains(token.getText())) {</span>
<span class="nc" id="L322">                return true;</span>
            }
<span class="nc" id="L324">        }</span>
<span class="nc" id="L325">        return false;</span>
    }

    /**
     * Checks if there's an identifier after type keyword.
     */
    private boolean hasIdentifierAfterTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L332" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L333">            return false;</span>
        }

<span class="nc" id="L336">        boolean foundTypeKeyword = false;</span>
<span class="nc bnc" id="L337" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L338" title="All 2 branches missed.">            if (TYPE_DEFINITION_KEYWORDS.contains(token.getText())) {</span>
<span class="nc" id="L339">                foundTypeKeyword = true;</span>
<span class="nc bnc" id="L340" title="All 4 branches missed.">            } else if (foundTypeKeyword &amp;&amp; IDENTIFIER_PATTERN.matcher(token.getText()).matches()) {</span>
<span class="nc" id="L341">                return true;</span>
            }
<span class="nc" id="L343">        }</span>
<span class="nc" id="L344">        return false;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L351" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L352">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L354" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L355">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a type definition error result.
     */
    private ErrorResult createTypeDefinitionError(CaplErrorType errorType, String messageKey,
                                                 String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L363">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L364">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L366">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L367">        metadata.put(&quot;errorType&quot;, &quot;typeDefinition&quot;);</span>
<span class="nc" id="L368">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L369">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L371">        return ErrorResult.builder()</span>
<span class="nc" id="L372">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L373">            .enhancedMessage(message)</span>
<span class="nc" id="L374">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L375">            .suggestion(suggestion)</span>
<span class="nc" id="L376">            .metadata(metadata)</span>
<span class="nc" id="L377">            .build();</span>
    }

    /**
     * Creates a generic type definition error result.
     */
    private ErrorResult createGenericTypeDefinitionError(ErrorContext context, String typeKind) {
<span class="nc" id="L384">        String message = I18n.l(&quot;analyzer.typeDefinition.message.generic&quot;, typeKind, getOffendingText(context));</span>
<span class="nc" id="L385">        String suggestion = I18n.l(&quot;analyzer.typeDefinition.suggestion.generic&quot;, typeKind);</span>

<span class="nc" id="L387">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L388">        metadata.put(&quot;errorType&quot;, &quot;typeDefinition_generic&quot;);</span>
<span class="nc" id="L389">        metadata.put(&quot;typeKind&quot;, typeKind);</span>
<span class="nc" id="L390">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L392">        return ErrorResult.builder()</span>
<span class="nc" id="L393">            .ruleId(CaplErrorType.PARSER_TYPE_DEFINITION_ERROR.getErrorCode())</span>
<span class="nc" id="L394">            .enhancedMessage(message)</span>
<span class="nc" id="L395">            .severity(CaplErrorType.PARSER_TYPE_DEFINITION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L396">            .suggestion(suggestion)</span>
<span class="nc" id="L397">            .metadata(metadata)</span>
<span class="nc" id="L398">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>