<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BlockStructureErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">BlockStructureErrorAnalyzer.java</span></div><h1>BlockStructureErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for block structure errors in CAPL programs.
 * Handles compoundStatement and blockItemList rules (CAPL.g4 lines 248, 255) including:
 * - Compound statement structure validation
 * - Block item list validation
 * - Brace matching
 * - Block content validation
 */
public class BlockStructureErrorAnalyzer extends ParserErrorAnalyzer {

    // Block structure keywords
<span class="nc" id="L27">    private static final Set&lt;String&gt; BLOCK_KEYWORDS = Set.of(</span>
        &quot;{&quot;, &quot;}&quot;
    );
    
    // Statement keywords that can appear in blocks
<span class="nc" id="L32">    private static final Set&lt;String&gt; STATEMENT_KEYWORDS = Set.of(</span>
        &quot;if&quot;, &quot;else&quot;, &quot;switch&quot;, &quot;case&quot;, &quot;default&quot;,
        &quot;for&quot;, &quot;while&quot;, &quot;do&quot;, &quot;foreach&quot;,
        &quot;return&quot;, &quot;break&quot;, &quot;continue&quot;
    );
    
    // Declaration keywords that can appear in blocks
<span class="nc" id="L39">    private static final Set&lt;String&gt; DECLARATION_KEYWORDS = Set.of(</span>
        &quot;int&quot;, &quot;char&quot;, &quot;float&quot;, &quot;double&quot;, &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;,
        &quot;const&quot;, &quot;message&quot;, &quot;signal&quot;, &quot;timer&quot;, &quot;msTimer&quot;
    );

    public BlockStructureErrorAnalyzer() {
<span class="nc" id="L45">        super(&quot;BlockStructureErrorAnalyzer&quot;, AnalyzerType.PARSER_STRUCTURAL, 170,</span>
<span class="nc" id="L46">              Set.of(CaplParseContext.COMPOUND_STATEMENT, CaplParseContext.BLOCK_ITEM_LIST));</span>
<span class="nc" id="L47">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L51">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L53">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L56" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">               isBlockStructureError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L62">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L67">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L73">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L74">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L75">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L78">            String offendingText = getOffendingText(context);</span>

            // Check for missing opening brace
<span class="nc bnc" id="L81" title="All 2 branches missed.">            if (isMissingOpeningBrace(precedingTokens, offendingText)) {</span>
<span class="nc" id="L82">                return createBlockStructureError(</span>
                    CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                    &quot;analyzer.blockStructure.missingOpeningBrace&quot;,
                    &quot;analyzer.blockStructure.missingOpeningBraceSuggestion&quot;,
                    context
                );
            }

            // Check for missing closing brace
<span class="nc bnc" id="L91" title="All 2 branches missed.">            if (isMissingClosingBrace(precedingTokens, offendingText)) {</span>
<span class="nc" id="L92">                return createBlockStructureError(</span>
                    CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                    &quot;analyzer.blockStructure.missingClosingBrace&quot;,
                    &quot;analyzer.blockStructure.missingClosingBraceSuggestion&quot;,
                    context
                );
            }

            // Check for unmatched braces
<span class="nc bnc" id="L101" title="All 2 branches missed.">            if (isUnmatchedBraces(precedingTokens, offendingText)) {</span>
<span class="nc" id="L102">                return createBlockStructureError(</span>
                    CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                    &quot;analyzer.blockStructure.unmatchedBraces&quot;,
                    &quot;analyzer.blockStructure.unmatchedBracesSuggestion&quot;,
                    context
                );
            }

            // Check for empty block content
<span class="nc bnc" id="L111" title="All 2 branches missed.">            if (isEmptyBlockContent(precedingTokens, offendingText)) {</span>
<span class="nc" id="L112">                return createBlockStructureError(</span>
                    CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                    &quot;analyzer.blockStructure.emptyBlock&quot;,
                    &quot;analyzer.blockStructure.emptyBlockSuggestion&quot;,
                    context
                );
            }

            // Check for invalid block content
<span class="nc bnc" id="L121" title="All 2 branches missed.">            if (isInvalidBlockContent(precedingTokens, offendingText)) {</span>
<span class="nc" id="L122">                return createBlockStructureError(</span>
                    CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                    &quot;analyzer.blockStructure.invalidContent&quot;,
                    &quot;analyzer.blockStructure.invalidContentSuggestion&quot;,
                    context
                );
            }

<span class="nc" id="L130">            return createGenericBlockStructureError(context);</span>

<span class="nc" id="L132">        } catch (Exception e) {</span>
<span class="nc" id="L133">            return createGenericBlockStructureError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a block structure error.
     */
    private boolean isBlockStructureError(ErrorContext context) {
<span class="nc" id="L143">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L144">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L145">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L148" title="All 2 branches missed.">        return hasBlockKeyword(precedingTokens) || </span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">               hasStatementKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">               hasDeclarationKeyword(precedingTokens);</span>
    }

    // ========== Validation Methods ==========

    private boolean isMissingOpeningBrace(List&lt;Token&gt; tokens, String offendingText) {
        // Check if we expect a block but don't have opening brace
<span class="nc bnc" id="L157" title="All 4 branches missed.">        return (hasStatementKeyword(tokens) || hasDeclarationKeyword(tokens)) &amp;&amp;</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L159" title="All 4 branches missed.">               (hasStatementContent(tokens) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingClosingBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L163" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">               offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isUnmatchedBraces(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L169" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L170">            return false;</span>
        }

<span class="nc" id="L173">        int braceCount = 0;</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">            if (&quot;{&quot;.equals(token.getText())) {</span>
<span class="nc" id="L176">                braceCount++;</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">            } else if (&quot;}&quot;.equals(token.getText())) {</span>
<span class="nc" id="L178">                braceCount--;</span>
<span class="nc bnc" id="L179" title="All 2 branches missed.">                if (braceCount &lt; 0) {</span>
<span class="nc" id="L180">                    return true; // More closing than opening braces</span>
                }
            }
<span class="nc" id="L183">        }</span>

        // Check if we have unmatched opening braces
<span class="nc bnc" id="L186" title="All 4 branches missed.">        return braceCount &gt; 0 &amp;&amp; offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isEmptyBlockContent(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L190" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">               offendingText.equals(&quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">               !hasStatementContent(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">               !hasDeclarationContent(tokens);</span>
    }

    private boolean isInvalidBlockContent(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L197" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">               !hasStatementKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">               !hasDeclarationKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;);</span>
    }

    // ========== Utility Methods ==========

    private boolean hasBlockKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L207">        return hasAnyKeyword(tokens, BLOCK_KEYWORDS);</span>
    }

    private boolean hasStatementKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L211">        return hasAnyKeyword(tokens, STATEMENT_KEYWORDS);</span>
    }

    private boolean hasDeclarationKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L215">        return hasAnyKeyword(tokens, DECLARATION_KEYWORDS);</span>
    }

    private boolean hasStatementContent(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L219" title="All 4 branches missed.">        return hasStatementKeyword(tokens) || hasExpressionContent(tokens);</span>
    }

    private boolean hasDeclarationContent(List&lt;Token&gt; tokens) {
<span class="nc" id="L223">        return hasDeclarationKeyword(tokens);</span>
    }

    private boolean hasExpressionContent(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L228">            return false;</span>
        }

        // Simple heuristic: look for identifiers, operators, or literals
<span class="nc bnc" id="L232" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L233">            String text = token.getText();</span>
<span class="nc bnc" id="L234" title="All 2 branches missed.">            if (text.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;) || // identifier</span>
<span class="nc bnc" id="L235" title="All 2 branches missed.">                text.matches(&quot;\\d+&quot;) || // number</span>
<span class="nc bnc" id="L236" title="All 2 branches missed.">                text.matches(&quot;\&quot;.*\&quot;&quot;) || // string</span>
<span class="nc bnc" id="L237" title="All 6 branches missed.">                &quot;=&quot;.equals(text) || &quot;+&quot; .equals(text) || &quot;-&quot;.equals(text)) { // operators</span>
<span class="nc" id="L238">                return true;</span>
            }
<span class="nc" id="L240">        }</span>
<span class="nc" id="L241">        return false;</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L245" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L246">            return false;</span>
        }

<span class="nc bnc" id="L249" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L251">                return true;</span>
            }
<span class="nc" id="L253">        }</span>
<span class="nc" id="L254">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L258" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L259">            return false;</span>
        }

<span class="nc bnc" id="L262" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L263" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L264">                return true;</span>
            }
<span class="nc" id="L266">        }</span>
<span class="nc" id="L267">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L271" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L272">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L274" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L275">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createBlockStructureError(CaplErrorType errorType, String messageKey,
                                                 String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L280">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L281">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L283">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L284">        metadata.put(&quot;errorType&quot;, &quot;blockStructure&quot;);</span>
<span class="nc" id="L285">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L286">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L288">        return ErrorResult.builder()</span>
<span class="nc" id="L289">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L290">            .enhancedMessage(message)</span>
<span class="nc" id="L291">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L292">            .suggestion(suggestion)</span>
<span class="nc" id="L293">            .metadata(metadata)</span>
<span class="nc" id="L294">            .build();</span>
    }

    private ErrorResult createGenericBlockStructureError(ErrorContext context) {
<span class="nc" id="L298">        String message = I18n.l(&quot;analyzer.blockStructure.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L299">        String suggestion = I18n.l(&quot;analyzer.blockStructure.genericSuggestion&quot;);</span>

<span class="nc" id="L301">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L302">        metadata.put(&quot;errorType&quot;, &quot;blockStructure_generic&quot;);</span>
<span class="nc" id="L303">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L305">        return ErrorResult.builder()</span>
<span class="nc" id="L306">            .ruleId(CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR.getErrorCode())</span>
<span class="nc" id="L307">            .enhancedMessage(message)</span>
<span class="nc" id="L308">            .severity(CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L309">            .suggestion(suggestion)</span>
<span class="nc" id="L310">            .metadata(metadata)</span>
<span class="nc" id="L311">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>