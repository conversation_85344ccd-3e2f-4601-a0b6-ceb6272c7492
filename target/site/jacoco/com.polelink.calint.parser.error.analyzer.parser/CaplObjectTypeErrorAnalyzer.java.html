<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplObjectTypeErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">CaplObjectTypeErrorAnalyzer.java</span></div><h1>CaplObjectTypeErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Framework analyzer for CAPL object type errors.
 * Acts as a coordinator that dispatches to specific object type analyzers.
 */
public class CaplObjectTypeErrorAnalyzer extends ParserErrorAnalyzer {

    // Message type keywords (messageTypeSpecifier - line 1101)
<span class="nc" id="L24">    private static final Set&lt;String&gt; MESSAGE_TYPES = Set.of(</span>
        &quot;message&quot;, &quot;multiplexedMessage&quot;, &quot;linFrame&quot;
    );

    // FlexRay type keywords (frTypeSpecifier - line 1174)
<span class="nc" id="L29">    private static final Set&lt;String&gt; FLEXRAY_TYPES = Set.of(</span>
        &quot;frFrame&quot;, &quot;frError&quot;, &quot;frPDU&quot;
    );

    // Diagnostic type keywords (diagTypeSpecifier - line 1277)
<span class="nc" id="L34">    private static final Set&lt;String&gt; DIAG_TYPES = Set.of(</span>
        &quot;diagRequest&quot;, &quot;diagResponse&quot;
    );

    // System variable type keywords (sysvarTypeSpecifier - line 1813)
<span class="nc" id="L39">    private static final Set&lt;String&gt; SYSVAR_TYPES = Set.of(</span>
        &quot;sysvar_int&quot;, &quot;sysvar_float&quot;, &quot;sysvar_string&quot;, &quot;sysvar_data&quot;,
        &quot;sysvar_int_array&quot;, &quot;sysvar_float_array&quot;, &quot;sysvar_long&quot;, &quot;sysvar_longlong&quot;
    );

    // Database type keywords (dbtypeSpecifier - line 1836)
<span class="nc" id="L45">    private static final Set&lt;String&gt; DB_TYPES = Set.of(</span>
        &quot;dbMsg&quot;, &quot;dbNode&quot;, &quot;dbPdu&quot;, &quot;dbFrFrame&quot;, &quot;dbFrPdu&quot;
    );

    // SOME/IP type keywords (someipTypeSpecifier - line 1856)
<span class="nc" id="L50">    private static final Set&lt;String&gt; SOMEIP_TYPES = Set.of(</span>
        &quot;serviceSignal&quot;, &quot;serviceSignalData&quot;, &quot;serviceSignalNumber&quot;, &quot;serviceSignalString&quot;
    );

    // Other CAPL object types from caplObjectType rule
<span class="nc" id="L55">    private static final Set&lt;String&gt; OTHER_OBJECT_TYPES = Set.of(</span>
        &quot;a429Word&quot;, &quot;afdxMessage&quot;, &quot;j1587ErrorMessage&quot;, &quot;j1587Message&quot;, &quot;j1587Param&quot;,
        &quot;pg&quot;, &quot;mostMessage&quot;, &quot;mostAmsMessage&quot;, &quot;mostRawMessage&quot;, &quot;pdu&quot;,
        &quot;ethernetFrame&quot;, &quot;ethernetPacket&quot;, &quot;ethernetErrorPacket&quot;,
        &quot;ip_address&quot;, &quot;ip_endpoint&quot;, &quot;signal&quot;
    );

    // CAPL object type keywords based on CAPL.g4 caplObjectType rule (line 1782)
<span class="nc" id="L63">    private static final Map&lt;String, String&gt; OBJECT_TYPE_CATEGORIES = createObjectTypeCategories();</span>

    // Patterns for validation
<span class="nc" id="L66">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L67">    private static final Pattern HEX_NUMBER_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L68">    private static final Pattern DECIMAL_NUMBER_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>
<span class="nc" id="L69">    private static final Pattern QUALIFIED_IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public CaplObjectTypeErrorAnalyzer() {
<span class="nc" id="L72">        super(&quot;CaplObjectTypeErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 165,</span>
<span class="nc" id="L73">              Set.of(CaplParseContext.CAPL_OBJECT_TYPE,</span>
                     CaplParseContext.MESSAGE_TYPE_SPECIFIER,
                     CaplParseContext.SIGNAL_TYPE_SPECIFIER));
<span class="nc" id="L76">    }</span>

    /**
     * Creates the object type categories map based on CAPL.g4 grammar.
     */
    private static Map&lt;String, String&gt; createObjectTypeCategories() {
<span class="nc" id="L82">        Map&lt;String, String&gt; categories = new HashMap&lt;&gt;();</span>

        // Message types (messageTypeSpecifier)
<span class="nc" id="L85">        MESSAGE_TYPES.forEach(type -&gt; categories.put(type, &quot;message&quot;));</span>

        // FlexRay types (frTypeSpecifier)
<span class="nc" id="L88">        FLEXRAY_TYPES.forEach(type -&gt; categories.put(type, &quot;flexray&quot;));</span>

        // Diagnostic types (diagTypeSpecifier)
<span class="nc" id="L91">        DIAG_TYPES.forEach(type -&gt; categories.put(type, &quot;diagnostic&quot;));</span>

        // System variable types (sysvarTypeSpecifier)
<span class="nc" id="L94">        SYSVAR_TYPES.forEach(type -&gt; categories.put(type, &quot;sysvar&quot;));</span>

        // Database types (dbtypeSpecifier)
<span class="nc" id="L97">        DB_TYPES.forEach(type -&gt; categories.put(type, &quot;dbtype&quot;));</span>

        // SOME/IP types (someipTypeSpecifier)
<span class="nc" id="L100">        SOMEIP_TYPES.forEach(type -&gt; categories.put(type, &quot;someip&quot;));</span>

        // Signal type (signalTypeSpecifier)
<span class="nc" id="L103">        categories.put(&quot;signal&quot;, &quot;signal&quot;);</span>

        // PDU type (pduTypeSpecifier)
<span class="nc" id="L106">        categories.put(&quot;pdu&quot;, &quot;pdu&quot;);</span>

        // A429 type (a429TypeSpecifier)
<span class="nc" id="L109">        categories.put(&quot;a429Word&quot;, &quot;a429&quot;);</span>

        // AFDX type (afdxTypeSpecifier)
<span class="nc" id="L112">        categories.put(&quot;afdxMessage&quot;, &quot;afdx&quot;);</span>

        // J1587 types (j1587TypeSpecifier)
<span class="nc" id="L115">        categories.put(&quot;j1587ErrorMessage&quot;, &quot;j1587&quot;);</span>
<span class="nc" id="L116">        categories.put(&quot;j1587Message&quot;, &quot;j1587&quot;);</span>
<span class="nc" id="L117">        categories.put(&quot;j1587Param&quot;, &quot;j1587&quot;);</span>

        // PG type (pgTypeSpecifier)
<span class="nc" id="L120">        categories.put(&quot;pg&quot;, &quot;pg&quot;);</span>

        // MOST types (mostTypeSpecifier)
<span class="nc" id="L123">        categories.put(&quot;mostMessage&quot;, &quot;most&quot;);</span>
<span class="nc" id="L124">        categories.put(&quot;mostAmsMessage&quot;, &quot;most&quot;);</span>
<span class="nc" id="L125">        categories.put(&quot;mostRawMessage&quot;, &quot;most&quot;);</span>

        // Ethernet types (ethernetTypeSpecifier)
<span class="nc" id="L128">        categories.put(&quot;ethernetFrame&quot;, &quot;ethernet&quot;);</span>
<span class="nc" id="L129">        categories.put(&quot;ethernetPacket&quot;, &quot;ethernet&quot;);</span>
<span class="nc" id="L130">        categories.put(&quot;ethernetErrorPacket&quot;, &quot;ethernet&quot;);</span>

        // IP types (ipAddressTypeSpecifier, ipEndpointTypeSpecifier)
<span class="nc" id="L133">        categories.put(&quot;ip_address&quot;, &quot;ip&quot;);</span>
<span class="nc" id="L134">        categories.put(&quot;ip_endpoint&quot;, &quot;ip&quot;);</span>

<span class="nc" id="L136">        return categories;</span>
    }

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L141">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L143">            parseContext = detectParseContext(context);</span>
        }

<span class="nc bnc" id="L146" title="All 2 branches missed.">        return parseContext != null &amp;&amp;</span>
<span class="nc bnc" id="L147" title="All 2 branches missed.">               (getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">                isCaplObjectTypeError(context));</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L153">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L158">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L164">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L165">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L166">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L169">            String offendingText = getOffendingText(context);</span>
<span class="nc" id="L170">            String objectType = determineObjectType(precedingTokens, offendingText);</span>

            // Dispatch to specific analyzers based on object type category
<span class="nc" id="L173">            String category = OBJECT_TYPE_CATEGORIES.getOrDefault(objectType, &quot;unknown&quot;);</span>

<span class="nc bnc" id="L175" title="All 8 branches missed.">            switch (category) {</span>
                case &quot;message&quot;:
<span class="nc" id="L177">                    return analyzeMessageTypeError(context, precedingTokens, offendingText);</span>
                case &quot;signal&quot;:
<span class="nc" id="L179">                    return analyzeSignalTypeError(context, precedingTokens, offendingText);</span>
                case &quot;flexray&quot;:
<span class="nc" id="L181">                    return analyzeFlexRayTypeError(context, precedingTokens, offendingText);</span>
                case &quot;diagnostic&quot;:
<span class="nc" id="L183">                    return analyzeDiagnosticTypeError(context, precedingTokens, offendingText);</span>
                case &quot;pdu&quot;:
<span class="nc" id="L185">                    return analyzePduTypeError(context, precedingTokens, offendingText);</span>
                case &quot;sysvar&quot;:
<span class="nc" id="L187">                    return analyzeSysvarTypeError(context, precedingTokens, offendingText);</span>
                // For T012 framework, delegate other types to generic analyzer
                // T012A will implement specific analyzers for these types
                case &quot;dbtype&quot;:
                case &quot;someip&quot;:
                case &quot;a429&quot;:
                case &quot;afdx&quot;:
                case &quot;j1587&quot;:
                case &quot;pg&quot;:
                case &quot;most&quot;:
                case &quot;ethernet&quot;:
                case &quot;ip&quot;:
<span class="nc" id="L199">                    return analyzeGenericObjectTypeError(context, precedingTokens, offendingText, category);</span>
                default:
<span class="nc" id="L201">                    return analyzeGenericObjectTypeError(context, precedingTokens, offendingText, objectType);</span>
            }

<span class="nc" id="L204">        } catch (Exception e) {</span>
<span class="nc" id="L205">            return createGenericCaplObjectError(context, &quot;unknown&quot;);</span>
        }
    }

    // ========== Specific Type Analyzers ==========

    /**
     * Analyzes message type errors (message, multiplexedMessage, linFrame).
     */
    private ErrorResult analyzeMessageTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing message identifier
<span class="nc bnc" id="L216" title="All 2 branches missed.">        if (isMissingMessageIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L217">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                &quot;analyzer.caplObject.message.missingIdentifier&quot;,
                &quot;analyzer.caplObject.message.missingIdentifierSuggestion&quot;,
                context
            );
        }

        // Check for invalid message ID format
<span class="nc bnc" id="L226" title="All 2 branches missed.">        if (isInvalidMessageId(tokens, offendingText)) {</span>
<span class="nc" id="L227">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                &quot;analyzer.caplObject.message.invalidId&quot;,
                &quot;analyzer.caplObject.message.invalidIdSuggestion&quot;,
                context,
                offendingText
            );
        }

        // Check for invalid qualified identifier
<span class="nc bnc" id="L237" title="All 2 branches missed.">        if (isInvalidQualifiedIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L238">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                &quot;analyzer.caplObject.message.invalidQualifiedId&quot;,
                &quot;analyzer.caplObject.message.invalidQualifiedIdSuggestion&quot;,
                context,
                offendingText
            );
        }

<span class="nc" id="L247">        return createGenericCaplObjectError(context, &quot;message&quot;);</span>
    }

    /**
     * Analyzes signal type errors.
     */
    private ErrorResult analyzeSignalTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing signal identifier
<span class="nc bnc" id="L255" title="All 2 branches missed.">        if (isMissingSignalIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L256">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                &quot;analyzer.caplObject.signal.missingIdentifier&quot;,
                &quot;analyzer.caplObject.signal.missingIdentifierSuggestion&quot;,
                context
            );
        }

        // Check for invalid signal name
<span class="nc bnc" id="L265" title="All 2 branches missed.">        if (isInvalidSignalName(tokens, offendingText)) {</span>
<span class="nc" id="L266">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_SIGNAL_TYPE_ERROR,
                &quot;analyzer.caplObject.signal.invalidName&quot;,
                &quot;analyzer.caplObject.signal.invalidNameSuggestion&quot;,
                context,
                offendingText
            );
        }

<span class="nc" id="L275">        return createGenericCaplObjectError(context, &quot;signal&quot;);</span>
    }

    /**
     * Analyzes FlexRay type errors (frFrame, frError, frPDU).
     */
    private ErrorResult analyzeFlexRayTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing FlexRay parameters
<span class="nc bnc" id="L283" title="All 2 branches missed.">        if (isMissingFlexRayParameters(tokens, offendingText)) {</span>
<span class="nc" id="L284">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_FLEXRAY_TYPE_ERROR,
                &quot;analyzer.caplObject.flexray.missingParameters&quot;,
                &quot;analyzer.caplObject.flexray.missingParametersSuggestion&quot;,
                context
            );
        }

        // Check for invalid FlexRay parameter format
<span class="nc bnc" id="L293" title="All 2 branches missed.">        if (isInvalidFlexRayParameterFormat(tokens, offendingText)) {</span>
<span class="nc" id="L294">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_FLEXRAY_TYPE_ERROR,
                &quot;analyzer.caplObject.flexray.invalidParameterFormat&quot;,
                &quot;analyzer.caplObject.flexray.invalidParameterFormatSuggestion&quot;,
                context,
                offendingText
            );
        }

<span class="nc" id="L303">        return createGenericCaplObjectError(context, &quot;flexray&quot;);</span>
    }

    /**
     * Analyzes diagnostic type errors (diagRequest, diagResponse).
     */
    private ErrorResult analyzeDiagnosticTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing diagnostic parameter
<span class="nc bnc" id="L311" title="All 2 branches missed.">        if (isMissingDiagnosticParameter(tokens, offendingText)) {</span>
<span class="nc" id="L312">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_DIAGNOSTIC_TYPE_ERROR,
                &quot;analyzer.caplObject.diagnostic.missingParameter&quot;,
                &quot;analyzer.caplObject.diagnostic.missingParameterSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L320">        return createGenericCaplObjectError(context, &quot;diagnostic&quot;);</span>
    }

    /**
     * Analyzes PDU type errors.
     */
    private ErrorResult analyzePduTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing PDU size specifier
<span class="nc bnc" id="L328" title="All 2 branches missed.">        if (isMissingPduSizeSpecifier(tokens, offendingText)) {</span>
<span class="nc" id="L329">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_PDU_TYPE_ERROR,
                &quot;analyzer.caplObject.pdu.missingSizeSpecifier&quot;,
                &quot;analyzer.caplObject.pdu.missingSizeSpecifierSuggestion&quot;,
                context
            );
        }

        // Check for invalid PDU size
<span class="nc bnc" id="L338" title="All 2 branches missed.">        if (isInvalidPduSize(tokens, offendingText)) {</span>
<span class="nc" id="L339">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_PDU_TYPE_ERROR,
                &quot;analyzer.caplObject.pdu.invalidSize&quot;,
                &quot;analyzer.caplObject.pdu.invalidSizeSuggestion&quot;,
                context,
                offendingText
            );
        }

<span class="nc" id="L348">        return createGenericCaplObjectError(context, &quot;pdu&quot;);</span>
    }

    /**
     * Analyzes system variable type errors.
     */
    private ErrorResult analyzeSysvarTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing sysvar identifier
<span class="nc bnc" id="L356" title="All 2 branches missed.">        if (isMissingSysvarIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L357">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_SYSVAR_TYPE_ERROR,
                &quot;analyzer.caplObject.sysvar.missingIdentifier&quot;,
                &quot;analyzer.caplObject.sysvar.missingIdentifierSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L365">        return createGenericCaplObjectError(context, &quot;sysvar&quot;);</span>
    }

    /**
     * Analyzes generic object type errors.
     */
    private ErrorResult analyzeGenericObjectTypeError(ErrorContext context, List&lt;Token&gt; tokens, 
                                                     String offendingText, String objectType) {
        // Check for wildcard usage validation
<span class="nc bnc" id="L374" title="All 2 branches missed.">        if (isInvalidWildcardUsage(tokens, offendingText)) {</span>
<span class="nc" id="L375">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_CAPL_OBJECT_TYPE_ERROR,
                &quot;analyzer.caplObject.generic.invalidWildcard&quot;,
                &quot;analyzer.caplObject.generic.invalidWildcardSuggestion&quot;,
                context,
                offendingText
            );
        }

        // Check for invalid object type
<span class="nc bnc" id="L385" title="All 2 branches missed.">        if (isInvalidObjectType(offendingText)) {</span>
<span class="nc" id="L386">            return createCaplObjectError(</span>
                CaplErrorType.PARSER_CAPL_OBJECT_TYPE_ERROR,
                &quot;analyzer.caplObject.generic.invalidObjectType&quot;,
                &quot;analyzer.caplObject.generic.invalidObjectTypeSuggestion&quot;,
                context,
                offendingText,
<span class="nc" id="L392">                String.join(&quot;, &quot;, OBJECT_TYPE_CATEGORIES.keySet())</span>
            );
        }

<span class="nc" id="L396">        return createGenericCaplObjectError(context, objectType);</span>
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a CAPL object type error.
     */
    private boolean isCaplObjectTypeError(ErrorContext context) {
<span class="nc" id="L405">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L406">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L407">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L410">        return hasObjectTypeKeyword(precedingTokens);</span>
    }

    /**
     * Determines the object type from tokens.
     */
    private String determineObjectType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L417" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L418">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L421" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L422">            String text = token.getText();</span>
<span class="nc bnc" id="L423" title="All 4 branches missed.">            if (OBJECT_TYPE_CATEGORIES.containsKey(text) || OTHER_OBJECT_TYPES.contains(text)) {</span>
<span class="nc" id="L424">                return text;</span>
            }
<span class="nc" id="L426">        }</span>

        // Check if offending text is an object type
<span class="nc bnc" id="L429" title="All 4 branches missed.">        if (OBJECT_TYPE_CATEGORIES.containsKey(offendingText) || OTHER_OBJECT_TYPES.contains(offendingText)) {</span>
<span class="nc" id="L430">            return offendingText;</span>
        }

<span class="nc" id="L433">        return &quot;unknown&quot;;</span>
    }

    /**
     * Checks if tokens contain an object type keyword.
     */
    private boolean hasObjectTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L440" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L441">            return false;</span>
        }

<span class="nc bnc" id="L444" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L445">            String text = token.getText();</span>
<span class="nc bnc" id="L446" title="All 4 branches missed.">            if (OBJECT_TYPE_CATEGORIES.containsKey(text) || OTHER_OBJECT_TYPES.contains(text)) {</span>
<span class="nc" id="L447">                return true;</span>
            }
<span class="nc" id="L449">        }</span>
<span class="nc" id="L450">        return false;</span>
    }

    // Validation methods for different object types
    private boolean isMissingMessageIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L455" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;message&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L456" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidMessageId(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L460" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;message&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L461" title="All 2 branches missed.">               !offendingText.equals(&quot;*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L462" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L463" title="All 2 branches missed.">               !HEX_NUMBER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L464" title="All 2 branches missed.">               !DECIMAL_NUMBER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L465" title="All 2 branches missed.">               !QUALIFIED_IDENTIFIER_PATTERN.matcher(offendingText).matches();</span>
    }

    private boolean isInvalidQualifiedIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L469" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;message&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L470" title="All 2 branches missed.">               offendingText.contains(&quot;.&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L471" title="All 2 branches missed.">               !QUALIFIED_IDENTIFIER_PATTERN.matcher(offendingText).matches();</span>
    }

    private boolean isMissingSignalIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L475" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;signal&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L476" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidSignalName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L480" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;signal&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L481" title="All 2 branches missed.">               !offendingText.equals(&quot;*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L482" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches();</span>
    }

    private boolean isMissingFlexRayParameters(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L486" title="All 6 branches missed.">        return (hasTokenPattern(tokens, &quot;frFrame&quot;) || hasTokenPattern(tokens, &quot;frError&quot;) || hasTokenPattern(tokens, &quot;frPDU&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L487" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidFlexRayParameterFormat(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L491" title="All 6 branches missed.">        return (hasTokenPattern(tokens, &quot;frFrame&quot;) || hasTokenPattern(tokens, &quot;frError&quot;) || hasTokenPattern(tokens, &quot;frPDU&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L492" title="All 4 branches missed.">               offendingText.startsWith(&quot;(&quot;) &amp;&amp; !offendingText.matches(&quot;\\(\\d+,\\d+,\\d+\\)&quot;);</span>
    }

    private boolean isMissingDiagnosticParameter(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L496" title="All 4 branches missed.">        return (hasTokenPattern(tokens, &quot;diagRequest&quot;) || hasTokenPattern(tokens, &quot;diagResponse&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L497" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingPduSizeSpecifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L501" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;pdu&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L502" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidPduSize(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L506" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;pdu&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L507" title="All 4 branches missed.">               !offendingText.equals(&quot;short&quot;) &amp;&amp; !offendingText.equals(&quot;long&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L508" title="All 2 branches missed.">               !HEX_NUMBER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L509" title="All 2 branches missed.">               !DECIMAL_NUMBER_PATTERN.matcher(offendingText).matches();</span>
    }

    private boolean isMissingSysvarIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L513" title="All 6 branches missed.">        return (hasTokenPattern(tokens, &quot;sysvar_int&quot;) || hasTokenPattern(tokens, &quot;sysvar_float&quot;) || hasTokenPattern(tokens, &quot;sysvar_string&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L514" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidWildcardUsage(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L518" title="All 4 branches missed.">        return offendingText.equals(&quot;*&quot;) &amp;&amp; !hasObjectTypeKeyword(tokens);</span>
    }

    private boolean isInvalidObjectType(String text) {
<span class="nc bnc" id="L522" title="All 2 branches missed.">        return text.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L523" title="All 2 branches missed.">               !OBJECT_TYPE_CATEGORIES.containsKey(text) &amp;&amp; </span>
<span class="nc bnc" id="L524" title="All 2 branches missed.">               !OTHER_OBJECT_TYPES.contains(text);</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L531" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L532">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L534" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L535">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a CAPL object error result.
     */
    private ErrorResult createCaplObjectError(CaplErrorType errorType, String messageKey,
                                            String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L543">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L544">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L546">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L547">        metadata.put(&quot;errorType&quot;, &quot;caplObjectType&quot;);</span>
<span class="nc" id="L548">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L549">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L551">        return ErrorResult.builder()</span>
<span class="nc" id="L552">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L553">            .enhancedMessage(message)</span>
<span class="nc" id="L554">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L555">            .suggestion(suggestion)</span>
<span class="nc" id="L556">            .metadata(metadata)</span>
<span class="nc" id="L557">            .build();</span>
    }

    /**
     * Creates a generic CAPL object error result.
     */
    private ErrorResult createGenericCaplObjectError(ErrorContext context, String objectType) {
<span class="nc" id="L564">        String message = I18n.l(&quot;analyzer.caplObject.generic.message&quot;, objectType, getOffendingText(context));</span>
<span class="nc" id="L565">        String suggestion = I18n.l(&quot;analyzer.caplObject.generic.suggestion&quot;, objectType);</span>

<span class="nc" id="L567">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L568">        metadata.put(&quot;errorType&quot;, &quot;caplObjectType_generic&quot;);</span>
<span class="nc" id="L569">        metadata.put(&quot;objectType&quot;, objectType);</span>
<span class="nc" id="L570">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L572">        return ErrorResult.builder()</span>
<span class="nc" id="L573">            .ruleId(CaplErrorType.PARSER_CAPL_OBJECT_TYPE_ERROR.getErrorCode())</span>
<span class="nc" id="L574">            .enhancedMessage(message)</span>
<span class="nc" id="L575">            .severity(CaplErrorType.PARSER_CAPL_OBJECT_TYPE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L576">            .suggestion(suggestion)</span>
<span class="nc" id="L577">            .metadata(metadata)</span>
<span class="nc" id="L578">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>