<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ParserErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">ParserErrorAnalyzer.java</span></div><h1>ParserErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AbstractErrorAnalyzer;
import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.IntervalSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Abstract base class for parser error analyzers.
 *
 * This class provides common functionality for analyzing syntax errors
 * that occur during the parsing phase of CAPL language processing.
 */
public abstract class ParserErrorAnalyzer extends AbstractErrorAnalyzer {

    /**
     * Constructor for ParserErrorAnalyzer.
     *
     * @param analyzerName the name of this analyzer
     * @param analyzerType the specific parser analyzer type
     * @param priority the priority of this analyzer
     * @param supportedContexts the set of contexts this analyzer supports
     */
    protected ParserErrorAnalyzer(String analyzerName, AnalyzerType analyzerType,
                                int priority, Set&lt;CaplParseContext&gt; supportedContexts) {
<span class="nc" id="L33">        super(analyzerName, analyzerType, priority, supportedContexts);</span>
<span class="nc" id="L34">    }</span>

    /**
     * Constructor with default priority.
     *
     * @param analyzerName the name of this analyzer
     * @param analyzerType the specific parser analyzer type
     * @param supportedContexts the set of contexts this analyzer supports
     */
    protected ParserErrorAnalyzer(String analyzerName, AnalyzerType analyzerType,
                                Set&lt;CaplParseContext&gt; supportedContexts) {
<span class="nc" id="L45">        super(analyzerName, analyzerType, supportedContexts);</span>
<span class="nc" id="L46">    }</span>

    // ========== Parser Error Detection Methods ==========

    /**
     * Determines if the given error context represents a parser error.
     *
     * @param context the error context to check
     * @return true if this is a parser error, false otherwise
     */
    protected boolean isParserError(ErrorContext context) {
<span class="nc bnc" id="L57" title="All 2 branches missed.">        if (context.getRecognizer() == null) {</span>
<span class="nc" id="L58">            return false;</span>
        }

        // Check if the recognizer is a Parser (not a Lexer)
<span class="nc" id="L62">        return context.getRecognizer() instanceof Parser;</span>
    }

    /**
     * Analyzes a parser error based on the recognition exception and parse context.
     *
     * @param exception the recognition exception that occurred
     * @param parseContext the current parse context
     * @return the error analysis result
     */
    protected ErrorResult analyzeParserError(RecognitionException exception, CaplParseContext parseContext) {
<span class="nc bnc" id="L73" title="All 2 branches missed.">        if (exception instanceof NoViableAltException) {</span>
<span class="nc" id="L74">            return analyzeNoViableAltException((NoViableAltException) exception, parseContext);</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">        } else if (exception instanceof InputMismatchException) {</span>
<span class="nc" id="L76">            return analyzeInputMismatchException((InputMismatchException) exception, parseContext);</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">        } else if (exception instanceof FailedPredicateException) {</span>
<span class="nc" id="L78">            return analyzeFailedPredicateException((FailedPredicateException) exception, parseContext);</span>
        } else {
<span class="nc" id="L80">            return analyzeGenericRecognitionException(exception, parseContext);</span>
        }
    }

    /**
     * Detects the current parse context from the error context.
     *
     * @param context the error context
     * @return the detected parse context
     */
    protected CaplParseContext detectParseContext(ErrorContext context) {
        // Use ContextDetector if available, otherwise return UNKNOWN
        // This method can be overridden by subclasses for more specific detection
<span class="nc" id="L93">        return CaplParseContext.UNKNOWN;</span>
    }

    // ========== Specific Exception Analysis Methods ==========

    /**
     * Analyzes a NoViableAltException.
     *
     * @param exception the NoViableAltException
     * @param parseContext the current parse context
     * @return the error analysis result
     */
    protected ErrorResult analyzeNoViableAltException(NoViableAltException exception, CaplParseContext parseContext) {
        // Default implementation - subclasses should override for specific analysis
<span class="nc" id="L107">        return createGenericParserError(exception, parseContext, &quot;No viable alternative found&quot;);</span>
    }

    /**
     * Analyzes an InputMismatchException.
     *
     * @param exception the InputMismatchException
     * @param parseContext the current parse context
     * @return the error analysis result
     */
    protected ErrorResult analyzeInputMismatchException(InputMismatchException exception, CaplParseContext parseContext) {
        // Default implementation - subclasses should override for specific analysis
<span class="nc" id="L119">        String expectedTokens = extractExpectedTokens(exception, exception.getRecognizer());</span>
<span class="nc" id="L120">        return createGenericParserError(exception, parseContext,</span>
            &quot;Input mismatch. Expected: &quot; + expectedTokens);
    }

    /**
     * Analyzes a FailedPredicateException.
     *
     * @param exception the FailedPredicateException
     * @param parseContext the current parse context
     * @return the error analysis result
     */
    protected ErrorResult analyzeFailedPredicateException(FailedPredicateException exception, CaplParseContext parseContext) {
        // Default implementation - subclasses should override for specific analysis
<span class="nc" id="L133">        return createGenericParserError(exception, parseContext,</span>
<span class="nc" id="L134">            &quot;Failed predicate: &quot; + exception.getPredicate());</span>
    }

    /**
     * Analyzes a generic RecognitionException.
     *
     * @param exception the RecognitionException
     * @param parseContext the current parse context
     * @return the error analysis result
     */
    protected ErrorResult analyzeGenericRecognitionException(RecognitionException exception, CaplParseContext parseContext) {
        // Default implementation - subclasses should override for specific analysis
<span class="nc" id="L146">        return createGenericParserError(exception, parseContext, &quot;Syntax error&quot;);</span>
    }

    // ========== Parser Error Utility Methods ==========

    /**
     * Extracts preceding tokens from the parser for context analysis.
     *
     * @param parser the parser instance
     * @param offendingToken the token where the error occurred
     * @return list of preceding tokens (up to 10 tokens before the error)
     */
    protected List&lt;Token&gt; extractPrecedingTokens(Parser parser, Token offendingToken) {
<span class="nc" id="L159">        List&lt;Token&gt; precedingTokens = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L161" title="All 4 branches missed.">        if (parser == null || offendingToken == null) {</span>
<span class="nc" id="L162">            return precedingTokens;</span>
        }

<span class="nc" id="L165">        TokenStream tokenStream = parser.getTokenStream();</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">        if (tokenStream == null) {</span>
<span class="nc" id="L167">            return precedingTokens;</span>
        }

<span class="nc" id="L170">        int offendingIndex = offendingToken.getTokenIndex();</span>
<span class="nc" id="L171">        int startIndex = Math.max(0, offendingIndex - 10); // Get up to 10 preceding tokens</span>

<span class="nc bnc" id="L173" title="All 2 branches missed.">        for (int i = startIndex; i &lt; offendingIndex; i++) {</span>
<span class="nc" id="L174">            Token token = tokenStream.get(i);</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">            if (token.getChannel() == Token.DEFAULT_CHANNEL) { // Skip whitespace/comments</span>
<span class="nc" id="L176">                precedingTokens.add(token);</span>
            }
        }

<span class="nc" id="L180">        return precedingTokens;</span>
    }

    /**
     * Extracts expected tokens from an InputMismatchException.
     *
     * @param exception the InputMismatchException
     * @param recognizer the recognizer instance
     * @return string representation of expected tokens
     */
    protected String extractExpectedTokens(InputMismatchException exception, Recognizer&lt;?, ?&gt; recognizer) {
<span class="nc bnc" id="L191" title="All 6 branches missed.">        if (exception == null || exception.getExpectedTokens() == null || recognizer == null) {</span>
<span class="nc" id="L192">            return &quot;unknown&quot;;</span>
        }

<span class="nc" id="L195">        IntervalSet expectedTokens = exception.getExpectedTokens();</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">        if (expectedTokens.isNil()) {</span>
<span class="nc" id="L197">            return &quot;unknown&quot;;</span>
        }

        // Convert token IDs to token names
<span class="nc" id="L201">        List&lt;String&gt; tokenNames = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L202" title="All 2 branches missed.">        for (int tokenType : expectedTokens.toArray()) {</span>
<span class="nc" id="L203">            String tokenName = recognizer.getVocabulary().getDisplayName(tokenType);</span>
<span class="nc" id="L204">            tokenNames.add(tokenName);</span>
        }

<span class="nc" id="L207">        return String.join(&quot;, &quot;, tokenNames);</span>
    }

    /**
     * Checks if the given token list matches a specific pattern.
     *
     * @param tokens the list of tokens to check
     * @param pattern the pattern to match (token text values)
     * @return true if the pattern matches, false otherwise
     */
    protected boolean hasTokenPattern(List&lt;Token&gt; tokens, String... pattern) {
<span class="nc bnc" id="L218" title="All 6 branches missed.">        if (tokens == null || pattern == null || tokens.size() &lt; pattern.length) {</span>
<span class="nc" id="L219">            return false;</span>
        }

        // Check if the last N tokens match the pattern
<span class="nc" id="L223">        int startIndex = tokens.size() - pattern.length;</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">        for (int i = 0; i &lt; pattern.length; i++) {</span>
<span class="nc" id="L225">            Token token = tokens.get(startIndex + i);</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">            if (!pattern[i].equals(token.getText())) {</span>
<span class="nc" id="L227">                return false;</span>
            }
        }

<span class="nc" id="L231">        return true;</span>
    }

    /**
     * Creates a generic parser error result.
     *
     * @param exception the recognition exception
     * @param parseContext the parse context
     * @param message the error message
     * @return the error result
     */
    protected ErrorResult createGenericParserError(RecognitionException exception, CaplParseContext parseContext, String message) {
        // This is a placeholder implementation
        // Subclasses should override this method to create proper ErrorResult objects
        // For now, return null to avoid compilation errors
<span class="nc" id="L246">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>