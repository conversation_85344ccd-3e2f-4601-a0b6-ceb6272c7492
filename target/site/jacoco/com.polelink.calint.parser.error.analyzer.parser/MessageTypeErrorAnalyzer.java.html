<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MessageTypeErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">MessageTypeErrorAnalyzer.java</span></div><h1>MessageTypeErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for message type declaration errors in CAPL programs.
 * Handles messageTypeSpecifier rule (CAPL.g4 line 1101) including:
 * - message, multiplexedMessage, linFrame types
 * - Various identifier formats: *, 0x100, 123, MyMsg, CAN1.0x200
 */
public class MessageTypeErrorAnalyzer extends ParserErrorAnalyzer {

    // Message type keywords based on CAPL.g4 messageTypeSpecifier rule
<span class="nc" id="L26">    private static final Set&lt;String&gt; MESSAGE_TYPE_KEYWORDS = Set.of(</span>
        &quot;message&quot;, &quot;multiplexedMessage&quot;, &quot;linFrame&quot;
    );

    // Patterns for message identifier validation
<span class="nc" id="L31">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L32">    private static final Pattern HEX_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L33">    private static final Pattern DECIMAL_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>
<span class="nc" id="L34">    private static final Pattern QUALIFIED_IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L35">    private static final Pattern DATABASE_QUALIFIED_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*\\.(0[xX][0-9a-fA-F]+|\\d+)&quot;);</span>

    public MessageTypeErrorAnalyzer() {
<span class="nc" id="L38">        super(&quot;MessageTypeErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 175,</span>
<span class="nc" id="L39">              Set.of(CaplParseContext.MESSAGE_TYPE_SPECIFIER));</span>
<span class="nc" id="L40">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L44">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L46">            parseContext = detectParseContext(context);</span>
        }

<span class="nc bnc" id="L49" title="All 2 branches missed.">        return parseContext == CaplParseContext.MESSAGE_TYPE_SPECIFIER ||</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">               isMessageTypeError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L55">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L60">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L66">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L67">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L68">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L71">            String offendingText = getOffendingText(context);</span>
<span class="nc" id="L72">            String messageType = determineMessageType(precedingTokens);</span>

            // Check for missing message type keyword
<span class="nc bnc" id="L75" title="All 2 branches missed.">            if (isMissingMessageTypeKeyword(precedingTokens, offendingText)) {</span>
<span class="nc" id="L76">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.missingKeyword&quot;,
                    &quot;analyzer.messageType.message.missingKeywordSuggestion&quot;,
                    context
                );
            }

            // Check for missing message identifier
<span class="nc bnc" id="L85" title="All 2 branches missed.">            if (isMissingMessageIdentifier(precedingTokens, offendingText)) {</span>
<span class="nc" id="L86">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.missingIdentifier&quot;,
                    &quot;analyzer.messageType.message.missingIdentifierSuggestion&quot;,
                    context,
                    messageType
                );
            }

            // Check for invalid wildcard usage
<span class="nc bnc" id="L96" title="All 2 branches missed.">            if (isInvalidWildcardUsage(precedingTokens, offendingText)) {</span>
<span class="nc" id="L97">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidWildcard&quot;,
                    &quot;analyzer.messageType.message.invalidWildcardSuggestion&quot;,
                    context
                );
            }

            // Check for invalid hexadecimal ID format
<span class="nc bnc" id="L106" title="All 2 branches missed.">            if (isInvalidHexadecimalId(precedingTokens, offendingText)) {</span>
<span class="nc" id="L107">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidHexId&quot;,
                    &quot;analyzer.messageType.message.invalidHexIdSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for invalid decimal ID format
<span class="nc bnc" id="L117" title="All 2 branches missed.">            if (isInvalidDecimalId(precedingTokens, offendingText)) {</span>
<span class="nc" id="L118">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidDecimalId&quot;,
                    &quot;analyzer.messageType.message.invalidDecimalIdSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for invalid qualified identifier
<span class="nc bnc" id="L128" title="All 2 branches missed.">            if (isInvalidQualifiedIdentifier(precedingTokens, offendingText)) {</span>
<span class="nc" id="L129">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidQualifiedId&quot;,
                    &quot;analyzer.messageType.message.invalidQualifiedIdSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for invalid database qualified format
<span class="nc bnc" id="L139" title="All 2 branches missed.">            if (isInvalidDatabaseQualifiedFormat(precedingTokens, offendingText)) {</span>
<span class="nc" id="L140">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidDatabaseQualified&quot;,
                    &quot;analyzer.messageType.message.invalidDatabaseQualifiedSuggestion&quot;,
                    context,
                    offendingText
                );
            }

            // Check for invalid message name
<span class="nc bnc" id="L150" title="All 2 branches missed.">            if (isInvalidMessageName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L151">                return createMessageTypeError(</span>
                    CaplErrorType.PARSER_MESSAGE_TYPE_ERROR,
                    &quot;analyzer.messageType.message.invalidName&quot;,
                    &quot;analyzer.messageType.message.invalidNameSuggestion&quot;,
                    context,
                    offendingText
                );
            }

<span class="nc" id="L160">            return createGenericMessageTypeError(context, messageType);</span>

<span class="nc" id="L162">        } catch (Exception e) {</span>
<span class="nc" id="L163">            return createGenericMessageTypeError(context, &quot;message&quot;);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a message type error.
     */
    private boolean isMessageTypeError(ErrorContext context) {
<span class="nc" id="L173">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L174">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L175">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L178">        return hasMessageTypeKeyword(precedingTokens);</span>
    }

    /**
     * Determines the message type from tokens.
     */
    private String determineMessageType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L185" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L186">            return &quot;message&quot;;</span>
        }

<span class="nc bnc" id="L189" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L190">            String text = token.getText();</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">            if (MESSAGE_TYPE_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L192">                return text;</span>
            }
<span class="nc" id="L194">        }</span>
<span class="nc" id="L195">        return &quot;message&quot;;</span>
    }

    /**
     * Checks if message type keyword is missing.
     */
    private boolean isMissingMessageTypeKeyword(List&lt;Token&gt; tokens, String offendingText) {
        // Look for identifier that could be a message name without keyword
<span class="nc bnc" id="L203" title="All 2 branches missed.">        return !hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">               (IDENTIFIER_PATTERN.matcher(offendingText).matches() ||</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">                offendingText.equals(&quot;*&quot;) ||</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">                HEX_PATTERN.matcher(offendingText).matches() ||</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">                DECIMAL_PATTERN.matcher(offendingText).matches());</span>
    }

    /**
     * Checks if message identifier is missing.
     */
    private boolean isMissingMessageIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L214" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L215" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if wildcard usage is invalid.
     */
    private boolean isInvalidWildcardUsage(List&lt;Token&gt; tokens, String offendingText) {
        // Wildcard should only be used after message type keyword
<span class="nc bnc" id="L223" title="All 4 branches missed.">        return offendingText.equals(&quot;*&quot;) &amp;&amp; !hasMessageTypeKeyword(tokens);</span>
    }

    /**
     * Checks if hexadecimal ID format is invalid.
     */
    private boolean isInvalidHexadecimalId(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L230" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">               offendingText.startsWith(&quot;0&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">               !HEX_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">               offendingText.length() &gt; 1;</span>
    }

    /**
     * Checks if decimal ID format is invalid.
     */
    private boolean isInvalidDecimalId(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L240" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L241" title="All 2 branches missed.">               offendingText.matches(&quot;\\d.*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L242" title="All 2 branches missed.">               !DECIMAL_PATTERN.matcher(offendingText).matches();</span>
    }

    /**
     * Checks if qualified identifier format is invalid.
     */
    private boolean isInvalidQualifiedIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L249" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">               offendingText.contains(&quot;.&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">               !QUALIFIED_IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">               !DATABASE_QUALIFIED_PATTERN.matcher(offendingText).matches();</span>
    }

    /**
     * Checks if database qualified format is invalid.
     */
    private boolean isInvalidDatabaseQualifiedFormat(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L259" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">               offendingText.contains(&quot;.&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L261" title="All 2 branches missed.">               offendingText.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*\\..*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">               !DATABASE_QUALIFIED_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L263" title="All 2 branches missed.">               !QUALIFIED_IDENTIFIER_PATTERN.matcher(offendingText).matches();</span>
    }

    /**
     * Checks if message name is invalid.
     */
    private boolean isInvalidMessageName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L270" title="All 2 branches missed.">        return hasMessageTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L271" title="All 2 branches missed.">               !offendingText.equals(&quot;*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">               !HEX_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">               !DECIMAL_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">               !QUALIFIED_IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L275" title="All 2 branches missed.">               !DATABASE_QUALIFIED_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">               !IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">               !offendingText.equals(&quot;;&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;);</span>
    }

    /**
     * Checks if tokens contain a message type keyword.
     */
    private boolean hasMessageTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L285" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L286">            return false;</span>
        }

<span class="nc bnc" id="L289" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">            if (MESSAGE_TYPE_KEYWORDS.contains(token.getText())) {</span>
<span class="nc" id="L291">                return true;</span>
            }
<span class="nc" id="L293">        }</span>
<span class="nc" id="L294">        return false;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L301" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L302">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L304" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L305">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a message type error result.
     */
    private ErrorResult createMessageTypeError(CaplErrorType errorType, String messageKey,
                                             String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L313">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L314">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L316">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L317">        metadata.put(&quot;errorType&quot;, &quot;messageType&quot;);</span>
<span class="nc" id="L318">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L319">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L321">        return ErrorResult.builder()</span>
<span class="nc" id="L322">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L323">            .enhancedMessage(message)</span>
<span class="nc" id="L324">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L325">            .suggestion(suggestion)</span>
<span class="nc" id="L326">            .metadata(metadata)</span>
<span class="nc" id="L327">            .build();</span>
    }

    /**
     * Creates a generic message type error result.
     */
    private ErrorResult createGenericMessageTypeError(ErrorContext context, String messageType) {
<span class="nc" id="L334">        String message = I18n.l(&quot;analyzer.messageType.message.generic&quot;, messageType, getOffendingText(context));</span>
<span class="nc" id="L335">        String suggestion = I18n.l(&quot;analyzer.messageType.message.genericSuggestion&quot;, messageType);</span>

<span class="nc" id="L337">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L338">        metadata.put(&quot;errorType&quot;, &quot;messageType_generic&quot;);</span>
<span class="nc" id="L339">        metadata.put(&quot;messageType&quot;, messageType);</span>
<span class="nc" id="L340">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L342">        return ErrorResult.builder()</span>
<span class="nc" id="L343">            .ruleId(CaplErrorType.PARSER_MESSAGE_TYPE_ERROR.getErrorCode())</span>
<span class="nc" id="L344">            .enhancedMessage(message)</span>
<span class="nc" id="L345">            .severity(CaplErrorType.PARSER_MESSAGE_TYPE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L346">            .suggestion(suggestion)</span>
<span class="nc" id="L347">            .metadata(metadata)</span>
<span class="nc" id="L348">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>