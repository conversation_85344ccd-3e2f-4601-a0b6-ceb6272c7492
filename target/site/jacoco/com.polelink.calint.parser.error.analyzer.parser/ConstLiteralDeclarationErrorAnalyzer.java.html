<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConstLiteralDeclarationErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">ConstLiteralDeclarationErrorAnalyzer.java</span></div><h1>ConstLiteralDeclarationErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Analyzer for constant literal declaration errors in CAPL programs.
 * Handles const literal declarations like: const x = 1;
 */
public class ConstLiteralDeclarationErrorAnalyzer extends ParserErrorAnalyzer {

    // Patterns for literal validation
<span class="nc" id="L24">    private static final Pattern INTEGER_LITERAL_PATTERN = Pattern.compile(&quot;-?\\d+&quot;);</span>
<span class="nc" id="L25">    private static final Pattern FLOAT_LITERAL_PATTERN = Pattern.compile(&quot;-?\\d+\\.\\d+([eE][+-]?\\d+)?&quot;);</span>
<span class="nc" id="L26">    private static final Pattern HEX_LITERAL_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L27">    private static final Pattern CHAR_LITERAL_PATTERN = Pattern.compile(&quot;'.'&quot;);</span>
<span class="nc" id="L28">    private static final Pattern STRING_LITERAL_PATTERN = Pattern.compile(&quot;\&quot;.*\&quot;&quot;);</span>
<span class="nc" id="L29">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public ConstLiteralDeclarationErrorAnalyzer() {
<span class="nc" id="L32">        super(&quot;ConstLiteralDeclarationErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 170,</span>
<span class="nc" id="L33">              Set.of(CaplParseContext.CONST_LITERAL_DECLARATION));</span>
<span class="nc" id="L34">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L38">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L40">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L43" title="All 2 branches missed.">        return parseContext == CaplParseContext.CONST_LITERAL_DECLARATION ||</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">               isConstLiteralDeclarationError(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L50">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L51">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L52">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L55">            String offendingText = getOffendingText(context);</span>

            // Check for missing const keyword
<span class="nc bnc" id="L58" title="All 2 branches missed.">            if (isMissingConstKeyword(precedingTokens, offendingText)) {</span>
<span class="nc" id="L59">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.missingConstKeyword&quot;,
                    &quot;analyzer.constLiteral.suggestion.missingConstKeyword&quot;,
                    context
                );
            }

            // Check for missing variable name
<span class="nc bnc" id="L68" title="All 2 branches missed.">            if (isMissingVariableName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L69">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.missingVariableName&quot;,
                    &quot;analyzer.constLiteral.suggestion.missingVariableName&quot;,
                    context
                );
            }

            // Check for missing assignment operator
<span class="nc bnc" id="L78" title="All 2 branches missed.">            if (isMissingAssignmentOperator(precedingTokens, offendingText)) {</span>
<span class="nc" id="L79">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.missingAssignmentOperator&quot;,
                    &quot;analyzer.constLiteral.suggestion.missingAssignmentOperator&quot;,
                    context
                );
            }

            // Check for missing literal value
<span class="nc bnc" id="L88" title="All 2 branches missed.">            if (isMissingLiteralValue(precedingTokens, offendingText)) {</span>
<span class="nc" id="L89">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.missingLiteralValue&quot;,
                    &quot;analyzer.constLiteral.suggestion.missingLiteralValue&quot;,
                    context
                );
            }

            // Check for invalid literal value
<span class="nc bnc" id="L98" title="All 2 branches missed.">            if (isInvalidLiteralValue(offendingText)) {</span>
<span class="nc" id="L99">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.invalidLiteralValue&quot;,
                    &quot;analyzer.constLiteral.suggestion.invalidLiteralValue&quot;,
                    context,
                    offendingText
                );
            }

            // Check for missing semicolon
<span class="nc bnc" id="L109" title="All 2 branches missed.">            if (isMissingSemicolon(precedingTokens, offendingText)) {</span>
<span class="nc" id="L110">                return createConstLiteralError(</span>
                    CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                    &quot;analyzer.constLiteral.message.missingSemicolon&quot;,
                    &quot;analyzer.constLiteral.suggestion.missingSemicolon&quot;,
                    context
                );
            }

<span class="nc" id="L118">            return createGenericConstLiteralError(context);</span>

<span class="nc" id="L120">        } catch (Exception e) {</span>
<span class="nc" id="L121">            return createGenericConstLiteralError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a const literal declaration error.
     */
    private boolean isConstLiteralDeclarationError(ErrorContext context) {
<span class="nc" id="L131">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L132">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L133">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L136">        return hasTokenPattern(precedingTokens, &quot;const&quot;);</span>
    }

    /**
     * Checks if const keyword is missing.
     */
    private boolean isMissingConstKeyword(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L143" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L144">            return false;</span>
        }

        // Look for variable name without const keyword
<span class="nc bnc" id="L148" title="All 2 branches missed.">        return IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">               !hasTokenPattern(tokens, &quot;const&quot;);</span>
    }

    /**
     * Checks if variable name is missing.
     */
    private boolean isMissingVariableName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L156" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L157">            return false;</span>
        }

        // Look for const keyword followed by assignment without variable name
<span class="nc bnc" id="L161" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;const&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">               offendingText.equals(&quot;=&quot;);</span>
    }

    /**
     * Checks if assignment operator is missing.
     */
    private boolean isMissingAssignmentOperator(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L169" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L170">            return false;</span>
        }

        // Look for const + identifier without assignment
<span class="nc bnc" id="L174" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;const&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">               hasIdentifierToken(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L176" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if literal value is missing.
     */
    private boolean isMissingLiteralValue(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L183" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L184">            return false;</span>
        }

        // Look for const + identifier + = without value
<span class="nc bnc" id="L188" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;const&quot;, &quot;=&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L189" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if literal value is invalid.
     */
    private boolean isInvalidLiteralValue(String text) {
<span class="nc bnc" id="L196" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L197">            return false;</span>
        }

        // Check if it looks like a literal but isn't valid
<span class="nc bnc" id="L201" title="All 2 branches missed.">        return !isValidLiteral(text) &amp;&amp; </span>
<span class="nc bnc" id="L202" title="All 6 branches missed.">               (text.matches(&quot;\\d.*&quot;) || text.startsWith(&quot;\&quot;&quot;) || text.startsWith(&quot;'&quot;));</span>
    }

    /**
     * Checks if semicolon is missing.
     */
    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L209" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L210">            return false;</span>
        }

        // Look for complete const declaration without semicolon
<span class="nc bnc" id="L214" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;const&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">               hasIdentifierToken(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;=&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L217" title="All 2 branches missed.">               hasLiteralToken(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L218" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if the text is a valid literal.
     */
    private boolean isValidLiteral(String text) {
<span class="nc bnc" id="L225" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L226">            return false;</span>
        }

<span class="nc bnc" id="L229" title="All 2 branches missed.">        return INTEGER_LITERAL_PATTERN.matcher(text).matches() ||</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">               FLOAT_LITERAL_PATTERN.matcher(text).matches() ||</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">               HEX_LITERAL_PATTERN.matcher(text).matches() ||</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">               CHAR_LITERAL_PATTERN.matcher(text).matches() ||</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">               STRING_LITERAL_PATTERN.matcher(text).matches();</span>
    }

    /**
     * Checks if tokens contain an identifier.
     */
    private boolean hasIdentifierToken(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L240" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L241">            return false;</span>
        }

<span class="nc bnc" id="L244" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">            if (IDENTIFIER_PATTERN.matcher(token.getText()).matches() &amp;&amp;</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">                !token.getText().equals(&quot;const&quot;)) {</span>
<span class="nc" id="L247">                return true;</span>
            }
<span class="nc" id="L249">        }</span>
<span class="nc" id="L250">        return false;</span>
    }

    /**
     * Checks if tokens contain a literal value.
     */
    private boolean hasLiteralToken(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L257" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L258">            return false;</span>
        }

<span class="nc bnc" id="L261" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">            if (isValidLiteral(token.getText())) {</span>
<span class="nc" id="L263">                return true;</span>
            }
<span class="nc" id="L265">        }</span>
<span class="nc" id="L266">        return false;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L273" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L274">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L276" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L277">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a const literal error result.
     */
    private ErrorResult createConstLiteralError(CaplErrorType errorType, String messageKey,
                                              String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L285">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L286">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L288">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L289">        metadata.put(&quot;errorType&quot;, &quot;constLiteralDeclaration&quot;);</span>
<span class="nc" id="L290">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L291">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L293">        return ErrorResult.builder()</span>
<span class="nc" id="L294">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L295">            .enhancedMessage(message)</span>
<span class="nc" id="L296">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L297">            .suggestion(suggestion)</span>
<span class="nc" id="L298">            .metadata(metadata)</span>
<span class="nc" id="L299">            .build();</span>
    }

    /**
     * Creates a generic const literal error result.
     */
    private ErrorResult createGenericConstLiteralError(ErrorContext context) {
<span class="nc" id="L306">        String message = I18n.l(&quot;analyzer.constLiteral.message.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L307">        String suggestion = I18n.l(&quot;analyzer.constLiteral.suggestion.generic&quot;);</span>

<span class="nc" id="L309">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L310">        metadata.put(&quot;errorType&quot;, &quot;constLiteralDeclaration_generic&quot;);</span>
<span class="nc" id="L311">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L313">        return ErrorResult.builder()</span>
<span class="nc" id="L314">            .ruleId(CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR.getErrorCode())</span>
<span class="nc" id="L315">            .enhancedMessage(message)</span>
<span class="nc" id="L316">            .severity(CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L317">            .suggestion(suggestion)</span>
<span class="nc" id="L318">            .metadata(metadata)</span>
<span class="nc" id="L319">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>