<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CompilationUnitErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">CompilationUnitErrorAnalyzer.java</span></div><h1>CompilationUnitErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for compilation unit structure errors in CAPL programs.
 * Handles compilationUnit rule (CAPL.g4 line 19) including:
 * - Overall program structure validation
 * - Missing required sections
 * - Section ordering validation
 * - EOF handling
 */
public class CompilationUnitErrorAnalyzer extends ParserErrorAnalyzer {

    // CAPL program structure keywords
<span class="nc" id="L27">    private static final Set&lt;String&gt; SECTION_KEYWORDS = Set.of(</span>
        &quot;includes&quot;, &quot;variables&quot;
    );
    
<span class="nc" id="L31">    private static final Set&lt;String&gt; HANDLER_KEYWORDS = Set.of(</span>
        &quot;on&quot;
    );
    
<span class="nc" id="L35">    private static final Set&lt;String&gt; FUNCTION_KEYWORDS = Set.of(</span>
        &quot;export&quot;, &quot;testcase&quot;, &quot;testfunction&quot;, &quot;void&quot;, &quot;int&quot;, &quot;char&quot;, &quot;float&quot;, &quot;double&quot;
    );

    public CompilationUnitErrorAnalyzer() {
<span class="nc" id="L40">        super(&quot;CompilationUnitErrorAnalyzer&quot;, AnalyzerType.PARSER_STRUCTURAL, 150,</span>
<span class="nc" id="L41">              Set.of(CaplParseContext.COMPILATION_UNIT, CaplParseContext.CAPL_PROGRAM));</span>
<span class="nc" id="L42">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L46">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L48">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L51" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">               isCompilationUnitError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L57">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L62">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L68">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L69">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L70">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L73">            String offendingText = getOffendingText(context);</span>

            // Check for missing EOF
<span class="nc bnc" id="L76" title="All 2 branches missed.">            if (isMissingEOF(precedingTokens, offendingText)) {</span>
<span class="nc" id="L77">                return createCompilationUnitError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.compilationUnit.missingEOF&quot;,
                    &quot;analyzer.compilationUnit.missingEOFSuggestion&quot;,
                    context
                );
            }

            // Check for invalid program structure
<span class="nc bnc" id="L86" title="All 2 branches missed.">            if (isInvalidProgramStructure(precedingTokens, offendingText)) {</span>
<span class="nc" id="L87">                return createCompilationUnitError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.compilationUnit.invalidStructure&quot;,
                    &quot;analyzer.compilationUnit.invalidStructureSuggestion&quot;,
                    context
                );
            }

            // Check for missing section closing
<span class="nc bnc" id="L96" title="All 2 branches missed.">            if (isMissingSectionClosing(precedingTokens, offendingText)) {</span>
<span class="nc" id="L97">                return createCompilationUnitError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.compilationUnit.missingSectionClosing&quot;,
                    &quot;analyzer.compilationUnit.missingSectionClosingSuggestion&quot;,
                    context
                );
            }

            // Check for unexpected content
<span class="nc bnc" id="L106" title="All 2 branches missed.">            if (isUnexpectedContent(precedingTokens, offendingText)) {</span>
<span class="nc" id="L107">                return createCompilationUnitError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.compilationUnit.unexpectedContent&quot;,
                    &quot;analyzer.compilationUnit.unexpectedContentSuggestion&quot;,
                    context
                );
            }

<span class="nc" id="L115">            return createGenericCompilationUnitError(context);</span>

<span class="nc" id="L117">        } catch (Exception e) {</span>
<span class="nc" id="L118">            return createGenericCompilationUnitError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a compilation unit error.
     */
    private boolean isCompilationUnitError(ErrorContext context) {
<span class="nc" id="L128">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L129">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L130">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L133" title="All 2 branches missed.">        return hasSectionKeyword(precedingTokens) || </span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">               hasHandlerKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">               hasFunctionKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">               isAtFileLevel(context);</span>
    }

    /**
     * Checks if missing EOF.
     */
    private boolean isMissingEOF(List&lt;Token&gt; tokens, String offendingText) {
        // Check if we have content but no proper EOF
<span class="nc bnc" id="L144" title="All 4 branches missed.">        return tokens != null &amp;&amp; tokens.size() &gt; 0 &amp;&amp; </span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L146" title="All 4 branches missed.">               (hasCompleteSection(tokens) || hasCompleteHandler(tokens));</span>
    }

    /**
     * Checks if program structure is invalid.
     */
    private boolean isInvalidProgramStructure(List&lt;Token&gt; tokens, String offendingText) {
        // Check for sections appearing in wrong order or invalid nesting
<span class="nc bnc" id="L154" title="All 2 branches missed.">        return hasSectionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L155" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || isInvalidSectionOrder(tokens));</span>
    }

    /**
     * Checks if section closing is missing.
     */
    private boolean isMissingSectionClosing(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L162" title="All 2 branches missed.">        return hasSectionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L163" title="All 2 branches missed.">               hasOpeningBrace(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">               !hasClosingBrace(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">               offendingText.equals(&quot;EOF&quot;);</span>
    }

    /**
     * Checks if there's unexpected content.
     */
    private boolean isUnexpectedContent(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L172" title="All 2 branches missed.">        return !hasSectionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">               !hasHandlerKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">               !hasFunctionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;);</span>
    }

    // ========== Validation Methods ==========

    private boolean hasSectionKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L182">        return hasAnyKeyword(tokens, SECTION_KEYWORDS);</span>
    }

    private boolean hasHandlerKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L186">        return hasAnyKeyword(tokens, HANDLER_KEYWORDS);</span>
    }

    private boolean hasFunctionKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L190">        return hasAnyKeyword(tokens, FUNCTION_KEYWORDS);</span>
    }

    private boolean hasOpeningBrace(List&lt;Token&gt; tokens) {
<span class="nc" id="L194">        return hasKeyword(tokens, &quot;{&quot;);</span>
    }

    private boolean hasClosingBrace(List&lt;Token&gt; tokens) {
<span class="nc" id="L198">        return hasKeyword(tokens, &quot;}&quot;);</span>
    }

    private boolean hasCompleteSection(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L202" title="All 6 branches missed.">        return hasSectionKeyword(tokens) &amp;&amp; hasOpeningBrace(tokens) &amp;&amp; hasClosingBrace(tokens);</span>
    }

    private boolean hasCompleteHandler(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L206" title="All 6 branches missed.">        return hasHandlerKeyword(tokens) &amp;&amp; hasOpeningBrace(tokens) &amp;&amp; hasClosingBrace(tokens);</span>
    }

    private boolean isInvalidSectionOrder(List&lt;Token&gt; tokens) {
        // Simple check for invalid section order
        // In CAPL, includes should come before variables
<span class="nc" id="L212">        boolean hasIncludes = hasKeyword(tokens, &quot;includes&quot;);</span>
<span class="nc" id="L213">        boolean hasVariables = hasKeyword(tokens, &quot;variables&quot;);</span>
        
<span class="nc bnc" id="L215" title="All 4 branches missed.">        if (hasIncludes &amp;&amp; hasVariables) {</span>
            // Check if variables appears before includes
<span class="nc" id="L217">            int includesIndex = getKeywordIndex(tokens, &quot;includes&quot;);</span>
<span class="nc" id="L218">            int variablesIndex = getKeywordIndex(tokens, &quot;variables&quot;);</span>
<span class="nc bnc" id="L219" title="All 2 branches missed.">            return variablesIndex &lt; includesIndex;</span>
        }
        
<span class="nc" id="L222">        return false;</span>
    }

    private boolean isAtFileLevel(ErrorContext context) {
        // Simple heuristic: if we're at the beginning of the file or after a complete section
<span class="nc bnc" id="L227" title="All 4 branches missed.">        return context.getLine() &lt;= 5 || context.getCharPositionInLine() == 0;</span>
    }

    // ========== Utility Methods ==========

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L233" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L234">            return false;</span>
        }

<span class="nc bnc" id="L237" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L239">                return true;</span>
            }
<span class="nc" id="L241">        }</span>
<span class="nc" id="L242">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L246" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L247">            return false;</span>
        }

<span class="nc bnc" id="L250" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L252">                return true;</span>
            }
<span class="nc" id="L254">        }</span>
<span class="nc" id="L255">        return false;</span>
    }

    private int getKeywordIndex(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L259" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L260">            return -1;</span>
        }

<span class="nc bnc" id="L263" title="All 2 branches missed.">        for (int i = 0; i &lt; tokens.size(); i++) {</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">            if (keyword.equals(tokens.get(i).getText())) {</span>
<span class="nc" id="L265">                return i;</span>
            }
        }
<span class="nc" id="L268">        return -1;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L272" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L273">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L275" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L276">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createCompilationUnitError(CaplErrorType errorType, String messageKey,
                                                  String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L281">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L282">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L284">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L285">        metadata.put(&quot;errorType&quot;, &quot;compilationUnit&quot;);</span>
<span class="nc" id="L286">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L287">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L289">        return ErrorResult.builder()</span>
<span class="nc" id="L290">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L291">            .enhancedMessage(message)</span>
<span class="nc" id="L292">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L293">            .suggestion(suggestion)</span>
<span class="nc" id="L294">            .metadata(metadata)</span>
<span class="nc" id="L295">            .build();</span>
    }

    private ErrorResult createGenericCompilationUnitError(ErrorContext context) {
<span class="nc" id="L299">        String message = I18n.l(&quot;analyzer.compilationUnit.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L300">        String suggestion = I18n.l(&quot;analyzer.compilationUnit.genericSuggestion&quot;);</span>

<span class="nc" id="L302">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L303">        metadata.put(&quot;errorType&quot;, &quot;compilationUnit_generic&quot;);</span>
<span class="nc" id="L304">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L306">        return ErrorResult.builder()</span>
<span class="nc" id="L307">            .ruleId(CaplErrorType.PARSER_COMPILATION_UNIT_ERROR.getErrorCode())</span>
<span class="nc" id="L308">            .enhancedMessage(message)</span>
<span class="nc" id="L309">            .severity(CaplErrorType.PARSER_COMPILATION_UNIT_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L310">            .suggestion(suggestion)</span>
<span class="nc" id="L311">            .metadata(metadata)</span>
<span class="nc" id="L312">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>