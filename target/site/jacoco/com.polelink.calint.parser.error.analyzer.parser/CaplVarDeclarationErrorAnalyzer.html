<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplVarDeclarationErrorAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_class">CaplVarDeclarationErrorAnalyzer</span></div><h1>CaplVarDeclarationErrorAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">769 of 769</td><td class="ctr2">0%</td><td class="bar">158 of 158</td><td class="ctr2">0%</td><td class="ctr1">116</td><td class="ctr2">116</td><td class="ctr1">143</td><td class="ctr2">143</td><td class="ctr1">37</td><td class="ctr2">37</td></tr></tfoot><tbody><tr><td id="a4"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L62" class="el_method">analyzeSpecific(ErrorContext)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="66" alt="66"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L109" class="el_method">analyzeConstLiteralDeclarationError(ErrorContext, List, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="60" alt="60"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L406" class="el_method">createGenericCaplVarDeclarationError(ErrorContext)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="47" alt="47"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L156" class="el_method">analyzeAssociativeArrayDeclarationError(ErrorContext, List, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="46" alt="46"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L388" class="el_method">createCaplVarDeclarationError(CaplErrorType, String, String, ErrorContext, Object[])</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="45" alt="45"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L193" class="el_method">analyzeTypeDefinitionError(ErrorContext, List, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="32" alt="32"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L220" class="el_method">analyzeRegularDeclarationError(ErrorContext, List, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="32" alt="32"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h10">5</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a22"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L93" class="el_method">isCaplVarDeclarationError(ErrorContext)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="29" alt="29"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a24"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L287" class="el_method">isMissingArrayBrackets(List, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="25" alt="25"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h14">3</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a17"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L336" class="el_method">hasIdentifier(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a19"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L353" class="el_method">hasKeyword(List, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="24" alt="24"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g11">4</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a12"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L366" class="el_method">hasAnyKeyword(List, Set)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="24" alt="24"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a6"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L40" class="el_method">canAnalyze(ErrorContext)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="22" alt="22"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h11">5</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a28"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L274" class="el_method">isMissingConstValue(List, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="21" alt="21"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h18">2</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a32"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L293" class="el_method">isMissingTypeBody(List, String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="21" alt="21"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h15">3</td><td class="ctr2" id="i15">3</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a34"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L257" class="el_method">isRegularDeclarationError(List, String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a26"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L269" class="el_method">isMissingConstAssignment(List, String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a31"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L308" class="el_method">isMissingSemicolon(List, String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f8">5</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h20">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a36"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L27" class="el_method">static {...}</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="19" alt="19"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h16">3</td><td class="ctr2" id="i16">3</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a11"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L379" class="el_method">getOffendingText(ErrorContext)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h13">4</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a27"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L264" class="el_method">isMissingConstIdentifier(List, String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="16" alt="16"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h21">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a29"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L303" class="el_method">isMissingDeclarator(List, String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="16" alt="16"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a13"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L327" class="el_method">hasArrayBrackets(List)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="14" alt="14"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a30"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L283" class="el_method">isMissingKeyType(List, String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="13" alt="13"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f21">3</td><td class="ctr2" id="g21">3</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a33"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L299" class="el_method">isMissingTypeSpecifier(List, String)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="13" alt="13"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a21"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L249" class="el_method">isAssociativeArrayDeclarationError(List, String)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">3</td><td class="ctr2" id="g22">3</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a25"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L279" class="el_method">isMissingArrayIdentifier(List, String)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g23">3</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a18"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L349" class="el_method">hasIdentifier(String)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="10" alt="10"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">3</td><td class="ctr2" id="g24">3</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a16"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L332" class="el_method">hasDeclarationPattern(List)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="9" alt="9"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">3</td><td class="ctr2" id="g25">3</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a8"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L34" class="el_method">CaplVarDeclarationErrorAnalyzer()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="8" alt="8"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h17">3</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a15"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L315" class="el_method">hasConstKeyword(List)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a20"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L319" class="el_method">hasTypeDefinitionKeyword(List)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a14"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L323" class="el_method">hasAssociativeArrayKeyword(List)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a7"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L51" class="el_method">canAnalyzeSpecific(ErrorContext)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a0"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L56" class="el_method">analyze(ErrorContext)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a23"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L245" class="el_method">isConstLiteralDeclarationError(List, String)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a35"><a href="CaplVarDeclarationErrorAnalyzer.java.html#L253" class="el_method">isTypeDefinitionError(List, String)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>