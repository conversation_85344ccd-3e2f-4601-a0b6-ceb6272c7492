<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SectionStructureErrorAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_class">SectionStructureErrorAnalyzer</span></div><h1>SectionStructureErrorAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">604 of 604</td><td class="ctr2">0%</td><td class="bar">96 of 96</td><td class="ctr2">0%</td><td class="ctr1">73</td><td class="ctr2">73</td><td class="ctr1">116</td><td class="ctr2">116</td><td class="ctr1">25</td><td class="ctr2">25</td></tr></tfoot><tbody><tr><td id="a24"><a href="SectionStructureErrorAnalyzer.java.html#L27" class="el_method">static {...}</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="65" alt="65"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">3</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="SectionStructureErrorAnalyzer.java.html#L111" class="el_method">analyzeIncludesSectionError(ErrorContext, List, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="110" height="10" title="60" alt="60"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="SectionStructureErrorAnalyzer.java.html#L158" class="el_method">analyzeVariablesSectionError(ErrorContext, List, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="110" height="10" title="60" alt="60"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="SectionStructureErrorAnalyzer.java.html#L318" class="el_method">createGenericSectionStructureError(ErrorContext)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="47" alt="47"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="SectionStructureErrorAnalyzer.java.html#L300" class="el_method">createSectionStructureError(CaplErrorType, String, String, ErrorContext, Object[])</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="45" alt="45"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="SectionStructureErrorAnalyzer.java.html#L71" class="el_method">analyzeSpecific(ErrorContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h2">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a15"><a href="SectionStructureErrorAnalyzer.java.html#L224" class="el_method">isInvalidIncludeDirective(List, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="26" alt="26"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a16"><a href="SectionStructureErrorAnalyzer.java.html#L237" class="el_method">isInvalidVariableDeclaration(List, String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="26" alt="26"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a18"><a href="SectionStructureErrorAnalyzer.java.html#L211" class="el_method">isMissingOpeningBrace(List, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="25" alt="25"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h14">3</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a11"><a href="SectionStructureErrorAnalyzer.java.html#L265" class="el_method">hasKeyword(List, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">4</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a9"><a href="SectionStructureErrorAnalyzer.java.html#L278" class="el_method">hasAnyKeyword(List, Set)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="24" alt="24"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a4"><a href="SectionStructureErrorAnalyzer.java.html#L49" class="el_method">canAnalyze(ErrorContext)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="22" alt="22"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a17"><a href="SectionStructureErrorAnalyzer.java.html#L217" class="el_method">isMissingClosingBrace(List, String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="22" alt="22"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a19"><a href="SectionStructureErrorAnalyzer.java.html#L245" class="el_method">isMissingSemicolon(List, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="21" alt="21"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h15">3</td><td class="ctr2" id="i15">3</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a20"><a href="SectionStructureErrorAnalyzer.java.html#L232" class="el_method">isMissingStringLiteral(List, String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a8"><a href="SectionStructureErrorAnalyzer.java.html#L291" class="el_method">getOffendingText(ErrorContext)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="18" alt="18"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">3</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h11">4</td><td class="ctr2" id="i11">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a21"><a href="SectionStructureErrorAnalyzer.java.html#L98" class="el_method">isSectionStructureError(ErrorContext)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="13" alt="13"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a23"><a href="SectionStructureErrorAnalyzer.java.html#L43" class="el_method">SectionStructureErrorAnalyzer()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="9" alt="9"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h16">3</td><td class="ctr2" id="i16">3</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a14"><a href="SectionStructureErrorAnalyzer.java.html#L203" class="el_method">isIncludesSectionError(List, String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a22"><a href="SectionStructureErrorAnalyzer.java.html#L207" class="el_method">isVariablesSectionError(List, String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a12"><a href="SectionStructureErrorAnalyzer.java.html#L253" class="el_method">hasSectionKeyword(List)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a10"><a href="SectionStructureErrorAnalyzer.java.html#L257" class="el_method">hasIncludeKeyword(List)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a13"><a href="SectionStructureErrorAnalyzer.java.html#L261" class="el_method">hasVariableKeyword(List)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a5"><a href="SectionStructureErrorAnalyzer.java.html#L60" class="el_method">canAnalyzeSpecific(ErrorContext)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a0"><a href="SectionStructureErrorAnalyzer.java.html#L65" class="el_method">analyze(ErrorContext)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>