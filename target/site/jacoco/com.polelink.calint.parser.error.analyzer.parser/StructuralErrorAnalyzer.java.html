<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StructuralErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">StructuralErrorAnalyzer.java</span></div><h1>StructuralErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Analyzer for structural errors in CAPL programs.
 * Handles compilation unit, section, block, and statement structure errors.
 */
public class StructuralErrorAnalyzer extends ParserErrorAnalyzer {

<span class="nc" id="L23">    private static final Pattern INCLUDE_PATTERN = Pattern.compile(&quot;#include\\s+[\&quot;&lt;].*[\&quot;&gt;]&quot;);</span>
<span class="nc" id="L24">    private static final Pattern SECTION_PATTERN = Pattern.compile(&quot;(variables|includes)\\s*\\{&quot;);</span>
<span class="nc" id="L25">    private static final Pattern BRACE_PATTERN = Pattern.compile(&quot;[{}]&quot;);</span>

    public StructuralErrorAnalyzer() {
<span class="nc" id="L28">        super(&quot;StructuralErrorAnalyzer&quot;, AnalyzerType.PARSER_STRUCTURAL, 150,</span>
<span class="nc" id="L29">              Set.of(CaplParseContext.COMPILATION_UNIT,</span>
                     CaplParseContext.CAPL_PROGRAM,
                     CaplParseContext.INCLUDES_SECTION,
                     CaplParseContext.VARIABLES_SECTION,
                     CaplParseContext.COMPOUND_STATEMENT,
                     CaplParseContext.UNKNOWN)); // Add UNKNOWN to supported contexts
<span class="nc" id="L35">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
        // This analyzer can handle any parser error context
        // The base class already checks for supported contexts
<span class="nc" id="L41">        return true;</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
<span class="nc" id="L46">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L48">            parseContext = detectParseContext(context);</span>
        }

        // Analyze based on context
<span class="nc bnc" id="L52" title="All 6 branches missed.">        switch (parseContext) {</span>
            case COMPILATION_UNIT:
<span class="nc" id="L54">                return analyzeCompilationUnitError(context);</span>
            case CAPL_PROGRAM:
<span class="nc" id="L56">                return analyzeCaplProgramError(context);</span>
            case INCLUDES_SECTION:
<span class="nc" id="L58">                return analyzeIncludesSectionError(context);</span>
            case VARIABLES_SECTION:
<span class="nc" id="L60">                return analyzeVariablesSectionError(context);</span>
            case COMPOUND_STATEMENT:
<span class="nc" id="L62">                return analyzeCompoundStatementError(context);</span>
            default:
<span class="nc" id="L64">                return analyzeGenericStructuralError(context);</span>
        }
    }

    /**
     * Analyzes compilation unit structure errors.
     */
    private ErrorResult analyzeCompilationUnitError(ErrorContext context) {
        try {
<span class="nc" id="L73">            List&lt;Token&gt; precedingTokens = null;</span>
<span class="nc bnc" id="L74" title="All 4 branches missed.">            if (context.getRecognizer() instanceof Parser &amp;&amp; context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L75">                precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L76">                    (Parser) context.getRecognizer(),</span>
<span class="nc" id="L77">                    (Token) context.getOffendingSymbol()</span>
                );
            }

            // Check for missing program structure
<span class="nc bnc" id="L82" title="All 2 branches missed.">            if (isMissingProgramStructure(precedingTokens)) {</span>
<span class="nc" id="L83">                return createStructuralError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.structural.message.missingProgramStructure&quot;,
                    &quot;analyzer.structural.suggestion.missingProgramStructure&quot;,
                    context
                );
            }

            // Check for incorrect section order
<span class="nc bnc" id="L92" title="All 2 branches missed.">            if (hasIncorrectSectionOrder(precedingTokens)) {</span>
<span class="nc" id="L93">                return createStructuralError(</span>
                    CaplErrorType.PARSER_COMPILATION_UNIT_ERROR,
                    &quot;analyzer.structural.message.incorrectSectionOrder&quot;,
                    &quot;analyzer.structural.suggestion.incorrectSectionOrder&quot;,
                    context
                );
            }

<span class="nc" id="L101">            return createGenericStructuralError(context, &quot;compilation unit&quot;);</span>
<span class="nc" id="L102">        } catch (Exception e) {</span>
<span class="nc" id="L103">            return createGenericStructuralError(context, &quot;compilation unit&quot;);</span>
        }
    }

    /**
     * Analyzes CAPL program structure errors.
     */
    private ErrorResult analyzeCaplProgramError(ErrorContext context) {
        try {
<span class="nc" id="L112">            List&lt;Token&gt; precedingTokens = null;</span>
<span class="nc bnc" id="L113" title="All 4 branches missed.">            if (context.getRecognizer() instanceof Parser &amp;&amp; context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L114">                precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L115">                    (Parser) context.getRecognizer(),</span>
<span class="nc" id="L116">                    (Token) context.getOffendingSymbol()</span>
                );
            }

            // Check for missing section braces
<span class="nc bnc" id="L121" title="All 2 branches missed.">            if (isMissingSectionBraces(precedingTokens)) {</span>
<span class="nc" id="L122">                return createStructuralError(</span>
                    CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR,
                    &quot;analyzer.structural.message.missingSectionBraces&quot;,
                    &quot;analyzer.structural.suggestion.missingSectionBraces&quot;,
                    context
                );
            }

<span class="nc" id="L130">            return createGenericStructuralError(context, &quot;CAPL program&quot;);</span>
<span class="nc" id="L131">        } catch (Exception e) {</span>
<span class="nc" id="L132">            return createGenericStructuralError(context, &quot;CAPL program&quot;);</span>
        }
    }

    /**
     * Analyzes includes section errors.
     */
    private ErrorResult analyzeIncludesSectionError(ErrorContext context) {
<span class="nc" id="L140">        String offendingText = getOffendingText(context);</span>
        
        // Check for malformed include statement
<span class="nc bnc" id="L143" title="All 2 branches missed.">        if (isMalformedIncludeStatement(offendingText)) {</span>
<span class="nc" id="L144">            return createStructuralError(</span>
                CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR,
                &quot;analyzer.structural.message.malformedInclude&quot;,
                &quot;analyzer.structural.suggestion.malformedInclude&quot;,
                context,
                offendingText
            );
        }

        // Check for missing include quotes
<span class="nc bnc" id="L154" title="All 2 branches missed.">        if (isMissingIncludeQuotes(offendingText)) {</span>
<span class="nc" id="L155">            return createStructuralError(</span>
                CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR,
                &quot;analyzer.structural.message.missingIncludeQuotes&quot;,
                &quot;analyzer.structural.suggestion.missingIncludeQuotes&quot;,
                context,
                offendingText
            );
        }

<span class="nc" id="L164">        return createGenericStructuralError(context, &quot;includes section&quot;);</span>
    }

    /**
     * Analyzes variables section errors.
     */
    private ErrorResult analyzeVariablesSectionError(ErrorContext context) {
<span class="nc" id="L171">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L172">            (Parser) context.getRecognizer(), </span>
<span class="nc" id="L173">            (Token) context.getOffendingSymbol()</span>
        );

        // Check for missing variable declaration syntax
<span class="nc bnc" id="L177" title="All 2 branches missed.">        if (isMissingVariableDeclarationSyntax(precedingTokens)) {</span>
<span class="nc" id="L178">            return createStructuralError(</span>
                CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR,
                &quot;analyzer.structural.message.missingVariableDeclaration&quot;,
                &quot;analyzer.structural.suggestion.missingVariableDeclaration&quot;,
                context
            );
        }

<span class="nc" id="L186">        return createGenericStructuralError(context, &quot;variables section&quot;);</span>
    }

    /**
     * Analyzes compound statement (block) errors.
     */
    private ErrorResult analyzeCompoundStatementError(ErrorContext context) {
<span class="nc" id="L193">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L194">            (Parser) context.getRecognizer(), </span>
<span class="nc" id="L195">            (Token) context.getOffendingSymbol()</span>
        );

        // Check for unmatched braces
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (hasUnmatchedBraces(precedingTokens)) {</span>
<span class="nc" id="L200">            return createStructuralError(</span>
                CaplErrorType.PARSER_BLOCK_STRUCTURE_ERROR,
                &quot;analyzer.structural.message.unmatchedBraces&quot;,
                &quot;analyzer.structural.suggestion.unmatchedBraces&quot;,
                context
            );
        }

        // Check for missing semicolon
<span class="nc bnc" id="L209" title="All 2 branches missed.">        if (isMissingSemicolon(precedingTokens)) {</span>
<span class="nc" id="L210">            return createStructuralError(</span>
                CaplErrorType.PARSER_STATEMENT_STRUCTURE_ERROR,
                &quot;analyzer.structural.message.missingSemicolon&quot;,
                &quot;analyzer.structural.suggestion.missingSemicolon&quot;,
                context
            );
        }

<span class="nc" id="L218">        return createGenericStructuralError(context, &quot;compound statement&quot;);</span>
    }

    /**
     * Analyzes generic structural errors.
     */
    private ErrorResult analyzeGenericStructuralError(ErrorContext context) {
<span class="nc" id="L225">        return createGenericStructuralError(context, &quot;structure&quot;);</span>
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a structural error based on context.
     */
    private boolean isStructuralError(ErrorContext context) {
<span class="nc bnc" id="L234" title="All 2 branches missed.">        if (context.getException() instanceof NoViableAltException) {</span>
<span class="nc" id="L235">            String offendingText = getOffendingText(context);</span>
<span class="nc bnc" id="L236" title="All 2 branches missed.">            return offendingText != null &amp;&amp; </span>
<span class="nc bnc" id="L237" title="All 4 branches missed.">                   (offendingText.equals(&quot;{&quot;) || offendingText.equals(&quot;}&quot;) || </span>
<span class="nc bnc" id="L238" title="All 4 branches missed.">                    offendingText.equals(&quot;;&quot;) || offendingText.startsWith(&quot;#&quot;));</span>
        }
<span class="nc" id="L240">        return false;</span>
    }

    /**
     * Checks if program structure is missing.
     */
    private boolean isMissingProgramStructure(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L247" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L248">            return true;</span>
        }

        // Look for basic program structure keywords
<span class="nc bnc" id="L252" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L253">            String text = token.getText();</span>
<span class="nc bnc" id="L254" title="All 4 branches missed.">            if (&quot;variables&quot;.equals(text) || &quot;includes&quot;.equals(text)) {</span>
<span class="nc" id="L255">                return false;</span>
            }
<span class="nc" id="L257">        }</span>

<span class="nc" id="L259">        return true;</span>
    }

    /**
     * Checks if sections are in incorrect order.
     */
    private boolean hasIncorrectSectionOrder(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L266" title="All 4 branches missed.">        if (tokens == null || tokens.size() &lt; 2) {</span>
<span class="nc" id="L267">            return false;</span>
        }
        
<span class="nc" id="L270">        int variablesIndex = findTokenIndex(tokens, &quot;variables&quot;);</span>
<span class="nc" id="L271">        int includesIndex = findTokenIndex(tokens, &quot;includes&quot;);</span>
        
        // includes should come before variables
<span class="nc bnc" id="L274" title="All 6 branches missed.">        return variablesIndex != -1 &amp;&amp; includesIndex != -1 &amp;&amp; includesIndex &gt; variablesIndex;</span>
    }

    /**
     * Checks if section braces are missing.
     */
    private boolean isMissingSectionBraces(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L281" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L282">            return false;</span>
        }
        
        // Look for section keywords without opening braces
<span class="nc bnc" id="L286" title="All 2 branches missed.">        for (int i = 0; i &lt; tokens.size() - 1; i++) {</span>
<span class="nc" id="L287">            Token token = tokens.get(i);</span>
<span class="nc bnc" id="L288" title="All 4 branches missed.">            if (token.getText().equals(&quot;variables&quot;) || token.getText().equals(&quot;includes&quot;)) {</span>
<span class="nc" id="L289">                Token nextToken = tokens.get(i + 1);</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">                if (!nextToken.getText().equals(&quot;{&quot;)) {</span>
<span class="nc" id="L291">                    return true;</span>
                }
            }
        }
        
<span class="nc" id="L296">        return false;</span>
    }

    /**
     * Checks if include statement is malformed.
     */
    private boolean isMalformedIncludeStatement(String text) {
<span class="nc bnc" id="L303" title="All 4 branches missed.">        if (text == null || !text.startsWith(&quot;#include&quot;)) {</span>
<span class="nc" id="L304">            return false;</span>
        }
        
<span class="nc bnc" id="L307" title="All 2 branches missed.">        return !INCLUDE_PATTERN.matcher(text).matches();</span>
    }

    /**
     * Checks if include statement is missing quotes.
     */
    private boolean isMissingIncludeQuotes(String text) {
<span class="nc bnc" id="L314" title="All 4 branches missed.">        if (text == null || !text.startsWith(&quot;#include&quot;)) {</span>
<span class="nc" id="L315">            return false;</span>
        }
        
<span class="nc bnc" id="L318" title="All 6 branches missed.">        return !text.contains(&quot;\&quot;&quot;) &amp;&amp; !text.contains(&quot;&lt;&quot;) &amp;&amp; !text.contains(&quot;&gt;&quot;);</span>
    }

    /**
     * Checks if variable declaration syntax is missing.
     */
    private boolean isMissingVariableDeclarationSyntax(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L325" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L326">            return false;</span>
        }
        
        // Look for incomplete variable declarations
<span class="nc bnc" id="L330" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;int&quot;) || </span>
<span class="nc bnc" id="L331" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;float&quot;) || </span>
<span class="nc bnc" id="L332" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;char&quot;) ||</span>
<span class="nc bnc" id="L333" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;message&quot;) ||</span>
<span class="nc bnc" id="L334" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;signal&quot;);</span>
    }

    /**
     * Checks if braces are unmatched.
     */
    private boolean hasUnmatchedBraces(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L341" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L342">            return false;</span>
        }
        
<span class="nc" id="L345">        int braceCount = 0;</span>
<span class="nc bnc" id="L346" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L347" title="All 2 branches missed.">            if (token.getText().equals(&quot;{&quot;)) {</span>
<span class="nc" id="L348">                braceCount++;</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">            } else if (token.getText().equals(&quot;}&quot;)) {</span>
<span class="nc" id="L350">                braceCount--;</span>
            }
<span class="nc" id="L352">        }</span>
        
<span class="nc bnc" id="L354" title="All 2 branches missed.">        return braceCount != 0;</span>
    }

    /**
     * Checks if semicolon is missing.
     */
    private boolean isMissingSemicolon(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L361" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L362">            return false;</span>
        }
        
        // Check if the last meaningful token should be followed by a semicolon
<span class="nc" id="L366">        Token lastToken = tokens.get(tokens.size() - 1);</span>
<span class="nc" id="L367">        String lastText = lastToken.getText();</span>
        
        // These tokens typically require semicolons
<span class="nc bnc" id="L370" title="All 2 branches missed.">        return lastText.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;) || // identifier</span>
<span class="nc bnc" id="L371" title="All 2 branches missed.">               lastText.matches(&quot;\\d+&quot;) || // number</span>
<span class="nc bnc" id="L372" title="All 2 branches missed.">               lastText.equals(&quot;)&quot;) || // closing parenthesis</span>
<span class="nc bnc" id="L373" title="All 2 branches missed.">               lastText.equals(&quot;]&quot;); // closing bracket</span>
    }

    /**
     * Finds the index of a token with specific text.
     */
    private int findTokenIndex(List&lt;Token&gt; tokens, String text) {
<span class="nc bnc" id="L380" title="All 2 branches missed.">        for (int i = 0; i &lt; tokens.size(); i++) {</span>
<span class="nc bnc" id="L381" title="All 2 branches missed.">            if (tokens.get(i).getText().equals(text)) {</span>
<span class="nc" id="L382">                return i;</span>
            }
        }
<span class="nc" id="L385">        return -1;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L392" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L393">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L395" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ? </span>
<span class="nc" id="L396">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a structural error result.
     */
    private ErrorResult createStructuralError(CaplErrorType errorType, String messageKey, 
                                            String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L404">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L405">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L407">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L408">        metadata.put(&quot;errorType&quot;, &quot;structural&quot;);</span>
<span class="nc" id="L409">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L410">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L412">        return ErrorResult.builder()</span>
<span class="nc" id="L413">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L414">            .enhancedMessage(message)</span>
<span class="nc" id="L415">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L416">            .suggestion(suggestion)</span>
<span class="nc" id="L417">            .metadata(metadata)</span>
<span class="nc" id="L418">            .build();</span>
    }

    /**
     * Creates a generic structural error result.
     */
    private ErrorResult createGenericStructuralError(ErrorContext context, String structureType) {
<span class="nc" id="L425">        String message = I18n.l(&quot;analyzer.structural.message.generic&quot;, structureType, getOffendingText(context));</span>
<span class="nc" id="L426">        String suggestion = I18n.l(&quot;analyzer.structural.suggestion.generic&quot;, structureType);</span>

<span class="nc" id="L428">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L429">        metadata.put(&quot;errorType&quot;, &quot;structural_generic&quot;);</span>
<span class="nc" id="L430">        metadata.put(&quot;structureType&quot;, structureType);</span>
<span class="nc" id="L431">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L433">        return ErrorResult.builder()</span>
<span class="nc" id="L434">            .ruleId(CaplErrorType.PARSER_STRUCTURAL_ERROR.getErrorCode())</span>
<span class="nc" id="L435">            .enhancedMessage(message)</span>
<span class="nc" id="L436">            .severity(CaplErrorType.PARSER_STRUCTURAL_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L437">            .suggestion(suggestion)</span>
<span class="nc" id="L438">            .metadata(metadata)</span>
<span class="nc" id="L439">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>