<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OtherCaplObjectTypeErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">OtherCaplObjectTypeErrorAnalyzer.java</span></div><h1>OtherCaplObjectTypeErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for other CAPL object type declaration errors.
 * Handles various CAPL object types from caplObjectType rule (CAPL.g4 line 1782) including:
 * - FlexRay types: frFrame, frError, frPDU
 * - Diagnostic types: diagRequest, diagResponse
 * - PDU types: pdu
 * - System variable types: sysvar_int*, sysvar_float*, etc.
 * - Database types: dbMsg*, dbNode*, etc.
 * - SOME/IP types: serviceSignal*, etc.
 * - Other types: a429Word, afdxMessage, j1587*, pg, most*, ethernet*, ip_*
 */
public class OtherCaplObjectTypeErrorAnalyzer extends ParserErrorAnalyzer {

    // FlexRay type keywords (frTypeSpecifier - line 1174)
<span class="nc" id="L31">    private static final Set&lt;String&gt; FLEXRAY_TYPES = Set.of(</span>
        &quot;frFrame&quot;, &quot;frError&quot;, &quot;frPDU&quot;
    );

    // Diagnostic type keywords (diagTypeSpecifier - line 1277)
<span class="nc" id="L36">    private static final Set&lt;String&gt; DIAG_TYPES = Set.of(</span>
        &quot;diagRequest&quot;, &quot;diagResponse&quot;
    );

    // System variable type keywords (sysvarTypeSpecifier - line 1813)
<span class="nc" id="L41">    private static final Set&lt;String&gt; SYSVAR_TYPES = Set.of(</span>
        &quot;sysvar_int&quot;, &quot;sysvar_float&quot;, &quot;sysvar_string&quot;, &quot;sysvar_data&quot;,
        &quot;sysvar_int_array&quot;, &quot;sysvar_float_array&quot;, &quot;sysvar_long&quot;, &quot;sysvar_longlong&quot;
    );

    // Database type keywords (dbtypeSpecifier - line 1836)
<span class="nc" id="L47">    private static final Set&lt;String&gt; DB_TYPES = Set.of(</span>
        &quot;dbMsg&quot;, &quot;dbNode&quot;, &quot;dbPdu&quot;, &quot;dbFrFrame&quot;, &quot;dbFrPdu&quot;
    );

    // SOME/IP type keywords (someipTypeSpecifier - line 1856)
<span class="nc" id="L52">    private static final Set&lt;String&gt; SOMEIP_TYPES = Set.of(</span>
        &quot;serviceSignal&quot;, &quot;serviceSignalData&quot;, &quot;serviceSignalNumber&quot;, &quot;serviceSignalString&quot;
    );

    // Other CAPL object types
<span class="nc" id="L57">    private static final Set&lt;String&gt; OTHER_TYPES = Set.of(</span>
        &quot;pdu&quot;, &quot;a429Word&quot;, &quot;afdxMessage&quot;, &quot;j1587ErrorMessage&quot;, &quot;j1587Message&quot;, &quot;j1587Param&quot;,
        &quot;pg&quot;, &quot;mostMessage&quot;, &quot;mostAmsMessage&quot;, &quot;mostRawMessage&quot;,
        &quot;ethernetFrame&quot;, &quot;ethernetPacket&quot;, &quot;ethernetErrorPacket&quot;,
        &quot;ip_address&quot;, &quot;ip_endpoint&quot;
    );

    // All supported types
<span class="nc" id="L65">    private static final Set&lt;String&gt; ALL_SUPPORTED_TYPES = Set.of(</span>
        // FlexRay
        &quot;frFrame&quot;, &quot;frError&quot;, &quot;frPDU&quot;,
        // Diagnostic
        &quot;diagRequest&quot;, &quot;diagResponse&quot;,
        // System variables
        &quot;sysvar_int&quot;, &quot;sysvar_float&quot;, &quot;sysvar_string&quot;, &quot;sysvar_data&quot;,
        &quot;sysvar_int_array&quot;, &quot;sysvar_float_array&quot;, &quot;sysvar_long&quot;, &quot;sysvar_longlong&quot;,
        // Database
        &quot;dbMsg&quot;, &quot;dbNode&quot;, &quot;dbPdu&quot;, &quot;dbFrFrame&quot;, &quot;dbFrPdu&quot;,
        // SOME/IP
        &quot;serviceSignal&quot;, &quot;serviceSignalData&quot;, &quot;serviceSignalNumber&quot;, &quot;serviceSignalString&quot;,
        // Others
        &quot;pdu&quot;, &quot;a429Word&quot;, &quot;afdxMessage&quot;, &quot;j1587ErrorMessage&quot;, &quot;j1587Message&quot;, &quot;j1587Param&quot;,
        &quot;pg&quot;, &quot;mostMessage&quot;, &quot;mostAmsMessage&quot;, &quot;mostRawMessage&quot;,
        &quot;ethernetFrame&quot;, &quot;ethernetPacket&quot;, &quot;ethernetErrorPacket&quot;,
        &quot;ip_address&quot;, &quot;ip_endpoint&quot;
    );

    // Patterns for validation
<span class="nc" id="L85">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L86">    private static final Pattern HEX_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L87">    private static final Pattern DECIMAL_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>
<span class="nc" id="L88">    private static final Pattern QUALIFIED_IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public OtherCaplObjectTypeErrorAnalyzer() {
<span class="nc" id="L91">        super(&quot;OtherCaplObjectTypeErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 177,</span>
<span class="nc" id="L92">              Set.of(CaplParseContext.OTHER_CAPL_OBJECT_TYPE));</span>
<span class="nc" id="L93">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L97">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L99">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L102" title="All 2 branches missed.">        return parseContext == CaplParseContext.OTHER_CAPL_OBJECT_TYPE ||</span>
<span class="nc bnc" id="L103" title="All 2 branches missed.">               isOtherCaplObjectTypeError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L108">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L113">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L119">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L120">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L121">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L124">            String offendingText = getOffendingText(context);</span>
<span class="nc" id="L125">            String objectType = determineObjectType(precedingTokens);</span>

            // Dispatch to specific type analyzers
<span class="nc bnc" id="L128" title="All 2 branches missed.">            if (FLEXRAY_TYPES.contains(objectType)) {</span>
<span class="nc" id="L129">                return analyzeFlexRayTypeError(context, precedingTokens, offendingText, objectType);</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">            } else if (DIAG_TYPES.contains(objectType)) {</span>
<span class="nc" id="L131">                return analyzeDiagnosticTypeError(context, precedingTokens, offendingText, objectType);</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">            } else if (objectType.equals(&quot;pdu&quot;)) {</span>
<span class="nc" id="L133">                return analyzePduTypeError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">            } else if (SYSVAR_TYPES.contains(objectType)) {</span>
<span class="nc" id="L135">                return analyzeSysvarTypeError(context, precedingTokens, offendingText, objectType);</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">            } else if (DB_TYPES.contains(objectType)) {</span>
<span class="nc" id="L137">                return analyzeDatabaseTypeError(context, precedingTokens, offendingText, objectType);</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">            } else if (SOMEIP_TYPES.contains(objectType)) {</span>
<span class="nc" id="L139">                return analyzeSomeIpTypeError(context, precedingTokens, offendingText, objectType);</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">            } else if (OTHER_TYPES.contains(objectType)) {</span>
<span class="nc" id="L141">                return analyzeOtherTypeError(context, precedingTokens, offendingText, objectType);</span>
            }

<span class="nc" id="L144">            return createGenericObjectTypeError(context, objectType);</span>

<span class="nc" id="L146">        } catch (Exception e) {</span>
<span class="nc" id="L147">            return createGenericObjectTypeError(context, &quot;unknown&quot;);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is an other CAPL object type error.
     */
    private boolean isOtherCaplObjectTypeError(ErrorContext context) {
<span class="nc" id="L157">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L158">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L159">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L162">        return hasOtherCaplObjectTypeKeyword(precedingTokens);</span>
    }

    /**
     * Determines the object type from tokens.
     */
    private String determineObjectType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L169" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L170">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L173" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L174">            String text = token.getText();</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">            if (ALL_SUPPORTED_TYPES.contains(text)) {</span>
<span class="nc" id="L176">                return text;</span>
            }
<span class="nc" id="L178">        }</span>
<span class="nc" id="L179">        return &quot;unknown&quot;;</span>
    }

    /**
     * Checks if tokens contain an other CAPL object type keyword.
     */
    private boolean hasOtherCaplObjectTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L186" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L187">            return false;</span>
        }

<span class="nc bnc" id="L190" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">            if (ALL_SUPPORTED_TYPES.contains(token.getText())) {</span>
<span class="nc" id="L192">                return true;</span>
            }
<span class="nc" id="L194">        }</span>
<span class="nc" id="L195">        return false;</span>
    }

    /**
     * Analyzes FlexRay type errors.
     */
    private ErrorResult analyzeFlexRayTypeError(ErrorContext context, List&lt;Token&gt; tokens, 
                                               String offendingText, String objectType) {
        // Check for missing identifier or invalid format
<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (isMissingIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L205">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_FLEXRAY_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.flexray.missingIdentifier&quot;,
                &quot;analyzer.otherCaplObject.flexray.missingIdentifierSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc bnc" id="L213" title="All 2 branches missed.">        if (isInvalidFlexRayFormat(offendingText)) {</span>
<span class="nc" id="L214">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_FLEXRAY_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.flexray.invalidFormat&quot;,
                &quot;analyzer.otherCaplObject.flexray.invalidFormatSuggestion&quot;,
                context, offendingText
            );
        }

<span class="nc" id="L222">        return createGenericObjectTypeError(context, objectType);</span>
    }

    /**
     * Analyzes diagnostic type errors.
     */
    private ErrorResult analyzeDiagnosticTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                                  String offendingText, String objectType) {
<span class="nc bnc" id="L230" title="All 2 branches missed.">        if (isMissingIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L231">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_DIAGNOSTIC_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.diagnostic.missingIdentifier&quot;,
                &quot;analyzer.otherCaplObject.diagnostic.missingIdentifierSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L239">        return createGenericObjectTypeError(context, objectType);</span>
    }

    /**
     * Analyzes PDU type errors.
     */
    private ErrorResult analyzePduTypeError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L246" title="All 2 branches missed.">        if (isMissingIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L247">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_PDU_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.pdu.missingIdentifier&quot;,
                &quot;analyzer.otherCaplObject.pdu.missingIdentifierSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L255">        return createGenericObjectTypeError(context, &quot;pdu&quot;);</span>
    }

    /**
     * Analyzes system variable type errors.
     */
    private ErrorResult analyzeSysvarTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                              String offendingText, String objectType) {
<span class="nc bnc" id="L263" title="All 2 branches missed.">        if (isMissingStar(tokens, offendingText)) {</span>
<span class="nc" id="L264">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_SYSVAR_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.sysvar.missingStar&quot;,
                &quot;analyzer.otherCaplObject.sysvar.missingStarSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L272">        return createGenericObjectTypeError(context, objectType);</span>
    }

    /**
     * Analyzes database type errors.
     */
    private ErrorResult analyzeDatabaseTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                                String offendingText, String objectType) {
<span class="nc bnc" id="L280" title="All 2 branches missed.">        if (isMissingStar(tokens, offendingText)) {</span>
<span class="nc" id="L281">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_DATABASE_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.database.missingStar&quot;,
                &quot;analyzer.otherCaplObject.database.missingStarSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L289">        return createGenericObjectTypeError(context, objectType);</span>
    }

    /**
     * Analyzes SOME/IP type errors.
     */
    private ErrorResult analyzeSomeIpTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                              String offendingText, String objectType) {
<span class="nc bnc" id="L297" title="All 2 branches missed.">        if (isMissingStar(tokens, offendingText)) {</span>
<span class="nc" id="L298">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_SOMEIP_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.someip.missingStar&quot;,
                &quot;analyzer.otherCaplObject.someip.missingStarSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L306">        return createGenericObjectTypeError(context, objectType);</span>
    }

    /**
     * Analyzes other type errors.
     */
    private ErrorResult analyzeOtherTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                             String offendingText, String objectType) {
<span class="nc bnc" id="L314" title="All 2 branches missed.">        if (isMissingIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L315">            return createObjectTypeError(</span>
                CaplErrorType.PARSER_OTHER_OBJECT_TYPE_ERROR,
                &quot;analyzer.otherCaplObject.other.missingIdentifier&quot;,
                &quot;analyzer.otherCaplObject.other.missingIdentifierSuggestion&quot;,
                context, objectType
            );
        }

<span class="nc" id="L323">        return createGenericObjectTypeError(context, objectType);</span>
    }

    // ========== Validation Methods ==========

    private boolean isMissingIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L329" title="All 2 branches missed.">        return hasOtherCaplObjectTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L330" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    private boolean isMissingStar(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L334" title="All 2 branches missed.">        return hasOtherCaplObjectTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L335" title="All 2 branches missed.">               !offendingText.equals(&quot;*&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L336" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isInvalidFlexRayFormat(String offendingText) {
        // FlexRay can have (channel,slot,cycle) format
<span class="nc bnc" id="L341" title="All 4 branches missed.">        return offendingText.startsWith(&quot;(&quot;) &amp;&amp; !offendingText.matches(&quot;\\(\\d+,\\d+,\\d+\\)&quot;);</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L345" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L346">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L348" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L349">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createObjectTypeError(CaplErrorType errorType, String messageKey,
                                            String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L354">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L355">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L357">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L358">        metadata.put(&quot;errorType&quot;, &quot;otherCaplObjectType&quot;);</span>
<span class="nc" id="L359">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L360">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L362">        return ErrorResult.builder()</span>
<span class="nc" id="L363">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L364">            .enhancedMessage(message)</span>
<span class="nc" id="L365">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L366">            .suggestion(suggestion)</span>
<span class="nc" id="L367">            .metadata(metadata)</span>
<span class="nc" id="L368">            .build();</span>
    }

    private ErrorResult createGenericObjectTypeError(ErrorContext context, String objectType) {
<span class="nc" id="L372">        String message = I18n.l(&quot;analyzer.otherCaplObject.generic&quot;, objectType, getOffendingText(context));</span>
<span class="nc" id="L373">        String suggestion = I18n.l(&quot;analyzer.otherCaplObject.genericSuggestion&quot;, objectType);</span>

<span class="nc" id="L375">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L376">        metadata.put(&quot;errorType&quot;, &quot;otherCaplObjectType_generic&quot;);</span>
<span class="nc" id="L377">        metadata.put(&quot;objectType&quot;, objectType);</span>
<span class="nc" id="L378">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L380">        return ErrorResult.builder()</span>
<span class="nc" id="L381">            .ruleId(CaplErrorType.PARSER_OTHER_OBJECT_TYPE_ERROR.getErrorCode())</span>
<span class="nc" id="L382">            .enhancedMessage(message)</span>
<span class="nc" id="L383">            .severity(CaplErrorType.PARSER_OTHER_OBJECT_TYPE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L384">            .suggestion(suggestion)</span>
<span class="nc" id="L385">            .metadata(metadata)</span>
<span class="nc" id="L386">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>