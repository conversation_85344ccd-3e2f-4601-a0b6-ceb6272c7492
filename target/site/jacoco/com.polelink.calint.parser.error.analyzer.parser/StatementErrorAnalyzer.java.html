<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StatementErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">StatementErrorAnalyzer.java</span></div><h1>StatementErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for statement errors in CAPL programs.
 * Handles statement rule (CAPL.g4 line 234) including:
 * - Compound statements: { ... }
 * - Selection statements: if/else, switch/case
 * - Iteration statements: for, while, do-while, foreach
 * - Jump statements: return, break, continue
 * - Expression statements: expression;
 */
public class StatementErrorAnalyzer extends ParserErrorAnalyzer {

    // Statement keywords
<span class="nc" id="L29">    private static final Set&lt;String&gt; COMPOUND_KEYWORDS = Set.of(&quot;{&quot;, &quot;}&quot;);</span>
<span class="nc" id="L30">    private static final Set&lt;String&gt; SELECTION_KEYWORDS = Set.of(&quot;if&quot;, &quot;else&quot;, &quot;switch&quot;, &quot;case&quot;, &quot;default&quot;);</span>
<span class="nc" id="L31">    private static final Set&lt;String&gt; ITERATION_KEYWORDS = Set.of(&quot;for&quot;, &quot;while&quot;, &quot;do&quot;, &quot;foreach&quot;);</span>
<span class="nc" id="L32">    private static final Set&lt;String&gt; JUMP_KEYWORDS = Set.of(&quot;return&quot;, &quot;break&quot;, &quot;continue&quot;);</span>

    // Control flow keywords
<span class="nc" id="L35">    private static final Set&lt;String&gt; CONTROL_FLOW_KEYWORDS = Set.of(</span>
        &quot;if&quot;, &quot;else&quot;, &quot;switch&quot;, &quot;case&quot;, &quot;default&quot;,
        &quot;for&quot;, &quot;while&quot;, &quot;do&quot;, &quot;foreach&quot;,
        &quot;return&quot;, &quot;break&quot;, &quot;continue&quot;
    );

    // Patterns for validation
<span class="nc" id="L42">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public StatementErrorAnalyzer() {
<span class="nc" id="L45">        super(&quot;StatementErrorAnalyzer&quot;, AnalyzerType.PARSER_EXPRESSION, 190,</span>
<span class="nc" id="L46">              Set.of(CaplParseContext.STATEMENT, CaplParseContext.COMPOUND_STATEMENT,</span>
                     CaplParseContext.SELECTION_STATEMENT, CaplParseContext.ITERATION_STATEMENT,
                     CaplParseContext.JUMP_STATEMENT));
<span class="nc" id="L49">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L53">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L55">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L58" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">               isStatementError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L64">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L69">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L75">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L76">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L77">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L80">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific statement analyzers
<span class="nc bnc" id="L83" title="All 2 branches missed.">            if (isCompoundStatementError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L84">                return analyzeCompoundStatementError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">            } else if (isSelectionStatementError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L86">                return analyzeSelectionStatementError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">            } else if (isIterationStatementError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L88">                return analyzeIterationStatementError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">            } else if (isJumpStatementError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L90">                return analyzeJumpStatementError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">            } else if (isExpressionStatementError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L92">                return analyzeExpressionStatementError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L95">            return createGenericStatementError(context);</span>

<span class="nc" id="L97">        } catch (Exception e) {</span>
<span class="nc" id="L98">            return createGenericStatementError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a statement error.
     */
    private boolean isStatementError(ErrorContext context) {
<span class="nc" id="L108">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L109">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L110">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L113" title="All 2 branches missed.">        return hasControlFlowKeyword(precedingTokens) || </span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">               hasCompoundStatementStructure(precedingTokens);</span>
    }

    /**
     * Analyzes compound statement errors.
     */
    private ErrorResult analyzeCompoundStatementError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing opening brace
<span class="nc bnc" id="L122" title="All 2 branches missed.">        if (isMissingOpeningBrace(tokens, offendingText)) {</span>
<span class="nc" id="L123">            return createStatementError(</span>
                CaplErrorType.PARSER_COMPOUND_STATEMENT_ERROR,
                &quot;analyzer.statement.compound.missingOpeningBrace&quot;,
                &quot;analyzer.statement.compound.missingOpeningBraceSuggestion&quot;,
                context
            );
        }

        // Check for missing closing brace
<span class="nc bnc" id="L132" title="All 2 branches missed.">        if (isMissingClosingBrace(tokens, offendingText)) {</span>
<span class="nc" id="L133">            return createStatementError(</span>
                CaplErrorType.PARSER_COMPOUND_STATEMENT_ERROR,
                &quot;analyzer.statement.compound.missingClosingBrace&quot;,
                &quot;analyzer.statement.compound.missingClosingBraceSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L141">        return createGenericStatementError(context);</span>
    }

    /**
     * Analyzes selection statement errors.
     */
    private ErrorResult analyzeSelectionStatementError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing condition in if statement
<span class="nc bnc" id="L149" title="All 2 branches missed.">        if (isMissingIfCondition(tokens, offendingText)) {</span>
<span class="nc" id="L150">            return createStatementError(</span>
                CaplErrorType.PARSER_SELECTION_STATEMENT_ERROR,
                &quot;analyzer.statement.selection.missingIfCondition&quot;,
                &quot;analyzer.statement.selection.missingIfConditionSuggestion&quot;,
                context
            );
        }

        // Check for missing switch expression
<span class="nc bnc" id="L159" title="All 2 branches missed.">        if (isMissingSwitchExpression(tokens, offendingText)) {</span>
<span class="nc" id="L160">            return createStatementError(</span>
                CaplErrorType.PARSER_SELECTION_STATEMENT_ERROR,
                &quot;analyzer.statement.selection.missingSwitchExpression&quot;,
                &quot;analyzer.statement.selection.missingSwitchExpressionSuggestion&quot;,
                context
            );
        }

        // Check for missing case value
<span class="nc bnc" id="L169" title="All 2 branches missed.">        if (isMissingCaseValue(tokens, offendingText)) {</span>
<span class="nc" id="L170">            return createStatementError(</span>
                CaplErrorType.PARSER_SELECTION_STATEMENT_ERROR,
                &quot;analyzer.statement.selection.missingCaseValue&quot;,
                &quot;analyzer.statement.selection.missingCaseValueSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L178">        return createGenericStatementError(context);</span>
    }

    /**
     * Analyzes iteration statement errors.
     */
    private ErrorResult analyzeIterationStatementError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing for loop condition
<span class="nc bnc" id="L186" title="All 2 branches missed.">        if (isMissingForCondition(tokens, offendingText)) {</span>
<span class="nc" id="L187">            return createStatementError(</span>
                CaplErrorType.PARSER_ITERATION_STATEMENT_ERROR,
                &quot;analyzer.statement.iteration.missingForCondition&quot;,
                &quot;analyzer.statement.iteration.missingForConditionSuggestion&quot;,
                context
            );
        }

        // Check for missing while condition
<span class="nc bnc" id="L196" title="All 2 branches missed.">        if (isMissingWhileCondition(tokens, offendingText)) {</span>
<span class="nc" id="L197">            return createStatementError(</span>
                CaplErrorType.PARSER_ITERATION_STATEMENT_ERROR,
                &quot;analyzer.statement.iteration.missingWhileCondition&quot;,
                &quot;analyzer.statement.iteration.missingWhileConditionSuggestion&quot;,
                context
            );
        }

        // Check for missing foreach initializer
<span class="nc bnc" id="L206" title="All 2 branches missed.">        if (isMissingForeachInitializer(tokens, offendingText)) {</span>
<span class="nc" id="L207">            return createStatementError(</span>
                CaplErrorType.PARSER_ITERATION_STATEMENT_ERROR,
                &quot;analyzer.statement.iteration.missingForeachInitializer&quot;,
                &quot;analyzer.statement.iteration.missingForeachInitializerSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L215">        return createGenericStatementError(context);</span>
    }

    /**
     * Analyzes jump statement errors.
     */
    private ErrorResult analyzeJumpStatementError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing semicolon
<span class="nc bnc" id="L223" title="All 2 branches missed.">        if (isMissingSemicolon(tokens, offendingText)) {</span>
<span class="nc" id="L224">            return createStatementError(</span>
                CaplErrorType.PARSER_JUMP_STATEMENT_ERROR,
                &quot;analyzer.statement.jump.missingSemicolon&quot;,
                &quot;analyzer.statement.jump.missingSemicolonSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L232">        return createGenericStatementError(context);</span>
    }

    /**
     * Analyzes expression statement errors.
     */
    private ErrorResult analyzeExpressionStatementError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing semicolon
<span class="nc bnc" id="L240" title="All 2 branches missed.">        if (isMissingSemicolon(tokens, offendingText)) {</span>
<span class="nc" id="L241">            return createStatementError(</span>
                CaplErrorType.PARSER_STATEMENT_ERROR,
                &quot;analyzer.statement.expression.missingSemicolon&quot;,
                &quot;analyzer.statement.expression.missingSemicolonSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L249">        return createGenericStatementError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isCompoundStatementError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L255" title="All 2 branches missed.">        return hasCompoundStatementStructure(tokens) || </span>
<span class="nc bnc" id="L256" title="All 4 branches missed.">               offendingText.equals(&quot;{&quot;) || offendingText.equals(&quot;}&quot;);</span>
    }

    private boolean isSelectionStatementError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L260">        return hasSelectionKeyword(tokens);</span>
    }

    private boolean isIterationStatementError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L264">        return hasIterationKeyword(tokens);</span>
    }

    private boolean isJumpStatementError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L268">        return hasJumpKeyword(tokens);</span>
    }

    private boolean isExpressionStatementError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L272" title="All 2 branches missed.">        return !hasControlFlowKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L273" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingOpeningBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L277" title="All 2 branches missed.">        return hasControlFlowKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">               !offendingText.equals(&quot;{&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L279" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || IDENTIFIER_PATTERN.matcher(offendingText).matches());</span>
    }

    private boolean isMissingClosingBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L283" title="All 4 branches missed.">        return hasOpeningBrace(tokens) &amp;&amp; offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isMissingIfCondition(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L287" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;if&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L288" title="All 4 branches missed.">               (offendingText.equals(&quot;)&quot;) || offendingText.equals(&quot;{&quot;));</span>
    }

    private boolean isMissingSwitchExpression(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L292" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;switch&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L293" title="All 4 branches missed.">               (offendingText.equals(&quot;)&quot;) || offendingText.equals(&quot;{&quot;));</span>
    }

    private boolean isMissingCaseValue(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L297" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;case&quot;) &amp;&amp; offendingText.equals(&quot;:&quot;);</span>
    }

    private boolean isMissingForCondition(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L301" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;for&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L302" title="All 4 branches missed.">               (offendingText.equals(&quot;)&quot;) || offendingText.equals(&quot;{&quot;));</span>
    }

    private boolean isMissingWhileCondition(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L306" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;while&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L307" title="All 4 branches missed.">               (offendingText.equals(&quot;)&quot;) || offendingText.equals(&quot;{&quot;));</span>
    }

    private boolean isMissingForeachInitializer(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L311" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;for&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L312" title="All 2 branches missed.">               offendingText.equals(&quot;:&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L313" title="All 2 branches missed.">               !hasValidForeachInitializer(tokens);</span>
    }

    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L317" title="All 2 branches missed.">        return hasJumpKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L318" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    // ========== Utility Methods ==========

    private boolean hasControlFlowKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L324">        return hasAnyKeyword(tokens, CONTROL_FLOW_KEYWORDS);</span>
    }

    private boolean hasCompoundStatementStructure(List&lt;Token&gt; tokens) {
<span class="nc" id="L328">        return hasAnyKeyword(tokens, COMPOUND_KEYWORDS);</span>
    }

    private boolean hasSelectionKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L332">        return hasAnyKeyword(tokens, SELECTION_KEYWORDS);</span>
    }

    private boolean hasIterationKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L336">        return hasAnyKeyword(tokens, ITERATION_KEYWORDS);</span>
    }

    private boolean hasJumpKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L340">        return hasAnyKeyword(tokens, JUMP_KEYWORDS);</span>
    }

    private boolean hasOpeningBrace(List&lt;Token&gt; tokens) {
<span class="nc" id="L344">        return hasKeyword(tokens, &quot;{&quot;);</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L348" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L349">            return false;</span>
        }

<span class="nc bnc" id="L352" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L353" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L354">                return true;</span>
            }
<span class="nc" id="L356">        }</span>
<span class="nc" id="L357">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L361" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L362">            return false;</span>
        }

<span class="nc bnc" id="L365" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L366" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L367">                return true;</span>
            }
<span class="nc" id="L369">        }</span>
<span class="nc" id="L370">        return false;</span>
    }

    private boolean hasValidForeachInitializer(List&lt;Token&gt; tokens) {
        // Simple check for valid foreach initializer pattern
<span class="nc bnc" id="L375" title="All 4 branches missed.">        return tokens != null &amp;&amp; tokens.size() &gt;= 2;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L379" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L380">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L382" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L383">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createStatementError(CaplErrorType errorType, String messageKey,
                                           String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L388">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L389">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L391">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L392">        metadata.put(&quot;errorType&quot;, &quot;statement&quot;);</span>
<span class="nc" id="L393">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L394">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L396">        return ErrorResult.builder()</span>
<span class="nc" id="L397">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L398">            .enhancedMessage(message)</span>
<span class="nc" id="L399">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L400">            .suggestion(suggestion)</span>
<span class="nc" id="L401">            .metadata(metadata)</span>
<span class="nc" id="L402">            .build();</span>
    }

    private ErrorResult createGenericStatementError(ErrorContext context) {
<span class="nc" id="L406">        String message = I18n.l(&quot;analyzer.statement.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L407">        String suggestion = I18n.l(&quot;analyzer.statement.genericSuggestion&quot;);</span>

<span class="nc" id="L409">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L410">        metadata.put(&quot;errorType&quot;, &quot;statement_generic&quot;);</span>
<span class="nc" id="L411">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L413">        return ErrorResult.builder()</span>
<span class="nc" id="L414">            .ruleId(CaplErrorType.PARSER_STATEMENT_ERROR.getErrorCode())</span>
<span class="nc" id="L415">            .enhancedMessage(message)</span>
<span class="nc" id="L416">            .severity(CaplErrorType.PARSER_STATEMENT_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L417">            .suggestion(suggestion)</span>
<span class="nc" id="L418">            .metadata(metadata)</span>
<span class="nc" id="L419">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>