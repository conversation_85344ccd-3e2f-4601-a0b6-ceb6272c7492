<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DeclarationErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">DeclarationErrorAnalyzer.java</span></div><h1>DeclarationErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Analyzer for declaration errors in CAPL programs.
 * Handles variable declarations, type declarations, and function declarations.
 */
public class DeclarationErrorAnalyzer extends ParserErrorAnalyzer {

    // CAPL type keywords
<span class="nc" id="L24">    private static final Set&lt;String&gt; BUILTIN_TYPES = Set.of(</span>
        &quot;char&quot;, &quot;int&quot;, &quot;float&quot;, &quot;double&quot;, &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;,
        &quot;void&quot;, &quot;timer&quot;, &quot;msTimer&quot;
    );

    // CAPL type modifiers
<span class="nc" id="L30">    private static final Set&lt;String&gt; TYPE_MODIFIERS = Set.of(</span>
        &quot;const&quot;, &quot;stack&quot;  // Note: CAPL uses 'stack' instead of 'static'
    );

    // CAPL object types
<span class="nc" id="L35">    private static final Set&lt;String&gt; CAPL_OBJECT_TYPES = Set.of(</span>
        &quot;message&quot;, &quot;signal&quot;, &quot;frFrame&quot;, &quot;frError&quot;, &quot;frPDU&quot;,
        &quot;diagRequest&quot;, &quot;diagResponse&quot;, &quot;pdu&quot;
    );

    // Patterns for validation
<span class="nc" id="L41">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L42">    private static final Pattern HEX_NUMBER_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L43">    private static final Pattern DECIMAL_NUMBER_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>

    public DeclarationErrorAnalyzer() {
<span class="nc" id="L46">        super(&quot;DeclarationErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 160,</span>
<span class="nc" id="L47">              Set.of(CaplParseContext.CAPL_VAR_DECLARATION,</span>
                     CaplParseContext.TYPE_SPECIFIER,
                     CaplParseContext.FUNCTION_DECLARATION,
                     CaplParseContext.DECLARATION,
                     CaplParseContext.DECLARATION_SPECIFIERS,
                     CaplParseContext.BUILTIN_TYPE_SPECIFIER,
                     CaplParseContext.CAPL_OBJECT_TYPE));
<span class="nc" id="L54">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
        // This analyzer can handle any declaration-related parser error
<span class="nc" id="L59">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L61">            parseContext = detectParseContext(context);</span>
        }

<span class="nc bnc" id="L64" title="All 2 branches missed.">        return parseContext != null &amp;&amp;</span>
<span class="nc bnc" id="L65" title="All 4 branches missed.">               (parseContext.isDeclarationContext() || getSupportedContexts().contains(parseContext));</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
<span class="nc" id="L70">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L72">            parseContext = detectParseContext(context);</span>
        }

        try {
            // Analyze based on specific declaration context
<span class="nc bnc" id="L77" title="All 6 branches missed.">            switch (parseContext) {</span>
                case CAPL_VAR_DECLARATION:
<span class="nc" id="L79">                    return analyzeVariableDeclarationError(context);</span>
                case TYPE_SPECIFIER:
                case BUILTIN_TYPE_SPECIFIER:
<span class="nc" id="L82">                    return analyzeTypeSpecifierError(context);</span>
                case FUNCTION_DECLARATION:
<span class="nc" id="L84">                    return analyzeFunctionDeclarationError(context);</span>
                case DECLARATION:
                case DECLARATION_SPECIFIERS:
<span class="nc" id="L87">                    return analyzeGeneralDeclarationError(context);</span>
                case CAPL_OBJECT_TYPE:
<span class="nc" id="L89">                    return analyzeCaplObjectTypeError(context);</span>
                default:
<span class="nc" id="L91">                    return analyzeGenericDeclarationError(context);</span>
            }
<span class="nc" id="L93">        } catch (Exception e) {</span>
<span class="nc" id="L94">            return createGenericDeclarationError(context, &quot;declaration analysis&quot;);</span>
        }
    }

    /**
     * Analyzes variable declaration errors.
     */
    private ErrorResult analyzeVariableDeclarationError(ErrorContext context) {
<span class="nc" id="L102">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L103">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L104">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L107">        String offendingText = getOffendingText(context);</span>

        // Check for missing type specifier
<span class="nc bnc" id="L110" title="All 2 branches missed.">        if (isMissingTypeSpecifier(precedingTokens, offendingText)) {</span>
<span class="nc" id="L111">            return createDeclarationError(</span>
                CaplErrorType.PARSER_DECLARATION_ERROR,
                &quot;analyzer.declaration.message.missingTypeSpecifier&quot;,
                &quot;analyzer.declaration.suggestion.missingTypeSpecifier&quot;,
                context
            );
        }

        // Check for invalid variable name
<span class="nc bnc" id="L120" title="All 2 branches missed.">        if (isInvalidVariableName(offendingText)) {</span>
<span class="nc" id="L121">            return createDeclarationError(</span>
                CaplErrorType.PARSER_DECLARATION_ERROR,
                &quot;analyzer.declaration.message.invalidVariableName&quot;,
                &quot;analyzer.declaration.suggestion.invalidVariableName&quot;,
                context,
                offendingText
            );
        }

        // Check for missing semicolon
<span class="nc bnc" id="L131" title="All 2 branches missed.">        if (isMissingSemicolon(precedingTokens, offendingText)) {</span>
<span class="nc" id="L132">            return createDeclarationError(</span>
                CaplErrorType.PARSER_DECLARATION_ERROR,
                &quot;analyzer.declaration.message.missingSemicolon&quot;,
                &quot;analyzer.declaration.suggestion.missingSemicolon&quot;,
                context
            );
        }

<span class="nc" id="L140">        return createGenericDeclarationError(context, &quot;variable declaration&quot;);</span>
    }

    /**
     * Analyzes type specifier errors.
     */
    private ErrorResult analyzeTypeSpecifierError(ErrorContext context) {
<span class="nc" id="L147">        String offendingText = getOffendingText(context);</span>

        // Check for invalid builtin type
<span class="nc bnc" id="L150" title="All 2 branches missed.">        if (isInvalidBuiltinType(offendingText)) {</span>
<span class="nc" id="L151">            return createDeclarationError(</span>
                CaplErrorType.PARSER_TYPE_ERROR,
                &quot;analyzer.declaration.message.invalidBuiltinType&quot;,
                &quot;analyzer.declaration.suggestion.invalidBuiltinType&quot;,
                context,
                offendingText,
<span class="nc" id="L157">                String.join(&quot;, &quot;, BUILTIN_TYPES)</span>
            );
        }

        // Check for invalid type modifier
<span class="nc bnc" id="L162" title="All 2 branches missed.">        if (isInvalidTypeModifier(offendingText)) {</span>
<span class="nc" id="L163">            return createDeclarationError(</span>
                CaplErrorType.PARSER_TYPE_ERROR,
                &quot;analyzer.declaration.message.invalidTypeModifier&quot;,
                &quot;analyzer.declaration.suggestion.invalidTypeModifier&quot;,
                context,
                offendingText,
<span class="nc" id="L169">                String.join(&quot;, &quot;, TYPE_MODIFIERS)</span>
            );
        }

<span class="nc" id="L173">        return createGenericDeclarationError(context, &quot;type specifier&quot;);</span>
    }

    /**
     * Analyzes function declaration errors.
     */
    private ErrorResult analyzeFunctionDeclarationError(ErrorContext context) {
<span class="nc" id="L180">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L181">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L182">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L185">        String offendingText = getOffendingText(context);</span>

        // Check for missing function name
<span class="nc bnc" id="L188" title="All 2 branches missed.">        if (isMissingFunctionName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L189">            return createDeclarationError(</span>
                CaplErrorType.PARSER_FUNCTION_ERROR,
                &quot;analyzer.declaration.message.missingFunctionName&quot;,
                &quot;analyzer.declaration.suggestion.missingFunctionName&quot;,
                context
            );
        }

        // Check for missing parentheses
<span class="nc bnc" id="L198" title="All 2 branches missed.">        if (isMissingParentheses(precedingTokens, offendingText)) {</span>
<span class="nc" id="L199">            return createDeclarationError(</span>
                CaplErrorType.PARSER_FUNCTION_ERROR,
                &quot;analyzer.declaration.message.missingParentheses&quot;,
                &quot;analyzer.declaration.suggestion.missingParentheses&quot;,
                context
            );
        }

        // Check for invalid parameter syntax
<span class="nc bnc" id="L208" title="All 2 branches missed.">        if (isInvalidParameterSyntax(precedingTokens, offendingText)) {</span>
<span class="nc" id="L209">            return createDeclarationError(</span>
                CaplErrorType.PARSER_FUNCTION_ERROR,
                &quot;analyzer.declaration.message.invalidParameterSyntax&quot;,
                &quot;analyzer.declaration.suggestion.invalidParameterSyntax&quot;,
                context
            );
        }

<span class="nc" id="L217">        return createGenericDeclarationError(context, &quot;function declaration&quot;);</span>
    }

    /**
     * Analyzes general declaration errors.
     */
    private ErrorResult analyzeGeneralDeclarationError(ErrorContext context) {
<span class="nc" id="L224">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L225">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L226">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L229">        String offendingText = getOffendingText(context);</span>

        // Check for incomplete declaration
<span class="nc bnc" id="L232" title="All 2 branches missed.">        if (isIncompleteDeclaration(precedingTokens, offendingText)) {</span>
<span class="nc" id="L233">            return createDeclarationError(</span>
                CaplErrorType.PARSER_DECLARATION_ERROR,
                &quot;analyzer.declaration.message.incompleteDeclaration&quot;,
                &quot;analyzer.declaration.suggestion.incompleteDeclaration&quot;,
                context
            );
        }

<span class="nc" id="L241">        return createGenericDeclarationError(context, &quot;declaration&quot;);</span>
    }

    /**
     * Analyzes CAPL object type errors.
     */
    private ErrorResult analyzeCaplObjectTypeError(ErrorContext context) {
<span class="nc" id="L248">        String offendingText = getOffendingText(context);</span>

        // Check for invalid CAPL object type
<span class="nc bnc" id="L251" title="All 2 branches missed.">        if (isInvalidCaplObjectType(offendingText)) {</span>
<span class="nc" id="L252">            return createDeclarationError(</span>
                CaplErrorType.PARSER_CAPL_OBJECT_ERROR,
                &quot;analyzer.declaration.message.invalidCaplObjectType&quot;,
                &quot;analyzer.declaration.suggestion.invalidCaplObjectType&quot;,
                context,
                offendingText,
<span class="nc" id="L258">                String.join(&quot;, &quot;, CAPL_OBJECT_TYPES)</span>
            );
        }

<span class="nc" id="L262">        return createGenericDeclarationError(context, &quot;CAPL object type&quot;);</span>
    }

    /**
     * Analyzes generic declaration errors.
     */
    private ErrorResult analyzeGenericDeclarationError(ErrorContext context) {
<span class="nc" id="L269">        return createGenericDeclarationError(context, &quot;declaration&quot;);</span>
    }

    // ========== Helper Methods ==========

    /**
     * Checks if type specifier is missing.
     */
    private boolean isMissingTypeSpecifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L278" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L279">            return false;</span>
        }

        // Look for variable name without preceding type
<span class="nc bnc" id="L283" title="All 2 branches missed.">        return IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L284" title="All 2 branches missed.">               !hasTypeKeyword(tokens);</span>
    }

    /**
     * Checks if variable name is invalid.
     */
    private boolean isInvalidVariableName(String text) {
<span class="nc bnc" id="L291" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L292">            return false;</span>
        }

        // Variable name should be a valid identifier
<span class="nc bnc" id="L296" title="All 2 branches missed.">        return !IDENTIFIER_PATTERN.matcher(text).matches() ||</span>
<span class="nc bnc" id="L297" title="All 2 branches missed.">               BUILTIN_TYPES.contains(text) ||</span>
<span class="nc bnc" id="L298" title="All 2 branches missed.">               TYPE_MODIFIERS.contains(text);</span>
    }

    /**
     * Checks if semicolon is missing.
     */
    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L305" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L306">            return false;</span>
        }

        // Check if this looks like end of declaration without semicolon
<span class="nc bnc" id="L310" title="All 2 branches missed.">        return IDENTIFIER_PATTERN.matcher(offendingText).matches() ||</span>
<span class="nc bnc" id="L311" title="All 2 branches missed.">               offendingText.equals(&quot;}&quot;) ||</span>
<span class="nc bnc" id="L312" title="All 2 branches missed.">               offendingText.equals(&quot;EOF&quot;);</span>
    }

    /**
     * Checks if builtin type is invalid.
     */
    private boolean isInvalidBuiltinType(String text) {
<span class="nc bnc" id="L319" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L320">            return false;</span>
        }

        // Check if it looks like a type but isn't valid
<span class="nc bnc" id="L324" title="All 4 branches missed.">        return text.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; !BUILTIN_TYPES.contains(text);</span>
    }

    /**
     * Checks if type modifier is invalid.
     */
    private boolean isInvalidTypeModifier(String text) {
<span class="nc bnc" id="L331" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L332">            return false;</span>
        }

        // Check for common invalid modifiers
<span class="nc bnc" id="L336" title="All 2 branches missed.">        return text.equals(&quot;static&quot;) || // CAPL doesn't have static</span>
<span class="nc bnc" id="L337" title="All 6 branches missed.">               (text.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; !TYPE_MODIFIERS.contains(text) &amp;&amp; !BUILTIN_TYPES.contains(text));</span>
    }

    /**
     * Checks if function name is missing.
     */
    private boolean isMissingFunctionName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L344" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L345">            return false;</span>
        }

        // Look for type followed by parentheses without function name
<span class="nc bnc" id="L349" title="All 4 branches missed.">        return offendingText.equals(&quot;(&quot;) &amp;&amp; hasTypeKeyword(tokens);</span>
    }

    /**
     * Checks if parentheses are missing in function declaration.
     */
    private boolean isMissingParentheses(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L356" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L357">            return false;</span>
        }

        // Look for function name without opening parenthesis
<span class="nc bnc" id="L361" title="All 4 branches missed.">        return hasTokenPattern(tokens, &quot;void&quot;) || hasTokenPattern(tokens, &quot;int&quot;) ||</span>
<span class="nc bnc" id="L362" title="All 4 branches missed.">               hasTokenPattern(tokens, &quot;float&quot;) || hasTokenPattern(tokens, &quot;char&quot;);</span>
    }

    /**
     * Checks if parameter syntax is invalid.
     */
    private boolean isInvalidParameterSyntax(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L369" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L370">            return false;</span>
        }

        // Look for invalid parameter patterns
<span class="nc bnc" id="L374" title="All 4 branches missed.">        return offendingText.equals(&quot;,&quot;) || offendingText.equals(&quot;)&quot;);</span>
    }

    /**
     * Checks if declaration is incomplete.
     */
    private boolean isIncompleteDeclaration(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L381" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L382">            return true;</span>
        }

        // Check for incomplete patterns
<span class="nc bnc" id="L386" title="All 6 branches missed.">        return hasTypeKeyword(tokens) &amp;&amp; (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if CAPL object type is invalid.
     */
    private boolean isInvalidCaplObjectType(String text) {
<span class="nc bnc" id="L393" title="All 4 branches missed.">        if (text == null || text.isEmpty()) {</span>
<span class="nc" id="L394">            return false;</span>
        }

        // Check if it looks like a CAPL object type but isn't valid
<span class="nc bnc" id="L398" title="All 4 branches missed.">        return text.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; !CAPL_OBJECT_TYPES.contains(text);</span>
    }

    /**
     * Checks if tokens contain a type keyword.
     */
    private boolean hasTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L405" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L406">            return false;</span>
        }

<span class="nc bnc" id="L409" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L410">            String text = token.getText();</span>
<span class="nc bnc" id="L411" title="All 4 branches missed.">            if (BUILTIN_TYPES.contains(text) || CAPL_OBJECT_TYPES.contains(text)) {</span>
<span class="nc" id="L412">                return true;</span>
            }
<span class="nc" id="L414">        }</span>
<span class="nc" id="L415">        return false;</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L422" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L423">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L425" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L426">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates a declaration error result.
     */
    private ErrorResult createDeclarationError(CaplErrorType errorType, String messageKey,
                                             String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L434">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L435">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L437">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L438">        metadata.put(&quot;errorType&quot;, &quot;declaration&quot;);</span>
<span class="nc" id="L439">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L440">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L442">        return ErrorResult.builder()</span>
<span class="nc" id="L443">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L444">            .enhancedMessage(message)</span>
<span class="nc" id="L445">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L446">            .suggestion(suggestion)</span>
<span class="nc" id="L447">            .metadata(metadata)</span>
<span class="nc" id="L448">            .build();</span>
    }

    /**
     * Creates a generic declaration error result.
     */
    private ErrorResult createGenericDeclarationError(ErrorContext context, String declarationType) {
<span class="nc" id="L455">        String message = I18n.l(&quot;analyzer.declaration.message.generic&quot;, declarationType, getOffendingText(context));</span>
<span class="nc" id="L456">        String suggestion = I18n.l(&quot;analyzer.declaration.suggestion.generic&quot;, declarationType);</span>

<span class="nc" id="L458">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L459">        metadata.put(&quot;errorType&quot;, &quot;declaration_generic&quot;);</span>
<span class="nc" id="L460">        metadata.put(&quot;declarationType&quot;, declarationType);</span>
<span class="nc" id="L461">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L463">        return ErrorResult.builder()</span>
<span class="nc" id="L464">            .ruleId(CaplErrorType.PARSER_DECLARATION_ERROR.getErrorCode())</span>
<span class="nc" id="L465">            .enhancedMessage(message)</span>
<span class="nc" id="L466">            .severity(CaplErrorType.PARSER_DECLARATION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L467">            .suggestion(suggestion)</span>
<span class="nc" id="L468">            .metadata(metadata)</span>
<span class="nc" id="L469">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>