<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SectionStructureErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">SectionStructureErrorAnalyzer.java</span></div><h1>SectionStructureErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for section structure errors in CAPL programs.
 * Handles caplIncludesSection and caplVariablesSection rules (CAPL.g4 lines 49, 70) including:
 * - Includes section structure validation
 * - Variables section structure validation
 * - Section content validation
 * - Brace matching within sections
 */
public class SectionStructureErrorAnalyzer extends ParserErrorAnalyzer {

    // Section keywords
<span class="nc" id="L27">    private static final Set&lt;String&gt; SECTION_KEYWORDS = Set.of(</span>
        &quot;includes&quot;, &quot;variables&quot;
    );
    
    // Include directive keywords
<span class="nc" id="L32">    private static final Set&lt;String&gt; INCLUDE_KEYWORDS = Set.of(</span>
        &quot;#include&quot;, &quot;include&quot;
    );
    
    // Variable declaration keywords
<span class="nc" id="L37">    private static final Set&lt;String&gt; VARIABLE_KEYWORDS = Set.of(</span>
        &quot;int&quot;, &quot;char&quot;, &quot;float&quot;, &quot;double&quot;, &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;,
        &quot;message&quot;, &quot;signal&quot;, &quot;timer&quot;, &quot;msTimer&quot;, &quot;const&quot;
    );

    public SectionStructureErrorAnalyzer() {
<span class="nc" id="L43">        super(&quot;SectionStructureErrorAnalyzer&quot;, AnalyzerType.PARSER_STRUCTURAL, 160,</span>
<span class="nc" id="L44">              Set.of(CaplParseContext.INCLUDES_SECTION, CaplParseContext.VARIABLES_SECTION));</span>
<span class="nc" id="L45">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L49">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L51">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L54" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">               isSectionStructureError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L60">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L65">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L71">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L72">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L73">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L76">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific section analyzers
<span class="nc bnc" id="L79" title="All 2 branches missed.">            if (isIncludesSectionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L80">                return analyzeIncludesSectionError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">            } else if (isVariablesSectionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L82">                return analyzeVariablesSectionError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L85">            return createGenericSectionStructureError(context);</span>

<span class="nc" id="L87">        } catch (Exception e) {</span>
<span class="nc" id="L88">            return createGenericSectionStructureError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a section structure error.
     */
    private boolean isSectionStructureError(ErrorContext context) {
<span class="nc" id="L98">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L99">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L100">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc" id="L103">        return hasSectionKeyword(precedingTokens);</span>
    }

    /**
     * Analyzes includes section errors.
     */
    private ErrorResult analyzeIncludesSectionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing opening brace
<span class="nc bnc" id="L111" title="All 2 branches missed.">        if (isMissingOpeningBrace(tokens, offendingText)) {</span>
<span class="nc" id="L112">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_INCLUDES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.includes.missingOpeningBrace&quot;,
                &quot;analyzer.sectionStructure.includes.missingOpeningBraceSuggestion&quot;,
                context
            );
        }

        // Check for missing closing brace
<span class="nc bnc" id="L121" title="All 2 branches missed.">        if (isMissingClosingBrace(tokens, offendingText)) {</span>
<span class="nc" id="L122">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_INCLUDES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.includes.missingClosingBrace&quot;,
                &quot;analyzer.sectionStructure.includes.missingClosingBraceSuggestion&quot;,
                context
            );
        }

        // Check for invalid include directive
<span class="nc bnc" id="L131" title="All 2 branches missed.">        if (isInvalidIncludeDirective(tokens, offendingText)) {</span>
<span class="nc" id="L132">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_INCLUDES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.includes.invalidDirective&quot;,
                &quot;analyzer.sectionStructure.includes.invalidDirectiveSuggestion&quot;,
                context
            );
        }

        // Check for missing string literal
<span class="nc bnc" id="L141" title="All 2 branches missed.">        if (isMissingStringLiteral(tokens, offendingText)) {</span>
<span class="nc" id="L142">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_INCLUDES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.includes.missingStringLiteral&quot;,
                &quot;analyzer.sectionStructure.includes.missingStringLiteralSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L150">        return createGenericSectionStructureError(context);</span>
    }

    /**
     * Analyzes variables section errors.
     */
    private ErrorResult analyzeVariablesSectionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing opening brace
<span class="nc bnc" id="L158" title="All 2 branches missed.">        if (isMissingOpeningBrace(tokens, offendingText)) {</span>
<span class="nc" id="L159">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_VARIABLES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.variables.missingOpeningBrace&quot;,
                &quot;analyzer.sectionStructure.variables.missingOpeningBraceSuggestion&quot;,
                context
            );
        }

        // Check for missing closing brace
<span class="nc bnc" id="L168" title="All 2 branches missed.">        if (isMissingClosingBrace(tokens, offendingText)) {</span>
<span class="nc" id="L169">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_VARIABLES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.variables.missingClosingBrace&quot;,
                &quot;analyzer.sectionStructure.variables.missingClosingBraceSuggestion&quot;,
                context
            );
        }

        // Check for invalid variable declaration
<span class="nc bnc" id="L178" title="All 2 branches missed.">        if (isInvalidVariableDeclaration(tokens, offendingText)) {</span>
<span class="nc" id="L179">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_VARIABLES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.variables.invalidDeclaration&quot;,
                &quot;analyzer.sectionStructure.variables.invalidDeclarationSuggestion&quot;,
                context
            );
        }

        // Check for missing semicolon
<span class="nc bnc" id="L188" title="All 2 branches missed.">        if (isMissingSemicolon(tokens, offendingText)) {</span>
<span class="nc" id="L189">            return createSectionStructureError(</span>
                CaplErrorType.PARSER_VARIABLES_SECTION_ERROR,
                &quot;analyzer.sectionStructure.variables.missingSemicolon&quot;,
                &quot;analyzer.sectionStructure.variables.missingSemicolonSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L197">        return createGenericSectionStructureError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isIncludesSectionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L203">        return hasKeyword(tokens, &quot;includes&quot;);</span>
    }

    private boolean isVariablesSectionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L207">        return hasKeyword(tokens, &quot;variables&quot;);</span>
    }

    private boolean isMissingOpeningBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L211" title="All 2 branches missed.">        return hasSectionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L213" title="All 6 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || hasIncludeKeyword(tokens) || hasVariableKeyword(tokens));</span>
    }

    private boolean isMissingClosingBrace(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L217" title="All 2 branches missed.">        return hasSectionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">               hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L219" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">               offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isInvalidIncludeDirective(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L224" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;includes&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L225" title="All 2 branches missed.">               hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">               !hasIncludeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isMissingStringLiteral(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L232" title="All 2 branches missed.">        return hasIncludeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L233" title="All 6 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    private boolean isInvalidVariableDeclaration(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L237" title="All 2 branches missed.">        return hasKeyword(tokens, &quot;variables&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">               hasKeyword(tokens, &quot;{&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">               !hasVariableKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">               !offendingText.equals(&quot;}&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L241" title="All 2 branches missed.">               !offendingText.equals(&quot;EOF&quot;);</span>
    }

    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L245" title="All 2 branches missed.">        return hasVariableKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L246" title="All 4 branches missed.">               (offendingText.equals(&quot;}&quot;) || offendingText.equals(&quot;EOF&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;;&quot;);</span>
    }

    // ========== Utility Methods ==========

    private boolean hasSectionKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L253">        return hasAnyKeyword(tokens, SECTION_KEYWORDS);</span>
    }

    private boolean hasIncludeKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L257">        return hasAnyKeyword(tokens, INCLUDE_KEYWORDS);</span>
    }

    private boolean hasVariableKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L261">        return hasAnyKeyword(tokens, VARIABLE_KEYWORDS);</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L265" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L266">            return false;</span>
        }

<span class="nc bnc" id="L269" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L271">                return true;</span>
            }
<span class="nc" id="L273">        }</span>
<span class="nc" id="L274">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L278" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L279">            return false;</span>
        }

<span class="nc bnc" id="L282" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L284">                return true;</span>
            }
<span class="nc" id="L286">        }</span>
<span class="nc" id="L287">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L291" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L292">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L294" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L295">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createSectionStructureError(CaplErrorType errorType, String messageKey,
                                                   String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L300">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L301">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L303">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L304">        metadata.put(&quot;errorType&quot;, &quot;sectionStructure&quot;);</span>
<span class="nc" id="L305">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L306">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L308">        return ErrorResult.builder()</span>
<span class="nc" id="L309">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L310">            .enhancedMessage(message)</span>
<span class="nc" id="L311">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L312">            .suggestion(suggestion)</span>
<span class="nc" id="L313">            .metadata(metadata)</span>
<span class="nc" id="L314">            .build();</span>
    }

    private ErrorResult createGenericSectionStructureError(ErrorContext context) {
<span class="nc" id="L318">        String message = I18n.l(&quot;analyzer.sectionStructure.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L319">        String suggestion = I18n.l(&quot;analyzer.sectionStructure.genericSuggestion&quot;);</span>

<span class="nc" id="L321">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L322">        metadata.put(&quot;errorType&quot;, &quot;sectionStructure_generic&quot;);</span>
<span class="nc" id="L323">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L325">        return ErrorResult.builder()</span>
<span class="nc" id="L326">            .ruleId(CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR.getErrorCode())</span>
<span class="nc" id="L327">            .enhancedMessage(message)</span>
<span class="nc" id="L328">            .severity(CaplErrorType.PARSER_SECTION_STRUCTURE_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L329">            .suggestion(suggestion)</span>
<span class="nc" id="L330">            .metadata(metadata)</span>
<span class="nc" id="L331">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>