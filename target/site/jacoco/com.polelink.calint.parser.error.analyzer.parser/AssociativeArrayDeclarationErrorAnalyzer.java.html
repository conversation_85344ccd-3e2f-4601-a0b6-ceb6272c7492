<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AssociativeArrayDeclarationErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">AssociativeArrayDeclarationErrorAnalyzer.java</span></div><h1>AssociativeArrayDeclarationErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Analyzer for associative array declaration errors in CAPL programs.
 * Handles associative array declarations like: int values[char[]]
 */
public class AssociativeArrayDeclarationErrorAnalyzer extends ParserErrorAnalyzer {

    // CAPL type keywords for array declarations
<span class="nc" id="L24">    private static final Set&lt;String&gt; VALID_TYPES = Set.of(</span>
        &quot;char&quot;, &quot;int&quot;, &quot;float&quot;, &quot;double&quot;, &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;
    );

    // Pattern for identifier validation
<span class="nc" id="L29">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>

    public AssociativeArrayDeclarationErrorAnalyzer() {
<span class="nc" id="L32">        super(&quot;AssociativeArrayDeclarationErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 170,</span>
<span class="nc" id="L33">              Set.of(CaplParseContext.ASSOCIATIVE_ARRAY_DECLARATION));</span>
<span class="nc" id="L34">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L38">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L40">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L43" title="All 2 branches missed.">        return parseContext == CaplParseContext.ASSOCIATIVE_ARRAY_DECLARATION ||</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">               isAssociativeArrayDeclarationError(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L50">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L51">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L52">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L55">            String offendingText = getOffendingText(context);</span>

            // Check for missing value type
<span class="nc bnc" id="L58" title="All 2 branches missed.">            if (isMissingValueType(precedingTokens, offendingText)) {</span>
<span class="nc" id="L59">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingValueType&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingValueType&quot;,
                    context
                );
            }

            // Check for invalid value type
<span class="nc bnc" id="L68" title="All 2 branches missed.">            if (isInvalidValueType(precedingTokens, offendingText)) {</span>
<span class="nc" id="L69">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.invalidValueType&quot;,
                    &quot;analyzer.associativeArray.suggestion.invalidValueType&quot;,
                    context,
                    offendingText,
<span class="nc" id="L75">                    String.join(&quot;, &quot;, VALID_TYPES)</span>
                );
            }

            // Check for missing array name
<span class="nc bnc" id="L80" title="All 2 branches missed.">            if (isMissingArrayName(precedingTokens, offendingText)) {</span>
<span class="nc" id="L81">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingArrayName&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingArrayName&quot;,
                    context
                );
            }

            // Check for missing opening bracket
<span class="nc bnc" id="L90" title="All 2 branches missed.">            if (isMissingOpeningBracket(precedingTokens, offendingText)) {</span>
<span class="nc" id="L91">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingOpeningBracket&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingOpeningBracket&quot;,
                    context
                );
            }

            // Check for missing key type
<span class="nc bnc" id="L100" title="All 2 branches missed.">            if (isMissingKeyType(precedingTokens, offendingText)) {</span>
<span class="nc" id="L101">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingKeyType&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingKeyType&quot;,
                    context
                );
            }

            // Check for invalid key type
<span class="nc bnc" id="L110" title="All 2 branches missed.">            if (isInvalidKeyType(precedingTokens, offendingText)) {</span>
<span class="nc" id="L111">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.invalidKeyType&quot;,
                    &quot;analyzer.associativeArray.suggestion.invalidKeyType&quot;,
                    context,
                    offendingText,
<span class="nc" id="L117">                    String.join(&quot;, &quot;, VALID_TYPES)</span>
                );
            }

            // Check for missing key array brackets
<span class="nc bnc" id="L122" title="All 2 branches missed.">            if (isMissingKeyArrayBrackets(precedingTokens, offendingText)) {</span>
<span class="nc" id="L123">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingKeyArrayBrackets&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingKeyArrayBrackets&quot;,
                    context
                );
            }

            // Check for missing closing bracket
<span class="nc bnc" id="L132" title="All 2 branches missed.">            if (isMissingClosingBracket(precedingTokens, offendingText)) {</span>
<span class="nc" id="L133">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingClosingBracket&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingClosingBracket&quot;,
                    context
                );
            }

            // Check for missing semicolon
<span class="nc bnc" id="L142" title="All 2 branches missed.">            if (isMissingSemicolon(precedingTokens, offendingText)) {</span>
<span class="nc" id="L143">                return createAssociativeArrayError(</span>
                    CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                    &quot;analyzer.associativeArray.message.missingSemicolon&quot;,
                    &quot;analyzer.associativeArray.suggestion.missingSemicolon&quot;,
                    context
                );
            }

<span class="nc" id="L151">            return createGenericAssociativeArrayError(context);</span>

<span class="nc" id="L153">        } catch (Exception e) {</span>
<span class="nc" id="L154">            return createGenericAssociativeArrayError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is an associative array declaration error.
     */
    private boolean isAssociativeArrayDeclarationError(ErrorContext context) {
<span class="nc" id="L164">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L165">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L166">            (Token) context.getOffendingSymbol()</span>
        );

        // Look for type followed by identifier and bracket patterns
<span class="nc bnc" id="L170" title="All 2 branches missed.">        return hasTypeKeyword(precedingTokens) &amp;&amp; </span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">               (hasTokenPattern(precedingTokens, &quot;[&quot;) || </span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">                getOffendingText(context).equals(&quot;[&quot;));</span>
    }

    /**
     * Checks if value type is missing.
     */
    private boolean isMissingValueType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L179" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L180">            return false;</span>
        }

        // Look for identifier without preceding type
<span class="nc bnc" id="L184" title="All 2 branches missed.">        return IDENTIFIER_PATTERN.matcher(offendingText).matches() &amp;&amp;</span>
<span class="nc bnc" id="L185" title="All 2 branches missed.">               !hasTypeKeyword(tokens);</span>
    }

    /**
     * Checks if value type is invalid.
     */
    private boolean isInvalidValueType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L192" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L193">            return false;</span>
        }

        // Check if it looks like a type but isn't valid
<span class="nc bnc" id="L197" title="All 2 branches missed.">        return offendingText.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">               !VALID_TYPES.contains(offendingText) &amp;&amp;</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">               !offendingText.equals(&quot;const&quot;);</span>
    }

    /**
     * Checks if array name is missing.
     */
    private boolean isMissingArrayName(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L206" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L207">            return false;</span>
        }

        // Look for type followed by bracket without array name
<span class="nc bnc" id="L211" title="All 4 branches missed.">        return hasTypeKeyword(tokens) &amp;&amp; offendingText.equals(&quot;[&quot;);</span>
    }

    /**
     * Checks if opening bracket is missing.
     */
    private boolean isMissingOpeningBracket(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L218" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L219">            return false;</span>
        }

        // Look for type + identifier without opening bracket
<span class="nc bnc" id="L223" title="All 2 branches missed.">        return hasTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">               hasIdentifierToken(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L225" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if key type is missing.
     */
    private boolean isMissingKeyType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L232" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L233">            return false;</span>
        }

        // Look for opening bracket without key type
<span class="nc bnc" id="L237" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;[&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L238" title="All 4 branches missed.">               (offendingText.equals(&quot;[&quot;) || offendingText.equals(&quot;]&quot;));</span>
    }

    /**
     * Checks if key type is invalid.
     */
    private boolean isInvalidKeyType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L245" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L246">            return false;</span>
        }

        // Check if it's inside brackets and looks like a type but isn't valid
<span class="nc bnc" id="L250" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;[&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">               offendingText.matches(&quot;[a-zA-Z]+&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">               !VALID_TYPES.contains(offendingText);</span>
    }

    /**
     * Checks if key array brackets are missing.
     */
    private boolean isMissingKeyArrayBrackets(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L259" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L260">            return false;</span>
        }

        // Look for key type without array brackets
<span class="nc bnc" id="L264" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;[&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L265" title="All 2 branches missed.">               hasTypeKeywordAfterBracket(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L266" title="All 2 branches missed.">               offendingText.equals(&quot;]&quot;);</span>
    }

    /**
     * Checks if closing bracket is missing.
     */
    private boolean isMissingClosingBracket(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L273" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L274">            return false;</span>
        }

        // Look for complete array declaration without closing bracket
<span class="nc bnc" id="L278" title="All 2 branches missed.">        return hasTokenPattern(tokens, &quot;[&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">               hasTypeKeywordAfterBracket(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;[&quot;, &quot;]&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L281" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if semicolon is missing.
     */
    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L288" title="All 4 branches missed.">        if (tokens == null || tokens.isEmpty()) {</span>
<span class="nc" id="L289">            return false;</span>
        }

        // Look for complete array declaration without semicolon
<span class="nc bnc" id="L293" title="All 2 branches missed.">        return hasCompleteArrayDeclaration(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L294" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    /**
     * Checks if tokens contain a type keyword.
     */
    private boolean hasTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L301" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L302">            return false;</span>
        }

<span class="nc bnc" id="L305" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">            if (VALID_TYPES.contains(token.getText())) {</span>
<span class="nc" id="L307">                return true;</span>
            }
<span class="nc" id="L309">        }</span>
<span class="nc" id="L310">        return false;</span>
    }

    /**
     * Checks if tokens contain an identifier.
     */
    private boolean hasIdentifierToken(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L317" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L318">            return false;</span>
        }

<span class="nc bnc" id="L321" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L322" title="All 2 branches missed.">            if (IDENTIFIER_PATTERN.matcher(token.getText()).matches() &amp;&amp;</span>
<span class="nc bnc" id="L323" title="All 2 branches missed.">                !VALID_TYPES.contains(token.getText())) {</span>
<span class="nc" id="L324">                return true;</span>
            }
<span class="nc" id="L326">        }</span>
<span class="nc" id="L327">        return false;</span>
    }

    /**
     * Checks if there's a type keyword after opening bracket.
     */
    private boolean hasTypeKeywordAfterBracket(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L334" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L335">            return false;</span>
        }

<span class="nc" id="L338">        boolean foundBracket = false;</span>
<span class="nc bnc" id="L339" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L340" title="All 2 branches missed.">            if (token.getText().equals(&quot;[&quot;)) {</span>
<span class="nc" id="L341">                foundBracket = true;</span>
<span class="nc bnc" id="L342" title="All 4 branches missed.">            } else if (foundBracket &amp;&amp; VALID_TYPES.contains(token.getText())) {</span>
<span class="nc" id="L343">                return true;</span>
            }
<span class="nc" id="L345">        }</span>
<span class="nc" id="L346">        return false;</span>
    }

    /**
     * Checks if tokens contain a complete array declaration.
     */
    private boolean hasCompleteArrayDeclaration(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L353" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L354">            return false;</span>
        }

<span class="nc bnc" id="L357" title="All 2 branches missed.">        return hasTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L358" title="All 2 branches missed.">               hasIdentifierToken(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L359" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;[&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L360" title="All 2 branches missed.">               hasTokenPattern(tokens, &quot;]&quot;);</span>
    }

    /**
     * Gets the offending text from error context.
     */
    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L367" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L368">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L370" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L371">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    /**
     * Creates an associative array error result.
     */
    private ErrorResult createAssociativeArrayError(CaplErrorType errorType, String messageKey,
                                                   String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L379">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L380">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L382">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L383">        metadata.put(&quot;errorType&quot;, &quot;associativeArrayDeclaration&quot;);</span>
<span class="nc" id="L384">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L385">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L387">        return ErrorResult.builder()</span>
<span class="nc" id="L388">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L389">            .enhancedMessage(message)</span>
<span class="nc" id="L390">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L391">            .suggestion(suggestion)</span>
<span class="nc" id="L392">            .metadata(metadata)</span>
<span class="nc" id="L393">            .build();</span>
    }

    /**
     * Creates a generic associative array error result.
     */
    private ErrorResult createGenericAssociativeArrayError(ErrorContext context) {
<span class="nc" id="L400">        String message = I18n.l(&quot;analyzer.associativeArray.message.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L401">        String suggestion = I18n.l(&quot;analyzer.associativeArray.suggestion.generic&quot;);</span>

<span class="nc" id="L403">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L404">        metadata.put(&quot;errorType&quot;, &quot;associativeArrayDeclaration_generic&quot;);</span>
<span class="nc" id="L405">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L407">        return ErrorResult.builder()</span>
<span class="nc" id="L408">            .ruleId(CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR.getErrorCode())</span>
<span class="nc" id="L409">            .enhancedMessage(message)</span>
<span class="nc" id="L410">            .severity(CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L411">            .suggestion(suggestion)</span>
<span class="nc" id="L412">            .metadata(metadata)</span>
<span class="nc" id="L413">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>