<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CaplVarDeclarationErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">CaplVarDeclarationErrorAnalyzer.java</span></div><h1>CaplVarDeclarationErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Specialized analyzer for CAPL variable declaration errors.
 * Handles caplVarDeclaration rule (CAPL.g4 line 880) including:
 * - constLiteralDeclaration: const x = 1;
 * - associativeArrayDeclaration: word map[key] value[];
 * - typeDefinition: struct/enum type definitions
 * - declaration: typical C-style declarations
 */
public class CaplVarDeclarationErrorAnalyzer extends ParserErrorAnalyzer {

    // Declaration keywords
<span class="nc" id="L27">    private static final Set&lt;String&gt; CONST_KEYWORDS = Set.of(&quot;const&quot;);</span>
<span class="nc" id="L28">    private static final Set&lt;String&gt; TYPE_DEFINITION_KEYWORDS = Set.of(&quot;struct&quot;, &quot;enum&quot;);</span>
<span class="nc" id="L29">    private static final Set&lt;String&gt; ASSOCIATIVE_ARRAY_KEYWORDS = Set.of(</span>
        &quot;int&quot;, &quot;char&quot;, &quot;float&quot;, &quot;double&quot;, &quot;byte&quot;, &quot;word&quot;, &quot;dword&quot;, &quot;qword&quot;, &quot;int64&quot;
    );

    public CaplVarDeclarationErrorAnalyzer() {
<span class="nc" id="L34">        super(&quot;CaplVarDeclarationErrorAnalyzer&quot;, AnalyzerType.PARSER_DECLARATION, 210,</span>
<span class="nc" id="L35">              Set.of(CaplParseContext.CAPL_VAR_DECLARATION));</span>
<span class="nc" id="L36">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L40">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L42">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L45" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L46" title="All 2 branches missed.">               isCaplVarDeclarationError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L51">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L56">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L62">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L63">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L64">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L67">            String offendingText = getOffendingText(context);</span>

            // Dispatch to specific declaration analyzers
<span class="nc bnc" id="L70" title="All 2 branches missed.">            if (isConstLiteralDeclarationError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L71">                return analyzeConstLiteralDeclarationError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">            } else if (isAssociativeArrayDeclarationError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L73">                return analyzeAssociativeArrayDeclarationError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">            } else if (isTypeDefinitionError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L75">                return analyzeTypeDefinitionError(context, precedingTokens, offendingText);</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">            } else if (isRegularDeclarationError(precedingTokens, offendingText)) {</span>
<span class="nc" id="L77">                return analyzeRegularDeclarationError(context, precedingTokens, offendingText);</span>
            }

<span class="nc" id="L80">            return createGenericCaplVarDeclarationError(context);</span>

<span class="nc" id="L82">        } catch (Exception e) {</span>
<span class="nc" id="L83">            return createGenericCaplVarDeclarationError(context);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is a CAPL variable declaration error.
     */
    private boolean isCaplVarDeclarationError(ErrorContext context) {
<span class="nc" id="L93">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L94">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L95">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L98" title="All 2 branches missed.">        return hasConstKeyword(precedingTokens) || </span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">               hasTypeDefinitionKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">               hasAssociativeArrayKeyword(precedingTokens) ||</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">               hasDeclarationPattern(precedingTokens);</span>
    }

    /**
     * Analyzes const literal declaration errors.
     */
    private ErrorResult analyzeConstLiteralDeclarationError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing identifier
<span class="nc bnc" id="L109" title="All 2 branches missed.">        if (isMissingConstIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L110">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.const.missingIdentifier&quot;,
                &quot;analyzer.caplVarDeclaration.const.missingIdentifierSuggestion&quot;,
                context
            );
        }

        // Check for missing assignment operator
<span class="nc bnc" id="L119" title="All 2 branches missed.">        if (isMissingConstAssignment(tokens, offendingText)) {</span>
<span class="nc" id="L120">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.const.missingAssignment&quot;,
                &quot;analyzer.caplVarDeclaration.const.missingAssignmentSuggestion&quot;,
                context
            );
        }

        // Check for missing constant value
<span class="nc bnc" id="L129" title="All 2 branches missed.">        if (isMissingConstValue(tokens, offendingText)) {</span>
<span class="nc" id="L130">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.const.missingValue&quot;,
                &quot;analyzer.caplVarDeclaration.const.missingValueSuggestion&quot;,
                context
            );
        }

        // Check for missing semicolon
<span class="nc bnc" id="L139" title="All 2 branches missed.">        if (isMissingSemicolon(tokens, offendingText)) {</span>
<span class="nc" id="L140">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_CONST_LITERAL_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.const.missingSemicolon&quot;,
                &quot;analyzer.caplVarDeclaration.const.missingSemicolonSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L148">        return createGenericCaplVarDeclarationError(context);</span>
    }

    /**
     * Analyzes associative array declaration errors.
     */
    private ErrorResult analyzeAssociativeArrayDeclarationError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing identifier
<span class="nc bnc" id="L156" title="All 2 branches missed.">        if (isMissingArrayIdentifier(tokens, offendingText)) {</span>
<span class="nc" id="L157">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingIdentifier&quot;,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingIdentifierSuggestion&quot;,
                context
            );
        }

        // Check for missing key type
<span class="nc bnc" id="L166" title="All 2 branches missed.">        if (isMissingKeyType(tokens, offendingText)) {</span>
<span class="nc" id="L167">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingKeyType&quot;,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingKeyTypeSuggestion&quot;,
                context
            );
        }

        // Check for missing brackets
<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (isMissingArrayBrackets(tokens, offendingText)) {</span>
<span class="nc" id="L177">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_ASSOCIATIVE_ARRAY_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingBrackets&quot;,
                &quot;analyzer.caplVarDeclaration.associativeArray.missingBracketsSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L185">        return createGenericCaplVarDeclarationError(context);</span>
    }

    /**
     * Analyzes type definition errors.
     */
    private ErrorResult analyzeTypeDefinitionError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing type body
<span class="nc bnc" id="L193" title="All 2 branches missed.">        if (isMissingTypeBody(tokens, offendingText)) {</span>
<span class="nc" id="L194">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                &quot;analyzer.caplVarDeclaration.typeDefinition.missingBody&quot;,
                &quot;analyzer.caplVarDeclaration.typeDefinition.missingBodySuggestion&quot;,
                context
            );
        }

        // Check for missing semicolon
<span class="nc bnc" id="L203" title="All 2 branches missed.">        if (isMissingSemicolon(tokens, offendingText)) {</span>
<span class="nc" id="L204">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_TYPE_DEFINITION_ERROR,
                &quot;analyzer.caplVarDeclaration.typeDefinition.missingSemicolon&quot;,
                &quot;analyzer.caplVarDeclaration.typeDefinition.missingSemicolonSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L212">        return createGenericCaplVarDeclarationError(context);</span>
    }

    /**
     * Analyzes regular declaration errors.
     */
    private ErrorResult analyzeRegularDeclarationError(ErrorContext context, List&lt;Token&gt; tokens, String offendingText) {
        // Check for missing type specifier
<span class="nc bnc" id="L220" title="All 2 branches missed.">        if (isMissingTypeSpecifier(tokens, offendingText)) {</span>
<span class="nc" id="L221">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_REGULAR_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.regular.missingTypeSpecifier&quot;,
                &quot;analyzer.caplVarDeclaration.regular.missingTypeSpecifierSuggestion&quot;,
                context
            );
        }

        // Check for missing declarator
<span class="nc bnc" id="L230" title="All 2 branches missed.">        if (isMissingDeclarator(tokens, offendingText)) {</span>
<span class="nc" id="L231">            return createCaplVarDeclarationError(</span>
                CaplErrorType.PARSER_REGULAR_DECLARATION_ERROR,
                &quot;analyzer.caplVarDeclaration.regular.missingDeclarator&quot;,
                &quot;analyzer.caplVarDeclaration.regular.missingDeclaratorSuggestion&quot;,
                context
            );
        }

<span class="nc" id="L239">        return createGenericCaplVarDeclarationError(context);</span>
    }

    // ========== Validation Methods ==========

    private boolean isConstLiteralDeclarationError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L245">        return hasConstKeyword(tokens);</span>
    }

    private boolean isAssociativeArrayDeclarationError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L249" title="All 4 branches missed.">        return hasAssociativeArrayKeyword(tokens) &amp;&amp; hasArrayBrackets(tokens);</span>
    }

    private boolean isTypeDefinitionError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc" id="L253">        return hasTypeDefinitionKeyword(tokens);</span>
    }

    private boolean isRegularDeclarationError(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L257" title="All 2 branches missed.">        return hasDeclarationPattern(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">               !hasConstKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L259" title="All 2 branches missed.">               !hasTypeDefinitionKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">               !hasArrayBrackets(tokens);</span>
    }

    private boolean isMissingConstIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L264" title="All 2 branches missed.">        return hasConstKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L265" title="All 4 branches missed.">               (offendingText.equals(&quot;=&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingConstAssignment(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L269" title="All 4 branches missed.">        return hasConstKeyword(tokens) &amp;&amp; hasIdentifier(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L270" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingConstValue(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L274" title="All 4 branches missed.">        return hasConstKeyword(tokens) &amp;&amp; hasKeyword(tokens, &quot;=&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L275" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingArrayIdentifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L279" title="All 4 branches missed.">        return hasAssociativeArrayKeyword(tokens) &amp;&amp; offendingText.equals(&quot;[&quot;);</span>
    }

    private boolean isMissingKeyType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L283" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;[&quot;) &amp;&amp; offendingText.equals(&quot;]&quot;);</span>
    }

    private boolean isMissingArrayBrackets(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L287" title="All 4 branches missed.">        return hasAssociativeArrayKeyword(tokens) &amp;&amp; hasIdentifier(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;[&quot;) &amp;&amp; </span>
<span class="nc bnc" id="L289" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingTypeBody(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L293" title="All 2 branches missed.">        return hasTypeDefinitionKeyword(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L294" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;)) &amp;&amp;</span>
<span class="nc bnc" id="L295" title="All 2 branches missed.">               !hasKeyword(tokens, &quot;{&quot;);</span>
    }

    private boolean isMissingTypeSpecifier(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L299" title="All 6 branches missed.">        return tokens != null &amp;&amp; tokens.isEmpty() &amp;&amp; hasIdentifier(offendingText);</span>
    }

    private boolean isMissingDeclarator(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L303" title="All 2 branches missed.">        return hasDeclarationPattern(tokens) &amp;&amp; </span>
<span class="nc bnc" id="L304" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean isMissingSemicolon(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L308" title="All 4 branches missed.">        return (hasConstKeyword(tokens) || hasTypeDefinitionKeyword(tokens)) &amp;&amp;</span>
<span class="nc bnc" id="L309" title="All 4 branches missed.">               (offendingText.equals(&quot;EOF&quot;) || offendingText.equals(&quot;}&quot;));</span>
    }

    // ========== Utility Methods ==========

    private boolean hasConstKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L315">        return hasAnyKeyword(tokens, CONST_KEYWORDS);</span>
    }

    private boolean hasTypeDefinitionKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L319">        return hasAnyKeyword(tokens, TYPE_DEFINITION_KEYWORDS);</span>
    }

    private boolean hasAssociativeArrayKeyword(List&lt;Token&gt; tokens) {
<span class="nc" id="L323">        return hasAnyKeyword(tokens, ASSOCIATIVE_ARRAY_KEYWORDS);</span>
    }

    private boolean hasArrayBrackets(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L327" title="All 4 branches missed.">        return hasKeyword(tokens, &quot;[&quot;) || hasKeyword(tokens, &quot;]&quot;);</span>
    }

    private boolean hasDeclarationPattern(List&lt;Token&gt; tokens) {
        // Simple heuristic for declaration pattern
<span class="nc bnc" id="L332" title="All 4 branches missed.">        return tokens != null &amp;&amp; tokens.size() &gt; 0;</span>
    }

    private boolean hasIdentifier(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L336" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L337">            return false;</span>
        }

<span class="nc bnc" id="L340" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L341" title="All 2 branches missed.">            if (token.getText().matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;)) {</span>
<span class="nc" id="L342">                return true;</span>
            }
<span class="nc" id="L344">        }</span>
<span class="nc" id="L345">        return false;</span>
    }

    private boolean hasIdentifier(String text) {
<span class="nc bnc" id="L349" title="All 4 branches missed.">        return text != null &amp;&amp; text.matches(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
    }

    private boolean hasKeyword(List&lt;Token&gt; tokens, String keyword) {
<span class="nc bnc" id="L353" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L354">            return false;</span>
        }

<span class="nc bnc" id="L357" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L358" title="All 2 branches missed.">            if (keyword.equals(token.getText())) {</span>
<span class="nc" id="L359">                return true;</span>
            }
<span class="nc" id="L361">        }</span>
<span class="nc" id="L362">        return false;</span>
    }

    private boolean hasAnyKeyword(List&lt;Token&gt; tokens, Set&lt;String&gt; keywords) {
<span class="nc bnc" id="L366" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L367">            return false;</span>
        }

<span class="nc bnc" id="L370" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L371" title="All 2 branches missed.">            if (keywords.contains(token.getText())) {</span>
<span class="nc" id="L372">                return true;</span>
            }
<span class="nc" id="L374">        }</span>
<span class="nc" id="L375">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L379" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L380">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L382" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L383">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createCaplVarDeclarationError(CaplErrorType errorType, String messageKey,
                                                     String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L388">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L389">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L391">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L392">        metadata.put(&quot;errorType&quot;, &quot;caplVarDeclaration&quot;);</span>
<span class="nc" id="L393">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L394">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L396">        return ErrorResult.builder()</span>
<span class="nc" id="L397">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L398">            .enhancedMessage(message)</span>
<span class="nc" id="L399">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L400">            .suggestion(suggestion)</span>
<span class="nc" id="L401">            .metadata(metadata)</span>
<span class="nc" id="L402">            .build();</span>
    }

    private ErrorResult createGenericCaplVarDeclarationError(ErrorContext context) {
<span class="nc" id="L406">        String message = I18n.l(&quot;analyzer.caplVarDeclaration.generic&quot;, getOffendingText(context));</span>
<span class="nc" id="L407">        String suggestion = I18n.l(&quot;analyzer.caplVarDeclaration.genericSuggestion&quot;);</span>

<span class="nc" id="L409">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L410">        metadata.put(&quot;errorType&quot;, &quot;caplVarDeclaration_generic&quot;);</span>
<span class="nc" id="L411">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L413">        return ErrorResult.builder()</span>
<span class="nc" id="L414">            .ruleId(CaplErrorType.PARSER_DECLARATION_ERROR.getErrorCode())</span>
<span class="nc" id="L415">            .enhancedMessage(message)</span>
<span class="nc" id="L416">            .severity(CaplErrorType.PARSER_DECLARATION_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L417">            .suggestion(suggestion)</span>
<span class="nc" id="L418">            .metadata(metadata)</span>
<span class="nc" id="L419">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>