<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NoViableAltAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">NoViableAltAnalyzer.java</span></div><h1>NoViableAltAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.NoViableAltException;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Analyzer for NO_VIABLE_ALT parser errors.
 * Provides generic error messages for syntax issues where no viable alternative exists.
 */
public class NoViableAltAnalyzer extends ParserErrorAnalyzer {

<span class="nc" id="L22">    private static final Pattern INCOMPLETE_STATEMENT_PATTERN = Pattern.compile(&quot;\\w+\\s*;?\\s*}&quot;);</span>

    public NoViableAltAnalyzer() {
<span class="nc" id="L25">        super(&quot;NoViableAltAnalyzer&quot;, AnalyzerType.PARSER_STRUCTURAL, 90, Set.of());</span>
<span class="nc" id="L26">    }</span>

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
        // Check if this is a NO_VIABLE_ALT error
<span class="nc bnc" id="L31" title="All 2 branches missed.">        return context.getException() instanceof NoViableAltException ||</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">               (context.getOriginalMessage() != null &amp;&amp;</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">                context.getOriginalMessage().contains(&quot;no viable alternative&quot;));</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
<span class="nc" id="L38">        String offendingText = getOffendingText(context);</span>

<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (offendingText != null) {</span>
            // Check for incomplete statements
<span class="nc bnc" id="L42" title="All 2 branches missed.">            if (INCOMPLETE_STATEMENT_PATTERN.matcher(offendingText).find()) {</span>
<span class="nc" id="L43">                return analyzeIncompleteStatement(context, offendingText);</span>
            }
        }

        // Generic NO_VIABLE_ALT error
<span class="nc" id="L48">        return createGenericNoViableAltError(context, offendingText);</span>
    }

    private ErrorResult analyzeIncompleteStatement(ErrorContext context, String offendingText) {
<span class="nc" id="L52">        String message = I18n.l(&quot;analyzer.noViableAlt.message.incompleteStatement&quot;, offendingText);</span>
<span class="nc" id="L53">        String suggestion = I18n.l(&quot;analyzer.noViableAlt.suggestion.incompleteStatement&quot;);</span>

<span class="nc" id="L55">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L56">        metadata.put(&quot;errorType&quot;, &quot;incomplete_statement&quot;);</span>
<span class="nc" id="L57">        metadata.put(&quot;offendingText&quot;, offendingText);</span>

<span class="nc" id="L59">        return ErrorResult.builder()</span>
<span class="nc" id="L60">            .ruleId(CaplErrorType.PARSER_NO_VIABLE_ALT.getErrorCode())</span>
<span class="nc" id="L61">            .enhancedMessage(message)</span>
<span class="nc" id="L62">            .severity(CaplErrorType.PARSER_NO_VIABLE_ALT.getDefaultSeverity())</span>
<span class="nc" id="L63">            .suggestion(suggestion)</span>
<span class="nc" id="L64">            .metadata(metadata)</span>
<span class="nc" id="L65">            .build();</span>
    }

    private ErrorResult createGenericNoViableAltError(ErrorContext context, String offendingText) {
<span class="nc bnc" id="L69" title="All 2 branches missed.">        String finalText = offendingText != null ? offendingText : &quot;unknown&quot;;</span>
<span class="nc" id="L70">        String message = I18n.l(&quot;analyzer.noViableAlt.message.generic&quot;, finalText);</span>
<span class="nc" id="L71">        String suggestion = I18n.l(&quot;analyzer.noViableAlt.suggestion.generic&quot;);</span>

<span class="nc" id="L73">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L74">        metadata.put(&quot;errorType&quot;, &quot;no_viable_alternative&quot;);</span>
<span class="nc" id="L75">        metadata.put(&quot;offendingText&quot;, offendingText);</span>

<span class="nc" id="L77">        return ErrorResult.builder()</span>
<span class="nc" id="L78">            .ruleId(CaplErrorType.PARSER_NO_VIABLE_ALT.getErrorCode())</span>
<span class="nc" id="L79">            .enhancedMessage(message)</span>
<span class="nc" id="L80">            .severity(CaplErrorType.PARSER_NO_VIABLE_ALT.getDefaultSeverity())</span>
<span class="nc" id="L81">            .suggestion(suggestion)</span>
<span class="nc" id="L82">            .metadata(metadata)</span>
<span class="nc" id="L83">            .build();</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc" id="L87">        String offendingSymbol = null;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">        if (context.getOffendingSymbol() != null) {</span>
            // Get the actual text from the token, not the toString() representation
<span class="nc" id="L90">            Object symbol = context.getOffendingSymbol();</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">            if (symbol instanceof org.antlr.v4.runtime.Token) {</span>
<span class="nc" id="L92">                org.antlr.v4.runtime.Token token = (org.antlr.v4.runtime.Token) symbol;</span>
<span class="nc" id="L93">                offendingSymbol = token.getText();</span>
<span class="nc" id="L94">            } else {</span>
<span class="nc" id="L95">                offendingSymbol = symbol.toString();</span>
            }
        }

        // Try to extract from original message
<span class="nc" id="L100">        String originalMessage = context.getOriginalMessage();</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">        if (originalMessage != null) {</span>
            // Look for patterns like &quot;at '...'&quot;
<span class="nc" id="L103">            Pattern pattern = Pattern.compile(&quot;at '([^']*)'&quot;);</span>
<span class="nc" id="L104">            Matcher matcher = pattern.matcher(originalMessage);</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">            if (matcher.find()) {</span>
<span class="nc" id="L106">                return matcher.group(1);</span>
            }
        }

<span class="nc bnc" id="L110" title="All 2 branches missed.">        return offendingSymbol != null ? offendingSymbol : &quot;unknown&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>