<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventHandlerErrorAnalyzer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_source">EventHandlerErrorAnalyzer.java</span></div><h1>EventHandlerErrorAnalyzer.java</h1><pre class="source lang-java linenums">package com.polelink.calint.parser.error.analyzer.parser;

import com.polelink.calint.parser.error.analyzer.AnalyzerType;
import com.polelink.calint.parser.error.context.CaplParseContext;
import com.polelink.calint.parser.error.model.ErrorContext;
import com.polelink.calint.parser.error.model.ErrorResult;
import com.polelink.calint.parser.error.model.CaplErrorType;
import com.polelink.calint.i18n.I18n;
import org.antlr.v4.runtime.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Specialized analyzer for event handler declaration errors in CAPL programs.
 * Handles eventHandler rule (CAPL.g4 line 427) including:
 * - System variable events: on sysvar, on envvar, on sysvar_change, etc.
 * - FlexRay events: on frFrame, on frError, on frPDU
 * - Qualified event types: on message, on signal, on timer, etc.
 */
public class EventHandlerErrorAnalyzer extends ParserErrorAnalyzer {

    // System variable event keywords (sysvarEvent - line 448)
<span class="nc" id="L27">    private static final Set&lt;String&gt; SYSVAR_EVENT_KEYWORDS = Set.of(</span>
        &quot;sysvar&quot;, &quot;envvar&quot;, &quot;sysvarMember&quot;, &quot;sysvar_change&quot;, &quot;sysvar_update&quot;
    );

    // FlexRay event keywords (flexrayEvent - line 504)
<span class="nc" id="L32">    private static final Set&lt;String&gt; FLEXRAY_EVENT_KEYWORDS = Set.of(</span>
        &quot;frFrame&quot;, &quot;frError&quot;, &quot;frFrameError&quot;, &quot;frNullFrame&quot;, &quot;frPocState&quot;,
        &quot;frSlot&quot;, &quot;frStartCycle&quot;, &quot;frSymbol&quot;, &quot;frPDU&quot;
    );

    // Common event type keywords (eventType - line 638)
<span class="nc" id="L38">    private static final Set&lt;String&gt; COMMON_EVENT_KEYWORDS = Set.of(</span>
        // System events
        &quot;timer&quot;, &quot;key&quot;, &quot;preStart&quot;, &quot;start&quot;, &quot;preStop&quot;, &quot;stopMeasurement&quot;,
        &quot;fct_called&quot;, &quot;fct_returned&quot;, &quot;warningLimit&quot;,
        // CAN events
        &quot;message&quot;, &quot;errorFrame&quot;, &quot;errorActive&quot;, &quot;errorPassive&quot;, &quot;busOff&quot;, &quot;signal&quot;,
        &quot;signal_change&quot;, &quot;signal_update&quot;,
        // LIN events
        &quot;linFrame&quot;, &quot;linDlcInfo&quot;, &quot;linLongDominantSignal&quot;, &quot;linSchedulerModeChange&quot;,
        &quot;linSleepModeEvent&quot;, &quot;linSpikeEvent&quot;, &quot;linWakeupFrame&quot;, &quot;linCsError&quot;,
        &quot;linReceiveError&quot;, &quot;linSlaveTimeout&quot;, &quot;linSyncError&quot;, &quot;linTransmError&quot;,
        // Other events
        &quot;pdu&quot;, &quot;diagRequest&quot;, &quot;diagResponse&quot;, &quot;ethernetPacket&quot;, &quot;ethernetFrame&quot;,
        &quot;mostMessage&quot;, &quot;mostAmsMessage&quot;, &quot;mostRawMessage&quot;, &quot;j1587Message&quot;,
        &quot;a429Word&quot;, &quot;serviceSignal&quot;
    );

    // Patterns for validation
<span class="nc" id="L56">    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(&quot;[a-zA-Z_][a-zA-Z0-9_]*&quot;);</span>
<span class="nc" id="L57">    private static final Pattern HEX_PATTERN = Pattern.compile(&quot;0[xX][0-9a-fA-F]+&quot;);</span>
<span class="nc" id="L58">    private static final Pattern DECIMAL_PATTERN = Pattern.compile(&quot;\\d+&quot;);</span>

    public EventHandlerErrorAnalyzer() {
<span class="nc" id="L61">        super(&quot;EventHandlerErrorAnalyzer&quot;, AnalyzerType.PARSER_EVENT, 180,</span>
<span class="nc" id="L62">              Set.of(CaplParseContext.EVENT_HANDLER, CaplParseContext.SYSVAR_EVENT,</span>
                     CaplParseContext.FLEXRAY_EVENT, CaplParseContext.QUALIFIED_EVENT_TYPE));
<span class="nc" id="L64">    }</span>

    @Override
    public boolean canAnalyze(ErrorContext context) {
<span class="nc" id="L68">        CaplParseContext parseContext = context.getParseContext();</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (parseContext == null) {</span>
<span class="nc" id="L70">            parseContext = detectParseContext(context);</span>
        }
        
<span class="nc bnc" id="L73" title="All 2 branches missed.">        return getSupportedContexts().contains(parseContext) ||</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">               isEventHandlerError(context);</span>
    }

    @Override
    protected boolean canAnalyzeSpecific(ErrorContext context) {
<span class="nc" id="L79">        return canAnalyze(context);</span>
    }

    @Override
    public ErrorResult analyze(ErrorContext context) {
<span class="nc" id="L84">        return analyzeSpecific(context);</span>
    }

    @Override
    protected ErrorResult analyzeSpecific(ErrorContext context) {
        try {
<span class="nc" id="L90">            List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L91">                (Parser) context.getRecognizer(),</span>
<span class="nc" id="L92">                (Token) context.getOffendingSymbol()</span>
            );

<span class="nc" id="L95">            String offendingText = getOffendingText(context);</span>

            // Check for missing 'on' keyword
<span class="nc bnc" id="L98" title="All 2 branches missed.">            if (isMissingOnKeyword(precedingTokens, offendingText)) {</span>
<span class="nc" id="L99">                return createEventHandlerError(</span>
                    CaplErrorType.PARSER_EVENT_HANDLER_ERROR,
                    &quot;analyzer.eventHandler.missingOnKeyword&quot;,
                    &quot;analyzer.eventHandler.missingOnKeywordSuggestion&quot;,
                    context
                );
            }

            // Check for missing event type
<span class="nc bnc" id="L108" title="All 2 branches missed.">            if (isMissingEventType(precedingTokens, offendingText)) {</span>
<span class="nc" id="L109">                return createEventHandlerError(</span>
                    CaplErrorType.PARSER_EVENT_HANDLER_ERROR,
                    &quot;analyzer.eventHandler.missingEventType&quot;,
                    &quot;analyzer.eventHandler.missingEventTypeSuggestion&quot;,
                    context
                );
            }

            // Check for missing compound statement
<span class="nc bnc" id="L118" title="All 2 branches missed.">            if (isMissingCompoundStatement(precedingTokens, offendingText)) {</span>
<span class="nc" id="L119">                return createEventHandlerError(</span>
                    CaplErrorType.PARSER_EVENT_HANDLER_ERROR,
                    &quot;analyzer.eventHandler.missingCompoundStatement&quot;,
                    &quot;analyzer.eventHandler.missingCompoundStatementSuggestion&quot;,
                    context
                );
            }

            // Dispatch to specific event type analyzers
<span class="nc" id="L128">            String eventType = determineEventType(precedingTokens);</span>
            
<span class="nc bnc" id="L130" title="All 2 branches missed.">            if (SYSVAR_EVENT_KEYWORDS.contains(eventType)) {</span>
<span class="nc" id="L131">                return analyzeSysvarEventError(context, precedingTokens, offendingText, eventType);</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">            } else if (FLEXRAY_EVENT_KEYWORDS.contains(eventType)) {</span>
<span class="nc" id="L133">                return analyzeFlexRayEventError(context, precedingTokens, offendingText, eventType);</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">            } else if (COMMON_EVENT_KEYWORDS.contains(eventType)) {</span>
<span class="nc" id="L135">                return analyzeQualifiedEventTypeError(context, precedingTokens, offendingText, eventType);</span>
            }

<span class="nc" id="L138">            return createGenericEventHandlerError(context, eventType);</span>

<span class="nc" id="L140">        } catch (Exception e) {</span>
<span class="nc" id="L141">            return createGenericEventHandlerError(context, &quot;unknown&quot;);</span>
        }
    }

    // ========== Helper Methods ==========

    /**
     * Checks if this is an event handler error.
     */
    private boolean isEventHandlerError(ErrorContext context) {
<span class="nc" id="L151">        List&lt;Token&gt; precedingTokens = extractPrecedingTokens(</span>
<span class="nc" id="L152">            (Parser) context.getRecognizer(),</span>
<span class="nc" id="L153">            (Token) context.getOffendingSymbol()</span>
        );

<span class="nc bnc" id="L156" title="All 4 branches missed.">        return hasOnKeyword(precedingTokens) || hasEventTypeKeyword(precedingTokens);</span>
    }

    /**
     * Determines the event type from tokens.
     */
    private String determineEventType(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L163" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L164">            return &quot;unknown&quot;;</span>
        }

<span class="nc bnc" id="L167" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L168">            String text = token.getText();</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">            if (SYSVAR_EVENT_KEYWORDS.contains(text) ||</span>
<span class="nc bnc" id="L170" title="All 2 branches missed.">                FLEXRAY_EVENT_KEYWORDS.contains(text) ||</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">                COMMON_EVENT_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L172">                return text;</span>
            }
<span class="nc" id="L174">        }</span>
<span class="nc" id="L175">        return &quot;unknown&quot;;</span>
    }

    /**
     * Checks if 'on' keyword is missing.
     */
    private boolean isMissingOnKeyword(List&lt;Token&gt; tokens, String offendingText) {
        // Look for event type keywords without preceding 'on'
<span class="nc bnc" id="L183" title="All 4 branches missed.">        return !hasOnKeyword(tokens) &amp;&amp; hasEventTypeKeyword(tokens);</span>
    }

    /**
     * Checks if event type is missing.
     */
    private boolean isMissingEventType(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L190" title="All 4 branches missed.">        return hasOnKeyword(tokens) &amp;&amp; !hasEventTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L191" title="All 4 branches missed.">               (offendingText.equals(&quot;{&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Checks if compound statement is missing.
     */
    private boolean isMissingCompoundStatement(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L198" title="All 4 branches missed.">        return hasOnKeyword(tokens) &amp;&amp; hasEventTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L199" title="All 4 branches missed.">               (offendingText.equals(&quot;;&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    /**
     * Analyzes system variable event errors.
     */
    private ErrorResult analyzeSysvarEventError(ErrorContext context, List&lt;Token&gt; tokens,
                                               String offendingText, String eventType) {
<span class="nc bnc" id="L207" title="All 2 branches missed.">        if (isMissingEventParameter(tokens, offendingText)) {</span>
<span class="nc" id="L208">            return createEventHandlerError(</span>
                CaplErrorType.PARSER_SYSVAR_EVENT_ERROR,
                &quot;analyzer.eventHandler.sysvar.missingParameter&quot;,
                &quot;analyzer.eventHandler.sysvar.missingParameterSuggestion&quot;,
                context, eventType
            );
        }

<span class="nc" id="L216">        return createGenericEventHandlerError(context, eventType);</span>
    }

    /**
     * Analyzes FlexRay event errors.
     */
    private ErrorResult analyzeFlexRayEventError(ErrorContext context, List&lt;Token&gt; tokens,
                                                String offendingText, String eventType) {
<span class="nc bnc" id="L224" title="All 2 branches missed.">        if (isMissingEventParameter(tokens, offendingText)) {</span>
<span class="nc" id="L225">            return createEventHandlerError(</span>
                CaplErrorType.PARSER_FLEXRAY_EVENT_ERROR,
                &quot;analyzer.eventHandler.flexray.missingParameter&quot;,
                &quot;analyzer.eventHandler.flexray.missingParameterSuggestion&quot;,
                context, eventType
            );
        }

<span class="nc" id="L233">        return createGenericEventHandlerError(context, eventType);</span>
    }

    /**
     * Analyzes qualified event type errors.
     */
    private ErrorResult analyzeQualifiedEventTypeError(ErrorContext context, List&lt;Token&gt; tokens,
                                                      String offendingText, String eventType) {
<span class="nc bnc" id="L241" title="All 2 branches missed.">        if (isMissingEventParameter(tokens, offendingText)) {</span>
<span class="nc" id="L242">            return createEventHandlerError(</span>
                CaplErrorType.PARSER_QUALIFIED_EVENT_TYPE_ERROR,
                &quot;analyzer.eventHandler.qualified.missingParameter&quot;,
                &quot;analyzer.eventHandler.qualified.missingParameterSuggestion&quot;,
                context, eventType
            );
        }

<span class="nc" id="L250">        return createGenericEventHandlerError(context, eventType);</span>
    }

    // ========== Validation Methods ==========

    private boolean isMissingEventParameter(List&lt;Token&gt; tokens, String offendingText) {
<span class="nc bnc" id="L256" title="All 2 branches missed.">        return hasEventTypeKeyword(tokens) &amp;&amp;</span>
<span class="nc bnc" id="L257" title="All 4 branches missed.">               (offendingText.equals(&quot;{&quot;) || offendingText.equals(&quot;EOF&quot;));</span>
    }

    private boolean hasOnKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L261" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L262">            return false;</span>
        }

<span class="nc bnc" id="L265" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc bnc" id="L266" title="All 2 branches missed.">            if (&quot;on&quot;.equals(token.getText())) {</span>
<span class="nc" id="L267">                return true;</span>
            }
<span class="nc" id="L269">        }</span>
<span class="nc" id="L270">        return false;</span>
    }

    private boolean hasEventTypeKeyword(List&lt;Token&gt; tokens) {
<span class="nc bnc" id="L274" title="All 2 branches missed.">        if (tokens == null) {</span>
<span class="nc" id="L275">            return false;</span>
        }

<span class="nc bnc" id="L278" title="All 2 branches missed.">        for (Token token : tokens) {</span>
<span class="nc" id="L279">            String text = token.getText();</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">            if (SYSVAR_EVENT_KEYWORDS.contains(text) ||</span>
<span class="nc bnc" id="L281" title="All 2 branches missed.">                FLEXRAY_EVENT_KEYWORDS.contains(text) ||</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">                COMMON_EVENT_KEYWORDS.contains(text)) {</span>
<span class="nc" id="L283">                return true;</span>
            }
<span class="nc" id="L285">        }</span>
<span class="nc" id="L286">        return false;</span>
    }

    private String getOffendingText(ErrorContext context) {
<span class="nc bnc" id="L290" title="All 2 branches missed.">        if (context.getOffendingSymbol() instanceof Token) {</span>
<span class="nc" id="L291">            return ((Token) context.getOffendingSymbol()).getText();</span>
        }
<span class="nc bnc" id="L293" title="All 2 branches missed.">        return context.getOffendingSymbol() != null ?</span>
<span class="nc" id="L294">               context.getOffendingSymbol().toString() : &quot;unknown&quot;;</span>
    }

    private ErrorResult createEventHandlerError(CaplErrorType errorType, String messageKey,
                                               String suggestionKey, ErrorContext context, Object... params) {
<span class="nc" id="L299">        String message = I18n.l(messageKey, params);</span>
<span class="nc" id="L300">        String suggestion = I18n.l(suggestionKey, params);</span>

<span class="nc" id="L302">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L303">        metadata.put(&quot;errorType&quot;, &quot;eventHandler&quot;);</span>
<span class="nc" id="L304">        metadata.put(&quot;context&quot;, context.getParseContext());</span>
<span class="nc" id="L305">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L307">        return ErrorResult.builder()</span>
<span class="nc" id="L308">            .ruleId(errorType.getErrorCode())</span>
<span class="nc" id="L309">            .enhancedMessage(message)</span>
<span class="nc" id="L310">            .severity(errorType.getDefaultSeverity())</span>
<span class="nc" id="L311">            .suggestion(suggestion)</span>
<span class="nc" id="L312">            .metadata(metadata)</span>
<span class="nc" id="L313">            .build();</span>
    }

    private ErrorResult createGenericEventHandlerError(ErrorContext context, String eventType) {
<span class="nc" id="L317">        String message = I18n.l(&quot;analyzer.eventHandler.generic&quot;, eventType, getOffendingText(context));</span>
<span class="nc" id="L318">        String suggestion = I18n.l(&quot;analyzer.eventHandler.genericSuggestion&quot;, eventType);</span>

<span class="nc" id="L320">        Map&lt;String, Object&gt; metadata = new HashMap&lt;&gt;();</span>
<span class="nc" id="L321">        metadata.put(&quot;errorType&quot;, &quot;eventHandler_generic&quot;);</span>
<span class="nc" id="L322">        metadata.put(&quot;eventType&quot;, eventType);</span>
<span class="nc" id="L323">        metadata.put(&quot;offendingText&quot;, getOffendingText(context));</span>

<span class="nc" id="L325">        return ErrorResult.builder()</span>
<span class="nc" id="L326">            .ruleId(CaplErrorType.PARSER_EVENT_HANDLER_ERROR.getErrorCode())</span>
<span class="nc" id="L327">            .enhancedMessage(message)</span>
<span class="nc" id="L328">            .severity(CaplErrorType.PARSER_EVENT_HANDLER_ERROR.getDefaultSeverity())</span>
<span class="nc" id="L329">            .suggestion(suggestion)</span>
<span class="nc" id="L330">            .metadata(metadata)</span>
<span class="nc" id="L331">            .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>