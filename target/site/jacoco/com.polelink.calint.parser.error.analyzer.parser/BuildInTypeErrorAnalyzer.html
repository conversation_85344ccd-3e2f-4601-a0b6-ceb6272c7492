<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BuildInTypeErrorAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.html" class="el_package">com.polelink.calint.parser.error.analyzer.parser</a> &gt; <span class="el_class">BuildInTypeErrorAnalyzer</span></div><h1>BuildInTypeErrorAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">765 of 765</td><td class="ctr2">0%</td><td class="bar">138 of 138</td><td class="ctr2">0%</td><td class="ctr1">102</td><td class="ctr2">102</td><td class="ctr1">158</td><td class="ctr2">158</td><td class="ctr1">33</td><td class="ctr2">33</td></tr></tfoot><tbody><tr><td id="a2"><a href="BuildInTypeErrorAnalyzer.java.html#L170" class="el_method">analyzeCompositeTypeError(ErrorContext, List, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="70" alt="70"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="BuildInTypeErrorAnalyzer.java.html#L69" class="el_method">analyzeSpecific(ErrorContext)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="55" alt="55"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="BuildInTypeErrorAnalyzer.java.html#L141" class="el_method">analyzeCaplIntegralTypeError(ErrorContext, List, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="48" alt="48"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="BuildInTypeErrorAnalyzer.java.html#L392" class="el_method">createGenericBuiltInTypeError(ErrorContext)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="47" alt="47"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="BuildInTypeErrorAnalyzer.java.html#L374" class="el_method">createBuiltInTypeError(CaplErrorType, String, String, ErrorContext, Object[])</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="45" alt="45"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="BuildInTypeErrorAnalyzer.java.html#L113" class="el_method">analyzeStandardCTypeError(ErrorContext, List, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="40" alt="40"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h11">6</td><td class="ctr2" id="i11">6</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a19"><a href="BuildInTypeErrorAnalyzer.java.html#L281" class="el_method">hasIdentifier(List)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="39" alt="39"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a18"><a href="BuildInTypeErrorAnalyzer.java.html#L275" class="el_method">hasConflictingModifiers(List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="36" alt="36"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h17">3</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a13"><a href="BuildInTypeErrorAnalyzer.java.html#L297" class="el_method">getStandardCType(List)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">4</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="BuildInTypeErrorAnalyzer.java.html#L311" class="el_method">getCaplIntegralType(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="26" alt="26"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">4</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a11"><a href="BuildInTypeErrorAnalyzer.java.html#L325" class="el_method">getCompositeType(List)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="26" alt="26"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a22"><a href="BuildInTypeErrorAnalyzer.java.html#L98" class="el_method">isBuiltInTypeError(ErrorContext)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="25" alt="25"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h12">6</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a29"><a href="BuildInTypeErrorAnalyzer.java.html#L242" class="el_method">isMissingTypeBody(List, String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="25" alt="25"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h14">4</td><td class="ctr2" id="i14">4</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a20"><a href="BuildInTypeErrorAnalyzer.java.html#L339" class="el_method">hasKeyword(List, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="24" alt="24"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g11">4</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a14"><a href="BuildInTypeErrorAnalyzer.java.html#L352" class="el_method">hasAnyKeyword(List, Set)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="24" alt="24"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h10">7</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a6"><a href="BuildInTypeErrorAnalyzer.java.html#L47" class="el_method">canAnalyze(ErrorContext)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="22" alt="22"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h13">5</td><td class="ctr2" id="i13">5</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a27"><a href="BuildInTypeErrorAnalyzer.java.html#L249" class="el_method">isMissingClosingBrace(List, String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="22" alt="22"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h15">4</td><td class="ctr2" id="i15">4</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a32"><a href="BuildInTypeErrorAnalyzer.java.html#L26" class="el_method">static {...}</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="22" alt="22"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h18">3</td><td class="ctr2" id="i18">3</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a28"><a href="BuildInTypeErrorAnalyzer.java.html#L220" class="el_method">isMissingDeclarator(List, String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="20" alt="20"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h20">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a25"><a href="BuildInTypeErrorAnalyzer.java.html#L232" class="el_method">isInvalidModifier(List, String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="18" alt="18"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h21">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a12"><a href="BuildInTypeErrorAnalyzer.java.html#L365" class="el_method">getOffendingText(ErrorContext)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="18" alt="18"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h16">4</td><td class="ctr2" id="i16">4</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a30"><a href="BuildInTypeErrorAnalyzer.java.html#L237" class="el_method">isMissingTypeName(List, String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="16" alt="16"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a15"><a href="BuildInTypeErrorAnalyzer.java.html#L270" class="el_method">hasBuiltInType(List)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="16" alt="16"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a26"><a href="BuildInTypeErrorAnalyzer.java.html#L226" class="el_method">isInvalidTypeCombination(List, String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="12" alt="12"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h23">2</td><td class="ctr2" id="i23">2</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a5"><a href="BuildInTypeErrorAnalyzer.java.html#L41" class="el_method">BuildInTypeErrorAnalyzer()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="8" alt="8"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h19">3</td><td class="ctr2" id="i19">3</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a21"><a href="BuildInTypeErrorAnalyzer.java.html#L258" class="el_method">hasStandardCType(List)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="5" alt="5"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a16"><a href="BuildInTypeErrorAnalyzer.java.html#L262" class="el_method">hasCaplIntegralType(List)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="5" alt="5"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a17"><a href="BuildInTypeErrorAnalyzer.java.html#L266" class="el_method">hasCompositeType(List)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="5" alt="5"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a7"><a href="BuildInTypeErrorAnalyzer.java.html#L58" class="el_method">canAnalyzeSpecific(ErrorContext)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a0"><a href="BuildInTypeErrorAnalyzer.java.html#L63" class="el_method">analyze(ErrorContext)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a31"><a href="BuildInTypeErrorAnalyzer.java.html#L208" class="el_method">isStandardCTypeError(List, String)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a23"><a href="BuildInTypeErrorAnalyzer.java.html#L212" class="el_method">isCaplIntegralTypeError(List, String)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a24"><a href="BuildInTypeErrorAnalyzer.java.html#L216" class="el_method">isCompositeTypeError(List, String)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>