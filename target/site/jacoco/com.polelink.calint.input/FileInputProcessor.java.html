<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileInputProcessor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.input</a> &gt; <span class="el_source">FileInputProcessor.java</span></div><h1>FileInputProcessor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.input;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.polelink.calint.issue.SeverityLevel;
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.util.SourceLocation;
import com.polelink.calint.issue.IssueObject;

/**
 * Implements the {@link InputProcessor} interface to load CAPL source files
 * from the file system.
 */
<span class="nc" id="L23">public class FileInputProcessor implements InputProcessor {</span>

<span class="nc" id="L25">    private static final Set&lt;String&gt; SUPPORTED_EXTENSIONS = Set.of(&quot;.can&quot;, &quot;.cin&quot;, &quot;.capl&quot;);</span>

    /**
     * Loads source files from the given list of paths.
     *
     * Paths can be to individual CAPL files (ending with .can) or directories.
     * If a path is a directory, this method will recursively search for .can files
     * within that directory and its subdirectories.
     *
     * All I/O errors are converted to issues and returned in the InputProcessingResult.
     * This method does not throw exceptions.
     *
     * @param paths A list of strings, where each string is an absolute or relative path
     *              to a file or directory.
     * @param defaultCharset The default charset to use when reading file contents if
     *                       the charset cannot be determined otherwise.
     * @return An InputProcessingResult containing the loaded source files and any issues encountered.
     */
    @Override
    public InputProcessingResult loadSourceFiles(List&lt;String&gt; paths, Charset defaultCharset) {
<span class="nc" id="L45">        List&lt;SourceFile&gt; sourceFiles = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L46">        List&lt;IssueObject&gt; issues = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L48" title="All 2 branches missed.">        if (paths == null) {</span>
<span class="nc" id="L49">            return InputProcessingResult.success(sourceFiles);</span>
        }

<span class="nc bnc" id="L52" title="All 2 branches missed.">        for (String pathString : paths) {</span>
            try {
<span class="nc" id="L54">                Path path = Paths.get(pathString);</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">                if (Files.isDirectory(path)) {</span>
<span class="nc" id="L56">                    try (Stream&lt;Path&gt; walk = Files.walk(path)) {</span>
<span class="nc" id="L57">                        List&lt;Path&gt; caplFilesInDir = walk</span>
<span class="nc" id="L58">                            .filter(Files::isRegularFile)</span>
<span class="nc" id="L59">                            .filter(p -&gt; SUPPORTED_EXTENSIONS.stream()</span>
<span class="nc" id="L60">                                .anyMatch(ext -&gt; p.toString().toLowerCase().endsWith(ext)))</span>
<span class="nc" id="L61">                            .collect(Collectors.toList());</span>

<span class="nc bnc" id="L63" title="All 2 branches missed.">                        for (Path caplFile : caplFilesInDir) {</span>
                            try {
<span class="nc" id="L65">                                sourceFiles.add(createSourceFile(caplFile, defaultCharset));</span>
<span class="nc" id="L66">                            } catch (IOException e) {</span>
<span class="nc" id="L67">                                IssueObject issue = new IssueObject(</span>
                                    &quot;LINT-F003&quot;,
<span class="nc" id="L69">                                    &quot;Failed to read file '&quot; + caplFile + &quot;': &quot; + e.getMessage(),</span>
                                    SeverityLevel.ERROR,
<span class="nc" id="L71">                                    new SourceLocation(caplFile.toString(), 1, 1, 1, 1)</span>
                                );
<span class="nc" id="L73">                                issues.add(issue);</span>
<span class="nc" id="L74">                            }</span>
<span class="nc" id="L75">                        }</span>
<span class="nc" id="L76">                    } catch (IOException e) {</span>
<span class="nc" id="L77">                        IssueObject issue = new IssueObject(</span>
                            &quot;LINT-F002&quot;,
<span class="nc" id="L79">                            &quot;Failed to scan directory '&quot; + pathString + &quot;': &quot; + e.getMessage(),</span>
                            SeverityLevel.ERROR,
                            new SourceLocation(pathString, 1, 1, 1, 1)
                        );
<span class="nc" id="L83">                        issues.add(issue);</span>
<span class="nc" id="L84">                    }</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">                } else if (Files.isRegularFile(path)) {</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">                    if (SUPPORTED_EXTENSIONS.stream().anyMatch(ext -&gt; path.toString().toLowerCase().endsWith(ext))) {</span>
                        try {
<span class="nc" id="L88">                            sourceFiles.add(createSourceFile(path, defaultCharset));</span>
<span class="nc" id="L89">                        } catch (IOException e) {</span>
<span class="nc" id="L90">                            IssueObject issue = new IssueObject(</span>
                                &quot;LINT-F003&quot;,
<span class="nc" id="L92">                                &quot;Failed to read file '&quot; + pathString + &quot;': &quot; + e.getMessage(),</span>
                                SeverityLevel.ERROR,
                                new SourceLocation(pathString, 1, 1, 1, 1)
                            );
<span class="nc" id="L96">                            issues.add(issue);</span>
<span class="nc" id="L97">                        }</span>
                    }
                } else {
                    // Optionally log a warning for paths that are not found or not files/directories
                    // For now, we just skip them as per general robustness.
                }
<span class="nc" id="L103">            } catch (Exception e) {</span>
                // Convert any unexpected exception to an issue
<span class="nc" id="L105">                IssueObject issue = new IssueObject(</span>
                    &quot;LINT-F001&quot;,
<span class="nc" id="L107">                    &quot;Failed to process path '&quot; + pathString + &quot;': &quot; + e.getMessage(),</span>
                    SeverityLevel.ERROR,
                    new SourceLocation(pathString, 1, 1, 1, 1)
                );
<span class="nc" id="L111">                issues.add(issue);</span>
<span class="nc" id="L112">            }</span>
<span class="nc" id="L113">        }</span>

<span class="nc bnc" id="L115" title="All 2 branches missed.">        if (issues.isEmpty()) {</span>
<span class="nc" id="L116">            return InputProcessingResult.success(sourceFiles);</span>
        } else {
<span class="nc" id="L118">            return InputProcessingResult.withIssues(sourceFiles, issues);</span>
        }
    }

    /**
     * Helper method to read a file's content and create a {@link SourceFile} object.
     *
     * @param filePath The path to the file.
     * @param charset The charset to use for reading the file.
     * @return A {@link SourceFile} object.
     * @throws IOException If an I/O error occurs reading the file.
     */
    private SourceFile createSourceFile(Path filePath, Charset charset) throws IOException {
<span class="nc" id="L131">        String content = Files.readString(filePath, charset);</span>
<span class="nc" id="L132">        return new SourceFile(filePath, content, charset);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>