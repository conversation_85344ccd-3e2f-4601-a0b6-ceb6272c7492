<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PreprocessingInputProcessor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.input</a> &gt; <span class="el_source">PreprocessingInputProcessor.java</span></div><h1>PreprocessingInputProcessor.java</h1><pre class="source lang-java linenums">package com.polelink.calint.input;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.polelink.calint.preprocessor.PreprocessedSourceFile;
import com.polelink.calint.util.SourceFile;
import com.polelink.calint.issue.IssueObject;
import com.polelink.calint.preprocessor.CaplPreprocessor;
import com.polelink.calint.preprocessor.MacroContext;
import com.polelink.calint.preprocessor.PreprocessorResult;

/**
 * An InputProcessor that performs CAPL preprocessing (macro expansion) 
 * before returning source files for parsing.
 * 
 * This processor wraps another InputProcessor (typically FileInputProcessor)
 * and adds preprocessing capabilities. It loads source files using the
 * wrapped processor, then applies CAPL preprocessing to expand macros
 * and handle other preprocessing directives.
 * 
 * The preprocessing step occurs between file loading and parsing:
 * Files → InputProcessor → SourceFiles → Preprocessor → PreprocessedSourceFiles → Parser
 */
public class PreprocessingInputProcessor implements InputProcessor {

<span class="nc" id="L31">    private static final Logger LOGGER = LoggerFactory.getLogger(PreprocessingInputProcessor.class.getName());</span>

    private final InputProcessor baseProcessor;
    private final CaplPreprocessor preprocessor;
    private final MacroContextProvider macroContextProvider;

    /**
     * Creates a new PreprocessingInputProcessor with default configuration.
     * 
     * @param baseProcessor The underlying input processor to load files
     */
    public PreprocessingInputProcessor(InputProcessor baseProcessor) {
<span class="nc" id="L43">        this(baseProcessor, new CaplPreprocessor(), new DefaultMacroContextProvider());</span>
<span class="nc" id="L44">    }</span>

    /**
     * Creates a new PreprocessingInputProcessor with custom configuration.
     * 
     * @param baseProcessor The underlying input processor to load files
     * @param preprocessor The CAPL preprocessor to use
     * @param macroContextProvider Provider for macro context information
     */
    public PreprocessingInputProcessor(InputProcessor baseProcessor, 
                                     CaplPreprocessor preprocessor,
<span class="nc" id="L55">                                     MacroContextProvider macroContextProvider) {</span>
<span class="nc" id="L56">        this.baseProcessor = baseProcessor;</span>
<span class="nc" id="L57">        this.preprocessor = preprocessor;</span>
<span class="nc" id="L58">        this.macroContextProvider = macroContextProvider;</span>
<span class="nc" id="L59">    }</span>

    @Override
    public InputProcessingResult loadSourceFiles(List&lt;String&gt; paths, Charset defaultCharset) {
<span class="nc" id="L63">        LOGGER.debug(&quot;Loading and preprocessing CAPL source files&quot;);</span>

        // First, load source files using the base processor
<span class="nc" id="L66">        InputProcessingResult baseResult = baseProcessor.loadSourceFiles(paths, defaultCharset);</span>

        // If base processing had issues, return them immediately
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (baseResult.hasIssues()) {</span>
<span class="nc" id="L70">            return baseResult;</span>
        }

<span class="nc" id="L73">        List&lt;SourceFile&gt; originalSourceFiles = baseResult.getSourceFiles();</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">        if (originalSourceFiles.isEmpty()) {</span>
<span class="nc" id="L75">            LOGGER.debug(&quot;No source files found to preprocess&quot;);</span>
<span class="nc" id="L76">            return InputProcessingResult.success(new ArrayList&lt;&gt;());</span>
        }

        // Then, preprocess each source file and collect all errors
<span class="nc" id="L80">        List&lt;SourceFile&gt; preprocessedFiles = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L81">        List&lt;PreprocessedSourceFile&gt; failedFiles = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L83" title="All 2 branches missed.">        for (SourceFile sourceFile : originalSourceFiles) {</span>
<span class="nc" id="L84">            PreprocessedSourceFile preprocessedFile = preprocessSourceFile(sourceFile);</span>

<span class="nc bnc" id="L86" title="All 2 branches missed.">            if (preprocessedFile.isPreprocessingSuccessful()) {</span>
<span class="nc" id="L87">                preprocessedFiles.add(preprocessedFile.getSourceFileForParsing());</span>
            } else {
<span class="nc" id="L89">                failedFiles.add(preprocessedFile);</span>
<span class="nc" id="L90">                LOGGER.debug(&quot;Preprocessing failed for file: &quot; + sourceFile.getAbsolutePath());</span>
            }
<span class="nc" id="L92">        }</span>

        // Collect all preprocessing issues
<span class="nc" id="L95">        List&lt;IssueObject&gt; allIssues = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">        for (PreprocessedSourceFile failedFile : failedFiles) {</span>
<span class="nc bnc" id="L97" title="All 4 branches missed.">            if (failedFile.getPreprocessorResult() != null &amp;&amp; failedFile.getPreprocessorResult().hasIssues()) {</span>
<span class="nc" id="L98">                allIssues.addAll(failedFile.getPreprocessorResult().getIssues());</span>
            }
<span class="nc" id="L100">            LOGGER.debug(&quot;Including failed preprocessing file for error reporting: &quot; + failedFile.getAbsolutePath());</span>
<span class="nc" id="L101">        }</span>

<span class="nc" id="L103">        LOGGER.debug(&quot;Preprocessing completed for &quot; + preprocessedFiles.size() + &quot; files&quot;);</span>

        // Return result with issues if any preprocessing failed
<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (!allIssues.isEmpty()) {</span>
<span class="nc" id="L107">            return InputProcessingResult.withIssues(preprocessedFiles, allIssues);</span>
        } else {
<span class="nc" id="L109">            return InputProcessingResult.success(preprocessedFiles);</span>
        }
    }

    /**
     * Preprocesses a single source file.
     *
     * @param sourceFile The source file to preprocess
     * @return A PreprocessedSourceFile containing the preprocessing results
     */
    private PreprocessedSourceFile preprocessSourceFile(SourceFile sourceFile) {
        try {
<span class="nc" id="L121">            LOGGER.debug(&quot;Preprocessing file: &quot; + sourceFile.getAbsolutePath());</span>

            // Create macro context for this file
<span class="nc" id="L124">            MacroContext context = macroContextProvider.createContext(sourceFile);</span>

            // Check if preprocessing is needed
<span class="nc bnc" id="L127" title="All 2 branches missed.">            if (!preprocessor.containsMacros(sourceFile.getContent())) {</span>
<span class="nc" id="L128">                LOGGER.debug(&quot;No macros found in file: &quot; + sourceFile.getAbsolutePath() + &quot;, skipping preprocessing&quot;);</span>
<span class="nc" id="L129">                return PreprocessedSourceFile.withoutPreprocessing(sourceFile);</span>
            }

            // Perform preprocessing
<span class="nc" id="L133">            PreprocessorResult result = preprocessor.preprocess(sourceFile.getContent(), context);</span>

<span class="nc bnc" id="L135" title="All 2 branches missed.">            if (!result.isSuccessful()) {</span>
<span class="nc" id="L136">                LOGGER.debug(&quot;Preprocessing failed for file: &quot; + sourceFile.getAbsolutePath() +</span>
<span class="nc" id="L137">                             &quot; - &quot; + result.getIssues().size() + &quot; issues found&quot;);</span>

                // Return the preprocessing result directly so that the original issues are preserved
<span class="nc" id="L140">                return new PreprocessedSourceFile(sourceFile, result);</span>
            }

<span class="nc bnc" id="L143" title="All 2 branches missed.">            if (result.hasMacroExpansions()) {</span>
<span class="nc" id="L144">                LOGGER.debug(&quot;Expanded &quot; + result.getExpansionCount() + &quot; macros in file: &quot; +</span>
<span class="nc" id="L145">                          sourceFile.getAbsolutePath());</span>
            }

<span class="nc" id="L148">            return new PreprocessedSourceFile(sourceFile, result);</span>

<span class="nc" id="L150">        } catch (Exception e) {</span>
<span class="nc" id="L151">            LOGGER.debug(&quot;Unexpected error during preprocessing of file: &quot; + </span>
<span class="nc" id="L152">                         sourceFile.getAbsolutePath() + &quot; - &quot; + e.getMessage());</span>
<span class="nc" id="L153">            return PreprocessedSourceFile.withPreprocessingFailure(sourceFile, </span>
<span class="nc" id="L154">                    &quot;Preprocessing error: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Interface for providing macro context information.
     */
    public interface MacroContextProvider {
        /**
         * Creates a macro context for the given source file.
         * 
         * @param sourceFile The source file to create context for
         * @return The macro context
         */
        MacroContext createContext(SourceFile sourceFile);
    }

    /**
     * Default implementation of MacroContextProvider that creates
     * basic context with file information only.
     */
<span class="nc" id="L175">    public static class DefaultMacroContextProvider implements MacroContextProvider {</span>
        @Override
        public MacroContext createContext(SourceFile sourceFile) {
<span class="nc" id="L178">            String fileName = sourceFile.getAbsolutePath().getFileName().toString();</span>
<span class="nc" id="L179">            return MacroContext.builder()</span>
<span class="nc" id="L180">                    .sourceFilePath(sourceFile.getAbsolutePath())</span>
<span class="nc" id="L181">                    .baseFileName(fileName) // Use same file as base for simplicity</span>
<span class="nc" id="L182">                    .build();</span>
        }
    }

    /**
     * Implementation of MacroContextProvider for simulation environments.
     */
    public static class SimulationMacroContextProvider implements MacroContextProvider {
        private final String nodeName;
        private final String networkName;
        private final String busType;
        private final int channel;

<span class="nc" id="L195">        public SimulationMacroContextProvider(String nodeName, String networkName, String busType, int channel) {</span>
<span class="nc" id="L196">            this.nodeName = nodeName;</span>
<span class="nc" id="L197">            this.networkName = networkName;</span>
<span class="nc" id="L198">            this.busType = busType;</span>
<span class="nc" id="L199">            this.channel = channel;</span>
<span class="nc" id="L200">        }</span>

        @Override
        public MacroContext createContext(SourceFile sourceFile) {
<span class="nc" id="L204">            String fileName = sourceFile.getAbsolutePath().getFileName().toString();</span>
<span class="nc" id="L205">            return MacroContext.builder()</span>
<span class="nc" id="L206">                    .sourceFilePath(sourceFile.getAbsolutePath())</span>
<span class="nc" id="L207">                    .baseFileName(fileName)</span>
<span class="nc" id="L208">                    .nodeName(nodeName)</span>
<span class="nc" id="L209">                    .networkName(networkName)</span>
<span class="nc" id="L210">                    .busType(busType)</span>
<span class="nc" id="L211">                    .channel(channel)</span>
<span class="nc" id="L212">                    .build();</span>
        }
    }

    /**
     * Gets the underlying base processor.
     * 
     * @return The base input processor
     */
    public InputProcessor getBaseProcessor() {
<span class="nc" id="L222">        return baseProcessor;</span>
    }

    /**
     * Gets the CAPL preprocessor.
     * 
     * @return The preprocessor
     */
    public CaplPreprocessor getPreprocessor() {
<span class="nc" id="L231">        return preprocessor;</span>
    }

    /**
     * Gets the macro context provider.
     * 
     * @return The macro context provider
     */
    public MacroContextProvider getMacroContextProvider() {
<span class="nc" id="L240">        return macroContextProvider;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>