<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InputProcessingResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.input</a> &gt; <span class="el_source">InputProcessingResult.java</span></div><h1>InputProcessingResult.java</h1><pre class="source lang-java linenums">package com.polelink.calint.input;

import java.util.ArrayList;
import java.util.List;

import com.polelink.calint.util.SourceFile;
import com.polelink.calint.issue.IssueObject;

/**
 * Result of input processing that includes both successfully processed files
 * and any issues encountered during processing.
 */
public class InputProcessingResult {
    
    private final List&lt;SourceFile&gt; sourceFiles;
    private final List&lt;IssueObject&gt; issues;
    
<span class="nc" id="L18">    public InputProcessingResult(List&lt;SourceFile&gt; sourceFiles, List&lt;IssueObject&gt; issues) {</span>
<span class="nc bnc" id="L19" title="All 2 branches missed.">        this.sourceFiles = sourceFiles != null ? sourceFiles : new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">        this.issues = issues != null ? issues : new ArrayList&lt;&gt;();</span>
<span class="nc" id="L21">    }</span>
    
    /**
     * Creates a successful result with no issues.
     */
    public static InputProcessingResult success(List&lt;SourceFile&gt; sourceFiles) {
<span class="nc" id="L27">        return new InputProcessingResult(sourceFiles, new ArrayList&lt;&gt;());</span>
    }
    
    /**
     * Creates a result with issues.
     */
    public static InputProcessingResult withIssues(List&lt;SourceFile&gt; sourceFiles, List&lt;IssueObject&gt; issues) {
<span class="nc" id="L34">        return new InputProcessingResult(sourceFiles, issues);</span>
    }
    
    /**
     * Gets the successfully processed source files.
     */
    public List&lt;SourceFile&gt; getSourceFiles() {
<span class="nc" id="L41">        return sourceFiles;</span>
    }
    
    /**
     * Gets the issues encountered during processing.
     */
    public List&lt;IssueObject&gt; getIssues() {
<span class="nc" id="L48">        return issues;</span>
    }
    
    /**
     * Returns true if there are any issues.
     */
    public boolean hasIssues() {
<span class="nc bnc" id="L55" title="All 2 branches missed.">        return !issues.isEmpty();</span>
    }
    
    /**
     * Returns true if processing was completely successful.
     */
    public boolean isSuccessful() {
<span class="nc" id="L62">        return issues.isEmpty();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>