<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SymbolTable.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.symbol</a> &gt; <span class="el_source">SymbolTable.java</span></div><h1>SymbolTable.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.symbol;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.polelink.calint.ast.node.base.AstNode;

/**
 * Symbol table for managing symbols and scopes during semantic analysis.
 *
 * This class provides functionality to:
 * - Enter and exit scopes
 * - Define symbols in the current scope
 * - Resolve symbols by searching through the scope chain
 * - Manage the scope stack
 */
public class SymbolTable {

    private final List&lt;Scope&gt; scopeStack;

    /**
     * Creates a new symbol table with a global scope.
     */
<span class="nc" id="L25">    public SymbolTable() {</span>
<span class="nc" id="L26">        this.scopeStack = new ArrayList&lt;&gt;();</span>
        // Start with a global/file scope - create directly since scopeStack is empty
<span class="nc" id="L28">        Scope globalScope = new Scope(ScopeKind.FILE, null, null);</span>
<span class="nc" id="L29">        scopeStack.add(globalScope);</span>
<span class="nc" id="L30">    }</span>

    /**
     * Enters a new scope.
     *
     * @param kind The kind of scope to enter
     * @param associatedNode The AST node associated with this scope (can be null)
     */
    public void enterScope(ScopeKind kind, AstNode associatedNode) {
<span class="nc" id="L39">        Scope parent = getCurrentScope();</span>
<span class="nc" id="L40">        Scope newScope = new Scope(kind, associatedNode, parent);</span>
<span class="nc" id="L41">        scopeStack.add(newScope);</span>
<span class="nc" id="L42">    }</span>

    /**
     * Exits the current scope.
     *
     * @throws IllegalStateException if trying to exit the global scope
     */
    public void exitScope() {
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (scopeStack.size() &lt;= 1) {</span>
<span class="nc" id="L51">            throw new IllegalStateException(&quot;Cannot exit global scope&quot;);</span>
        }
<span class="nc" id="L53">        scopeStack.remove(scopeStack.size() - 1);</span>
<span class="nc" id="L54">    }</span>

    /**
     * Gets the current (innermost) scope.
     *
     * @return The current scope
     * @throws IllegalStateException if no scope is active
     */
    public Scope getCurrentScope() {
<span class="nc bnc" id="L63" title="All 2 branches missed.">        if (scopeStack.isEmpty()) {</span>
<span class="nc" id="L64">            throw new IllegalStateException(&quot;No active scope&quot;);</span>
        }
<span class="nc" id="L66">        return scopeStack.get(scopeStack.size() - 1);</span>
    }

    /**
     * Defines a symbol in the current scope.
     *
     * @param symbol The symbol to define
     * @return true if the symbol was defined successfully, false if it already exists in the current scope
     */
    public boolean define(Symbol symbol) {
<span class="nc" id="L76">        return getCurrentScope().define(symbol);</span>
    }

    /**
     * Resolves a symbol by searching through the scope chain from innermost to outermost.
     *
     * @param name The name of the symbol to resolve
     * @return The symbol if found, empty otherwise
     */
    public Optional&lt;Symbol&gt; resolve(String name) {
        // Search from innermost to outermost scope
<span class="nc bnc" id="L87" title="All 2 branches missed.">        for (int i = scopeStack.size() - 1; i &gt;= 0; i--) {</span>
<span class="nc" id="L88">            Scope scope = scopeStack.get(i);</span>
<span class="nc" id="L89">            Optional&lt;Symbol&gt; symbol = scope.lookup(name);</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">            if (symbol.isPresent()) {</span>
<span class="nc" id="L91">                return symbol;</span>
            }
        }
<span class="nc" id="L94">        return Optional.empty();</span>
    }

    /**
     * Gets the current scope depth (number of nested scopes).
     */
    public int getScopeDepth() {
<span class="nc" id="L101">        return scopeStack.size();</span>
    }

    /**
     * Checks if we're currently in the global scope.
     */
    public boolean isInGlobalScope() {
<span class="nc bnc" id="L108" title="All 2 branches missed.">        return scopeStack.size() == 1;</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L113">        return String.format(&quot;SymbolTable{scopeDepth=%d, currentScope=%s}&quot;,</span>
<span class="nc" id="L114">                           scopeStack.size(), getCurrentScope());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>