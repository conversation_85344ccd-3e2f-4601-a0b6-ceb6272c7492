<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Symbol.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.symbol</a> &gt; <span class="el_source">Symbol.java</span></div><h1>Symbol.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.symbol;

import com.polelink.calint.ast.node.base.AstNode;

import java.util.Objects;

/**
 * Represents a symbol in the symbol table.
 * A symbol has a name, kind, type information, and references to its declaration.
 */
public class Symbol {
    
    private final String name;
    private final SymbolKind kind;
    private final String type; // For now, just a string representation of the type
    private final AstNode declarationNode;
    private final Scope declaringScope;

    /**
     * Creates a new symbol.
     * 
     * @param name The name of the symbol
     * @param kind The kind of symbol
     * @param type The type of the symbol (can be null)
     * @param declarationNode The AST node where this symbol is declared
     * @param declaringScope The scope where this symbol is declared
     */
<span class="nc" id="L28">    public Symbol(String name, SymbolKind kind, String type, AstNode declarationNode, Scope declaringScope) {</span>
<span class="nc" id="L29">        this.name = Objects.requireNonNull(name, &quot;name cannot be null&quot;);</span>
<span class="nc" id="L30">        this.kind = Objects.requireNonNull(kind, &quot;kind cannot be null&quot;);</span>
<span class="nc" id="L31">        this.type = type;</span>
<span class="nc" id="L32">        this.declarationNode = declarationNode;</span>
<span class="nc" id="L33">        this.declaringScope = declaringScope;</span>
<span class="nc" id="L34">    }</span>

    /**
     * Gets the name of this symbol.
     */
    public String getName() {
<span class="nc" id="L40">        return name;</span>
    }

    /**
     * Gets the kind of this symbol.
     */
    public SymbolKind getKind() {
<span class="nc" id="L47">        return kind;</span>
    }

    /**
     * Gets the type of this symbol.
     */
    public String getType() {
<span class="nc" id="L54">        return type;</span>
    }

    /**
     * Gets the AST node where this symbol is declared.
     */
    public AstNode getDeclarationNode() {
<span class="nc" id="L61">        return declarationNode;</span>
    }

    /**
     * Gets the scope where this symbol is declared.
     */
    public Scope getDeclaringScope() {
<span class="nc" id="L68">        return declaringScope;</span>
    }

    /**
     * Checks if this symbol is a variable.
     */
    public boolean isVariable() {
<span class="nc bnc" id="L75" title="All 2 branches missed.">        return kind == SymbolKind.VARIABLE;</span>
    }

    /**
     * Checks if this symbol is a function.
     */
    public boolean isFunction() {
<span class="nc bnc" id="L82" title="All 2 branches missed.">        return kind == SymbolKind.FUNCTION;</span>
    }

    /**
     * Checks if this symbol is a parameter.
     */
    public boolean isParameter() {
<span class="nc bnc" id="L89" title="All 2 branches missed.">        return kind == SymbolKind.PARAMETER;</span>
    }

    @Override
    public boolean equals(Object o) {
<span class="nc bnc" id="L94" title="All 2 branches missed.">        if (this == o) return true;</span>
<span class="nc bnc" id="L95" title="All 4 branches missed.">        if (o == null || getClass() != o.getClass()) return false;</span>
<span class="nc" id="L96">        Symbol symbol = (Symbol) o;</span>
<span class="nc bnc" id="L97" title="All 4 branches missed.">        return Objects.equals(name, symbol.name) &amp;&amp;</span>
               kind == symbol.kind &amp;&amp;
<span class="nc bnc" id="L99" title="All 2 branches missed.">               Objects.equals(type, symbol.type);</span>
    }

    @Override
    public int hashCode() {
<span class="nc" id="L104">        return Objects.hash(name, kind, type);</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L109">        return String.format(&quot;Symbol{name='%s', kind=%s, type='%s'}&quot;, name, kind, type);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>