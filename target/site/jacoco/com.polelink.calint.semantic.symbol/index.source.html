<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.polelink.calint.semantic.symbol</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <span class="el_package">com.polelink.calint.semantic.symbol</span></div><h1>com.polelink.calint.semantic.symbol</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">446 of 446</td><td class="ctr2">0%</td><td class="bar">34 of 34</td><td class="ctr2">0%</td><td class="ctr1">49</td><td class="ctr2">49</td><td class="ctr1">82</td><td class="ctr2">82</td><td class="ctr1">32</td><td class="ctr2">32</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a2"><a href="Symbol.java.html" class="el_source">Symbol.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="140" alt="140"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">21</td><td class="ctr2" id="g0">21</td><td class="ctr1" id="h1">22</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j0">12</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a4"><a href="SymbolTable.java.html" class="el_source">SymbolTable.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="137" alt="137"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">14</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h0">27</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j1">9</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="Scope.java.html" class="el_source">Scope.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="97" alt="97"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j2">9</td><td class="ctr2" id="k2">9</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="SymbolKind.java.html" class="el_source">SymbolKind.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="45" alt="45"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="ScopeKind.java.html" class="el_source">ScopeKind.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="27" alt="27"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>