<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SymbolKind.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.symbol</a> &gt; <span class="el_source">SymbolKind.java</span></div><h1>SymbolKind.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.symbol;

/**
 * Enumeration of different kinds of symbols in CAPL.
 */
<span class="nc" id="L6">public enum SymbolKind {</span>
    /**
     * Variable symbol (local or global variables).
     */
<span class="nc" id="L10">    VARIABLE,</span>
    
    /**
     * Function parameter symbol.
     */
<span class="nc" id="L15">    PARAMETER,</span>
    
    /**
     * Function symbol.
     */
<span class="nc" id="L20">    FUNCTION,</span>
    
    /**
     * Message symbol.
     */
<span class="nc" id="L25">    MESSAGE,</span>
    
    /**
     * Event handler symbol.
     */
<span class="nc" id="L30">    EVENT_HANDLER,</span>
    
    /**
     * Type symbol (for user-defined types).
     */
<span class="nc" id="L35">    TYPE,</span>
    
    /**
     * Constant symbol.
     */
<span class="nc" id="L40">    CONSTANT</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>