<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ScopeKind.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.symbol</a> &gt; <span class="el_source">ScopeKind.java</span></div><h1>ScopeKind.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.symbol;

/**
 * Enumeration of different kinds of scopes in CAPL.
 */
<span class="nc" id="L6">public enum ScopeKind {</span>
    /**
     * Global/file scope.
     */
<span class="nc" id="L10">    FILE,</span>
    
    /**
     * Function scope.
     */
<span class="nc" id="L15">    FUNCTION,</span>
    
    /**
     * Block scope (compound statements).
     */
<span class="nc" id="L20">    BLOCK,</span>
    
    /**
     * Event handler scope.
     */
<span class="nc" id="L25">    EVENT_HANDLER</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>