<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Scope.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.semantic.symbol</a> &gt; <span class="el_source">Scope.java</span></div><h1>Scope.java</h1><pre class="source lang-java linenums">package com.polelink.calint.semantic.symbol;

import com.polelink.calint.ast.node.base.AstNode;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Represents a scope in the symbol table.
 * A scope contains symbols and has a reference to its parent scope.
 */
public class Scope {
    
    private final ScopeKind kind;
    private final AstNode associatedNode;
    private final Scope parent;
    private final Map&lt;String, Symbol&gt; symbols;

    /**
     * Creates a new scope.
     * 
     * @param kind The kind of scope
     * @param associatedNode The AST node associated with this scope (can be null)
     * @param parent The parent scope (null for global scope)
     */
<span class="nc" id="L28">    public Scope(ScopeKind kind, AstNode associatedNode, Scope parent) {</span>
<span class="nc" id="L29">        this.kind = Objects.requireNonNull(kind, &quot;kind cannot be null&quot;);</span>
<span class="nc" id="L30">        this.associatedNode = associatedNode;</span>
<span class="nc" id="L31">        this.parent = parent;</span>
<span class="nc" id="L32">        this.symbols = new HashMap&lt;&gt;();</span>
<span class="nc" id="L33">    }</span>

    /**
     * Gets the kind of this scope.
     */
    public ScopeKind getKind() {
<span class="nc" id="L39">        return kind;</span>
    }

    /**
     * Gets the AST node associated with this scope.
     */
    public AstNode getAssociatedNode() {
<span class="nc" id="L46">        return associatedNode;</span>
    }

    /**
     * Gets the parent scope.
     */
    public Scope getParent() {
<span class="nc" id="L53">        return parent;</span>
    }

    /**
     * Defines a symbol in this scope.
     * 
     * @param symbol The symbol to define
     * @return true if the symbol was defined successfully, false if it already exists
     */
    public boolean define(Symbol symbol) {
<span class="nc" id="L63">        Objects.requireNonNull(symbol, &quot;symbol cannot be null&quot;);</span>
<span class="nc" id="L64">        String name = symbol.getName();</span>
        
<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (symbols.containsKey(name)) {</span>
<span class="nc" id="L67">            return false; // Symbol already exists in this scope</span>
        }
        
<span class="nc" id="L70">        symbols.put(name, symbol);</span>
<span class="nc" id="L71">        return true;</span>
    }

    /**
     * Looks up a symbol in this scope only (does not search parent scopes).
     * 
     * @param name The name of the symbol to look up
     * @return The symbol if found, empty otherwise
     */
    public Optional&lt;Symbol&gt; lookup(String name) {
<span class="nc" id="L81">        return Optional.ofNullable(symbols.get(name));</span>
    }

    /**
     * Gets all symbols defined in this scope.
     */
    public Map&lt;String, Symbol&gt; getSymbols() {
<span class="nc" id="L88">        return new HashMap&lt;&gt;(symbols);</span>
    }

    /**
     * Checks if this scope is the global/file scope.
     */
    public boolean isGlobalScope() {
<span class="nc bnc" id="L95" title="All 2 branches missed.">        return parent == null;</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L100">        return String.format(&quot;Scope{kind=%s, symbols=%d, parent=%s}&quot;, </span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">                           kind, symbols.size(), parent != null ? &quot;yes&quot; : &quot;no&quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>