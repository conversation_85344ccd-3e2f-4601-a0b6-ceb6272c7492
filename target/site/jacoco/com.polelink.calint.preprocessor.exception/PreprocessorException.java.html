<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PreprocessorException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">calint</a> &gt; <a href="index.source.html" class="el_package">com.polelink.calint.preprocessor.exception</a> &gt; <span class="el_source">PreprocessorException.java</span></div><h1>PreprocessorException.java</h1><pre class="source lang-java linenums">package com.polelink.calint.preprocessor.exception;

/**
 * Base exception class for CAPL preprocessor errors.
 * 
 * This exception is thrown when preprocessing operations fail,
 * such as macro expansion errors, include file processing errors,
 * or other preprocessing-related issues.
 */
public class PreprocessorException extends Exception {

    /**
     * Creates a new PreprocessorException with the specified message.
     * 
     * @param message The error message
     */
    public PreprocessorException(String message) {
<span class="nc" id="L18">        super(message);</span>
<span class="nc" id="L19">    }</span>

    /**
     * Creates a new PreprocessorException with the specified message and cause.
     * 
     * @param message The error message
     * @param cause The underlying cause of the exception
     */
    public PreprocessorException(String message, Throwable cause) {
<span class="nc" id="L28">        super(message, cause);</span>
<span class="nc" id="L29">    }</span>

    /**
     * Creates a new PreprocessorException with the specified cause.
     * 
     * @param cause The underlying cause of the exception
     */
    public PreprocessorException(Throwable cause) {
<span class="nc" id="L37">        super(cause);</span>
<span class="nc" id="L38">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.11.202310140853</span></div></body></html>