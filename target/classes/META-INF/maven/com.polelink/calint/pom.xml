<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.polelink</groupId>
	<artifactId>calint</artifactId>
	<version>0.1.0-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>calint</name>
	<description>A static analysis tool for CAPL scripts.</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>17</java.version>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>
		<antlr4.version>4.13.1</antlr4.version> <!-- Or the specific version you
		plan to use -->
		<junit.jupiter.version>5.10.0</junit.jupiter.version> <!-- Or the
		specific version you plan to use -->
		<mockito.version>5.11.0</mockito.version> <!-- Or a recent stable version -->
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>

		<jackson.version>2.15.2</jackson.version> <!-- Or a more recent stable
		version -->
		<slf4j.version>2.0.17</slf4j.version>
		<logback.version>1.5.18</logback.version>
		<reflections.version>0.10.2</reflections.version> <!-- For rule discovery -->
		<jacoco.version>0.8.11</jacoco.version> <!-- JaCoCo for test coverage -->

		<!-- LSP related versions -->
		<lsp4j.version>0.21.1</lsp4j.version> <!-- Eclipse LSP4J for Language
		Server Protocol -->
		<java-websocket.version>1.5.4</java-websocket.version> <!-- WebSocket
		support for remote LSP -->
		
		<version.capl-grammar>0.1.0</version.capl-grammar>
	</properties>

	<dependencies>
		<!-- ANTLR v4 Runtime -->
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4-runtime</artifactId>
			<version>${antlr4.version}</version>
		</dependency>

		<!-- JUnit 5 for testing -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>${junit.jupiter.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>${junit.jupiter.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-params</artifactId>
			<version>${junit.jupiter.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- Mockito for mocking -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>${mockito.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-junit-jupiter</artifactId>
			<version>${mockito.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- Apache Commons CLI -->
		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
			<version>1.9.0</version>
		</dependency>

		<!-- Jackson for JSON processing -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jdk8</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-parameter-names</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-yaml</artifactId>
			<version>${jackson.version}</version>
		</dependency>

		<!-- SLF4J API -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>

		<!-- Logback Classic (includes logback-core and slf4j-api) -->
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>${logback.version}</version>
		</dependency>

		<!-- Reflections for classpath scanning (rule discovery) -->
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>${reflections.version}</version>
		</dependency>

		<!-- LSP4J Dependencies for Language Server Protocol -->
		<!-- LSP4J Core - Main LSP implementation -->
		<dependency>
			<groupId>org.eclipse.lsp4j</groupId>
			<artifactId>org.eclipse.lsp4j</artifactId>
			<version>${lsp4j.version}</version>
		</dependency>

		<!-- LSP4J JSON-RPC - Communication protocol -->
		<dependency>
			<groupId>org.eclipse.lsp4j</groupId>
			<artifactId>org.eclipse.lsp4j.jsonrpc</artifactId>
			<version>${lsp4j.version}</version>
		</dependency>

		<!-- Java-WebSocket for WebSocket transport (remote LSP mode) -->
		<dependency>
			<groupId>org.java-websocket</groupId>
			<artifactId>Java-WebSocket</artifactId>
			<version>${java-websocket.version}</version>
		</dependency>

		<dependency>
			<groupId>com.polelink</groupId>
			<artifactId>capl-grammar</artifactId>
			<version>${version.capl-grammar}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- Java Compiler Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>


			<!-- Maven Shade Plugin to build an executable uber JAR -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>3.5.0</version> <!-- Or a more recent version -->
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>com.polelink.calint.cli.CalintCli</mainClass>
								</transformer>
								<!-- Merge SPI service files to avoid service
								discovery issues -->
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
								<!-- Merge Spring handlers and schemas -->
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.handlers</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.schemas</resource>
								</transformer>
							</transformers>
							<!-- Suppress duplicate resource warnings by
							excluding irrelevant files -->
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<!-- Exclude Maven metadata -->
										<exclude>META-INF/maven/**</exclude>
										<!-- Exclude license and notice files -->
										<exclude>META-INF/LICENSE*</exclude>
										<exclude>META-INF/NOTICE*</exclude>
										<exclude>META-INF/DEPENDENCIES</exclude>
										<exclude>about.html</exclude>
										<!-- Exclude manifest files -->
										<exclude>META-INF/MANIFEST.MF</exclude>
										<!-- Exclude module info -->
										<exclude>
											META-INF/versions/*/module-info.class</exclude>
										<exclude>module-info.class</exclude>
										<!-- Exclude signature files to avoid
										JNI errors -->
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
										<!-- Exclude Jackson service files
										(handled by ServicesResourceTransformer) -->
										<exclude>
											META-INF/services/com.fasterxml.jackson.databind.Module</exclude>
										<exclude>
											META-INF/services/com.fasterxml.jackson.core.ObjectCodec</exclude>
										<exclude>
											META-INF/services/com.fasterxml.jackson.core.JsonFactory</exclude>
									</excludes>
								</filter>
							</filters>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<!-- Minimize jar by removing unused classes -->
							<minimizeJar>false</minimizeJar>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Surefire Plugin for Test Execution -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.2.5</version>
				<configuration>
					<!-- Show detailed test execution information -->
					<printSummary>true</printSummary>
					<useFile>false</useFile>
					<includes>
						<include>**/*Test.java</include>
					</includes>
					<!-- Set test timeout to prevent hanging -->
					<forkedProcessTimeoutInSeconds>60</forkedProcessTimeoutInSeconds>
					<!-- Reduce JVM exit timeout to avoid long waits -->
					<forkedProcessExitTimeoutInSeconds>10</forkedProcessExitTimeoutInSeconds>
					<!-- Reduce test output verbosity -->
					<reportFormat>brief</reportFormat>
					<consoleOutputReporter>
						<disable>true</disable>
					</consoleOutputReporter>
					<!-- Reduce stack trace verbosity -->
					<trimStackTrace>true</trimStackTrace>
					<!-- Disable detailed test progress reporting -->
					<statelessTestsetReporter
						implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
						<disable>true</disable>
						<version>3.0</version>
						<usePhrasedFileName>false</usePhrasedFileName>
						<usePhrasedTestSuiteClassName>false</usePhrasedTestSuiteClassName>
						<usePhrasedTestCaseClassName>false</usePhrasedTestCaseClassName>
						<usePhrasedTestCaseMethodName>false</usePhrasedTestCaseMethodName>
					</statelessTestsetReporter>
				</configuration>
			</plugin>

			<!-- JaCoCo Plugin for Test Coverage -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco.version}</version>
				<executions>
					<!-- Prepare agent for test execution -->
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<!-- Generate coverage report after tests -->
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<!-- Exclude generated ANTLR classes from coverage -->
					<excludes>
						<exclude>com/polelink/calint/capl/**</exclude>
						<exclude>**/generated-sources/**</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>
